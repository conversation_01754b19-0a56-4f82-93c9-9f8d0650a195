// NOTE This file is auto-generated by Contentlayer

import type { <PERSON><PERSON>, <PERSON><PERSON>, Image<PERSON>ieldD<PERSON>, IsoDateTimeString } from 'contentlayer/core'
import * as Local from 'contentlayer/source-files'

export { isType } from 'contentlayer/client'

export type { Markdown, MDX, ImageFieldData, IsoDateTimeString }

/** Document types */
export type Pillar = {
  /** File path relative to `contentDirPath` */
  _id: string
  _raw: Local.RawDocumentData
  type: 'Pillar'
  /** The title of the page */
  title: string
  /** The description of the page */
  description: string
  /** The slug for the page URL */
  slug: string
  /** The parent cluster ID */
  cluster: string
  /** The template to use for rendering */
  template: string
  /** The priority level */
  priority?: 'High' | 'Medium' | 'Low' | undefined
  /** Last updated date */
  lastUpdated?: string | undefined
  /** MDX file body */
  body: MDX
  url: string
  slugFromPath: string
}  

/** Nested types */
  

/** Helper types */

export type AllTypes = DocumentTypes | NestedTypes
export type AllTypeNames = DocumentTypeNames | NestedTypeNames

export type DocumentTypes = Pillar
export type DocumentTypeNames = 'Pillar'

export type NestedTypes = never
export type NestedTypeNames = never

export type DataExports = {
  allDocuments: DocumentTypes[]
  allPillars: Pillar[]
}


export interface ContentlayerGenTypes {
  documentTypes: DocumentTypes
  documentTypeMap: DocumentTypeMap
  documentTypeNames: DocumentTypeNames
  nestedTypes: NestedTypes
  nestedTypeMap: NestedTypeMap
  nestedTypeNames: NestedTypeNames
  allTypeNames: AllTypeNames
  dataExports: DataExports
}

declare global {
  interface ContentlayerGen extends ContentlayerGenTypes {}
}

export type DocumentTypeMap = {
  Pillar: Pillar
}

export type NestedTypeMap = {

}

 