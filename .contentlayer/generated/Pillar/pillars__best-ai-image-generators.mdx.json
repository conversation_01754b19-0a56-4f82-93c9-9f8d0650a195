{"title": "Best AI Image & Texture Generators (2025) — Midjourney vs Stable Diffusion, Ideogram & DALL-E", "description": "Professional comparison of Midjourney, Stable Diffusion, Ideogram, and DALL-E for game developers, digital artists, and marketers. Find your perfect AI image generator.", "slug": "best-ai-image-generators-2025-midjourney-vs-stable-diffusion-ideogram-dalle", "cluster": "image", "template": "pillar", "priority": "Medium", "lastUpdated": "2025-07-22", "body": {"raw": "\nimport PillarLayout from \"@/templates/PillarLayout\"\nimport QuizCta from \"@/components/QuizCta\"\n\n<PillarLayout quizSlug=\"image\">\n\n<QuizCta task=\"image\" />\n\n## The New Creative Frontier: Beyond Novelty to Professional Integration\n\nThe era of AI image generation has moved beyond novelty and into a phase of serious professional integration. For game developers, digital artists, and marketers, these tools are no longer experimental toys but powerful assets capable of accelerating workflows, inspiring new concepts, and producing commercial-grade visuals.\n\nThe market has matured into a competitive landscape where the leading platforms—**Midjourney, Stable Diffusion, Ideogram, and DALL-E**—each champion a distinct approach to creation. This analysis reveals that there is no single \"best\" generator. The optimal choice balances **Aesthetic Quality, Technical Control, Typographic Fidelity, and Commercial Viability**.\n\n---\n\n## The Gauntlet: A Unified Prompt Showdown\n\nTo test the limits of each model, we used a single, complex prompt designed to evaluate photorealism, object detail, lighting, depth of field, and in-image text generation.\n\n**The Prompt:** *\"Photorealistic product shot for a marketing campaign. A sleek, matte black bottle of high-end perfume named '<PERSON>therea' sits on a wet, reflective marble surface. In the background, out of focus, are glowing neon orchids. The words 'Etherea: The Scent of Tomorrow' are elegantly displayed in a modern sans-serif font at the bottom.\"*\n\n### The Results: Side-by-Side Visual Analysis\n\n**Midjourney v6:** Delivers unparalleled photorealism. The lighting on the wet marble, the texture of the matte bottle, and the soft bokeh of the neon orchids are exceptionally cinematic and \"art-directed\". However, it struggles with the text, rendering a stylized but illegible version of the brand name and tagline.\n\n**Stable Diffusion XL Lightning:** Produces a high-quality image with remarkable speed, a testament to its efficient architecture. The composition is strong, but the fine details and lighting nuances lack the hyper-realistic polish of Midjourney. It represents a powerful balance of speed and quality, ideal for rapid iteration.\n\n**Ideogram:** Excels where others fail. The text \"Etherea: The Scent of Tomorrow\" is rendered with near-perfect clarity and elegant composition, demonstrating its core strength. The surrounding image is competent but less photorealistic than Midjourney or DALL-E, appearing more like a high-quality digital illustration than a photograph.\n\n**DALL-E 4 (via GPT-4o):** Shows superior prompt comprehension. It successfully interprets and renders every element of the prompt correctly—the matte bottle, wet surface, neon bokeh, and the text. The integration with ChatGPT allows for this nuanced understanding. While the text is legible, Ideogram's is more typographically refined.\n\n---\n\n## Prompt Showdown Scorecard\n\n| Platform | Photorealism (1-5) | Prompt Adherence (1-5) | Typography (1-5) | Overall Aesthetic (1-5) | Verdict |\n|----------|---------------------|-------------------------|-------------------|-------------------------|---------|\n| **Midjourney v6** | 5 | 4 | 2 | 5 | The Artist: Unmatched beauty, but can't write. |\n| **SDXL Lightning** | 4 | 4 | 2 | 4 | The Engineer: Fast and capable, a workhorse. |\n| **Ideogram** | 3 | 4 | 5 | 4 | The Typographer: Flawless text, good-enough image. |\n| **DALL-E 4** | 4 | 5 | 4 | 4 | The Co-Creator: Understands everything, a true generalist. |\n\n<div style={{textAlign:'right',fontSize:'0.9rem'}}><a href=\"/export/image-scorecard.csv\">Export to Sheets →</a></div>\n\n---\n\n## The Business Imperative: Cost vs. Commercial Rights\n\nChoosing a platform involves more than just creative output; it requires a careful analysis of cost and legal considerations.\n\n### Economic Analysis: The Cost of 1,000 Images\n\nThe cost per image varies dramatically depending on the platform's pricing model—subscription, credits, or pay-per-image API calls.\n\n| Platform | Recommended Plan/Method | Total Cost for 1,000 Images | Effective Cost-per-Image | Key Considerations |\n|----------|-------------------------|------------------------------|--------------------------|-------------------|\n| **Midjourney** | Standard Plan ($30/mo) | ~$30 | ~$0.03 | Subscription includes ~900 fast generations. |\n| **SDXL Lightning** | Replicate API | ~$1.40 | ~$0.0014 | Requires technical setup; API pricing varies by provider. |\n| **Ideogram** | Plus Plan ($16/mo, billed yearly) | ~$16 | ~$0.016 | Includes 1,000 priority credits/month. |\n| **DALL-E 4 (API)** | OpenAI API (DALL-E 3) | ~$40 | $0.04 | Pay-as-you-go; price is for standard quality. |\n\n### Legal Framework: Commercial Licensing\n\nThe ability to use AI-generated images commercially is complex. Under U.S. law, AI-generated images generally cannot be copyrighted, making the platform's Terms of Service paramount.\n\n| Platform | Ownership of Output | Commercial Use Allowed? | Key Restrictions & Revenue Caps | Private Generation? |\n|----------|---------------------|-------------------------|--------------------------------|-------------------|\n| **Midjourney** | You own assets you create. | Yes, with paid plans. | Businesses with >$1M gross annual revenue must use Pro/Mega plan. | Yes (Pro/Mega plans) |\n| **Stable Diffusion** | You own the output. | Yes, with license compliance. | Use is restricted from specific harmful applications. Enterprise license needed for companies >$1M revenue. | N/A (local/private) |\n| **Ideogram** | You are free to use images. | Yes, on all plans. | Free plan generations are public by default, posing a risk for proprietary work. | Yes (Plus/Pro plans) |\n| **DALL-E** | You own assets you create. | Yes, per OpenAI's terms. | Subject to OpenAI's Content Policy; no explicit revenue caps mentioned for basic use. | Yes (via API/ChatGPT) |\n\n---\n\n## Strategic Recommendations by Professional Persona\n\n### 🎮 **For the Game Developer**\nA hybrid approach is best. Use **Midjourney** for high-fidelity concept art and marketing visuals. For asset creation, **Stable Diffusion** is superior for generating tileable textures, large asset batches, and maintaining character consistency with tools like ControlNet.\n\n### 🎨 **For the Digital Artist / Illustrator**\n**Midjourney** is the primary tool for its unparalleled aesthetic control and painterly results. Use **DALL-E 4** for its conversational interface to rapidly brainstorm and overcome creative blocks.\n\n### 📈 **For the Marketer / Designer**\nStart with **Ideogram** for any asset requiring text, such as social media graphics or logos. Use **DALL-E 4** for quickly generating blog headers and A/B testing ad concepts. For high-stakes campaign hero images, Midjourney's Pro plan ensures both top-tier quality and privacy.\n\n---\n\n## Deep-dive profiles\n\n### Midjourney — _the aesthetic perfectionist_\n\n**Strengths.** Unmatched photorealism and artistic quality; intuitive Discord interface; strong community and prompt sharing.\n**Weaknesses.** Poor text rendering; requires Discord; limited fine-tuning control.\n*Perfect for: Concept art, marketing hero images, artistic illustrations.*\n\n### Stable Diffusion — _the technical powerhouse_\n\n**Strengths.** Open-source flexibility; ControlNet for precise control; can run locally; extensive model ecosystem.\n**Weaknesses.** Steep learning curve; requires technical setup; inconsistent quality without expertise.\n*Perfect for: Bulk generation, custom training, technical workflows.*\n\n### Ideogram — _the typography specialist_\n\n**Strengths.** Exceptional text rendering; clean, modern aesthetic; affordable pricing; good prompt adherence.\n**Weaknesses.** Less photorealistic than competitors; smaller community; newer platform.\n*Perfect for: Logos, social media graphics, text-heavy designs.*\n\n### DALL-E 4 — _the conversational creator_\n\n**Strengths.** Superior prompt understanding; ChatGPT integration; consistent quality; ethical safeguards.\n**Weaknesses.** More expensive than alternatives; less artistic flair than Midjourney; API-dependent.\n*Perfect for: Rapid ideation, conversational workflows, general-purpose generation.*\n\n---\n\n<QuizCta task=\"image\" />\n\n---\n\nexport const faqData = [\n  { q: \"What is the best AI for generating photorealistic images?\", a: \"Midjourney is widely regarded as the leader for photorealistic images due to its advanced aesthetic model and parameters like --style raw. However, DALL-E 4 and Stable Diffusion XL can also produce high-quality realistic images, especially with careful prompting.\" },\n  { q: \"Which AI image generator is best for creating logos with text?\", a: \"Ideogram is the specialist for generating images with accurate and well-composed text. Its models are specifically trained for typography, making it the top choice for logos, posters, and marketing materials with text. DALL-E 4 is also improving in this area.\" },\n  { q: \"Can I use images from Midjourney or DALL-E for commercial purposes?\", a: \"Yes, both platforms allow commercial use, but with important conditions. You own the assets you create. However, Midjourney requires a Pro or Mega plan for companies with over $1M in annual revenue. Furthermore, AI-generated images currently exist in a legal gray area and may not be eligible for copyright protection.\" },\n  { q: \"What is the cheapest way to generate a lot of AI images?\", a: \"Using an API for a speed-optimized model like Stable Diffusion XL Lightning is often the most cost-effective method for high-volume generation, with costs potentially under $0.002 per image. However, this requires technical setup. For subscription models, Ideogram's paid plans offer a large number of credits for a relatively low monthly cost.\" },\n  { q: \"What's the difference between Midjourney and Stable Diffusion?\", a: \"The main difference is control versus ease of use. Midjourney offers a curated, high-quality aesthetic experience through its Discord interface. Stable Diffusion is an open-source model that provides maximum control and customization (e.g., training your own models, using ControlNet) but has a much steeper learning curve and often requires a powerful local computer or API integration.\" },\n  { q: \"Which AI image generator is best for game development?\", a: \"A hybrid approach works best: Midjourney for concept art and hero images, Stable Diffusion for bulk asset generation and textures. Stable Diffusion's ControlNet feature is particularly valuable for maintaining consistency across character designs and creating tileable textures.\" }\n]\n\n</PillarLayout>\n", "code": "var Component=(()=>{var Gn=Object.create;var we=Object.defineProperty;var Xn=Object.getOwnPropertyDescriptor;var Qn=Object.getOwnPropertyNames;var Zn=Object.getPrototypeOf,Jn=Object.prototype.hasOwnProperty;var h=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),ei=(t,e)=>{for(var n in e)we(t,n,{get:e[n],enumerable:!0})},Zt=(t,e,n,i)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let o of Qn(e))!Jn.call(t,o)&&o!==n&&we(t,o,{get:()=>e[o],enumerable:!(i=Xn(e,o))||i.enumerable});return t};var Jt=(t,e,n)=>(n=t!=null?Gn(Zn(t)):{},Zt(e||!t||!t.__esModule?we(n,\"default\",{value:t,enumerable:!0}):n,t)),ti=t=>Zt(we({},\"__esModule\",{value:!0}),t);var de=h((Na,er)=>{er.exports=React});var tr=h(Ye=>{\"use strict\";(function(){\"use strict\";var t=de(),e=Symbol.for(\"react.element\"),n=Symbol.for(\"react.portal\"),i=Symbol.for(\"react.fragment\"),o=Symbol.for(\"react.strict_mode\"),a=Symbol.for(\"react.profiler\"),f=Symbol.for(\"react.provider\"),d=Symbol.for(\"react.context\"),c=Symbol.for(\"react.forward_ref\"),p=Symbol.for(\"react.suspense\"),x=Symbol.for(\"react.suspense_list\"),S=Symbol.for(\"react.memo\"),P=Symbol.for(\"react.lazy\"),U=Symbol.for(\"react.offscreen\"),se=Symbol.iterator,ge=\"@@iterator\";function I(r){if(r===null||typeof r!=\"object\")return null;var l=se&&r[se]||r[ge];return typeof l==\"function\"?l:null}var te=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function _(r){{for(var l=arguments.length,u=new Array(l>1?l-1:0),m=1;m<l;m++)u[m-1]=arguments[m];Ie(\"error\",r,u)}}function Ie(r,l,u){{var m=te.ReactDebugCurrentFrame,v=m.getStackAddendum();v!==\"\"&&(l+=\"%s\",u=u.concat([v]));var T=u.map(function(N){return String(N)});T.unshift(\"Warning: \"+l),Function.prototype.apply.call(console[r],console,T)}}var W=!1,M=!1,ie=!1,le=!1,z=!1,O;O=Symbol.for(\"react.module.reference\");function ye(r){return!!(typeof r==\"string\"||typeof r==\"function\"||r===i||r===a||z||r===o||r===p||r===x||le||r===U||W||M||ie||typeof r==\"object\"&&r!==null&&(r.$$typeof===P||r.$$typeof===S||r.$$typeof===f||r.$$typeof===d||r.$$typeof===c||r.$$typeof===O||r.getModuleId!==void 0))}function Ne(r,l,u){var m=r.displayName;if(m)return m;var v=l.displayName||l.name||\"\";return v!==\"\"?u+\"(\"+v+\")\":u}function R(r){return r.displayName||\"Context\"}function w(r){if(r==null)return null;if(typeof r.tag==\"number\"&&_(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof r==\"function\")return r.displayName||r.name||null;if(typeof r==\"string\")return r;switch(r){case i:return\"Fragment\";case n:return\"Portal\";case a:return\"Profiler\";case o:return\"StrictMode\";case p:return\"Suspense\";case x:return\"SuspenseList\"}if(typeof r==\"object\")switch(r.$$typeof){case d:var l=r;return R(l)+\".Consumer\";case f:var u=r;return R(u._context)+\".Provider\";case c:return Ne(r,r.render,\"ForwardRef\");case S:var m=r.displayName||null;return m!==null?m:w(r.type)||\"Memo\";case P:{var v=r,T=v._payload,N=v._init;try{return w(N(T))}catch{return null}}}return null}var J=Object.assign,re=0,fe,ve,oe,b,ne,ee,Ue;function Le(){}Le.__reactDisabledLog=!0;function xe(){{if(re===0){fe=console.log,ve=console.info,oe=console.warn,b=console.error,ne=console.group,ee=console.groupCollapsed,Ue=console.groupEnd;var r={configurable:!0,enumerable:!0,value:Le,writable:!0};Object.defineProperties(console,{info:r,log:r,warn:r,error:r,group:r,groupCollapsed:r,groupEnd:r})}re++}}function g(){{if(re--,re===0){var r={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:J({},r,{value:fe}),info:J({},r,{value:ve}),warn:J({},r,{value:oe}),error:J({},r,{value:b}),group:J({},r,{value:ne}),groupCollapsed:J({},r,{value:ee}),groupEnd:J({},r,{value:Ue})})}re<0&&_(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var C=te.ReactCurrentDispatcher,Te;function Pe(r,l,u){{if(Te===void 0)try{throw Error()}catch(v){var m=v.stack.trim().match(/\\n( *(at )?)/);Te=m&&m[1]||\"\"}return`\n`+Te+r}}var We=!1,Ee;{var kn=typeof WeakMap==\"function\"?WeakMap:Map;Ee=new kn}function Mt(r,l){if(!r||We)return\"\";{var u=Ee.get(r);if(u!==void 0)return u}var m;We=!0;var v=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var T;T=C.current,C.current=null,xe();try{if(l){var N=function(){throw Error()};if(Object.defineProperty(N.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(N,[])}catch(j){m=j}Reflect.construct(r,[],N)}else{try{N.call()}catch(j){m=j}r.call(N.prototype)}}else{try{throw Error()}catch(j){m=j}r()}}catch(j){if(j&&m&&typeof j.stack==\"string\"){for(var y=j.stack.split(`\n`),D=m.stack.split(`\n`),E=y.length-1,k=D.length-1;E>=1&&k>=0&&y[E]!==D[k];)k--;for(;E>=1&&k>=0;E--,k--)if(y[E]!==D[k]){if(E!==1||k!==1)do if(E--,k--,k<0||y[E]!==D[k]){var L=`\n`+y[E].replace(\" at new \",\" at \");return r.displayName&&L.includes(\"<anonymous>\")&&(L=L.replace(\"<anonymous>\",r.displayName)),typeof r==\"function\"&&Ee.set(r,L),L}while(E>=1&&k>=0);break}}}finally{We=!1,C.current=T,g(),Error.prepareStackTrace=v}var ue=r?r.displayName||r.name:\"\",ae=ue?Pe(ue):\"\";return typeof r==\"function\"&&Ee.set(r,ae),ae}function Rn(r,l,u){return Mt(r,!1)}function wn(r){var l=r.prototype;return!!(l&&l.isReactComponent)}function ke(r,l,u){if(r==null)return\"\";if(typeof r==\"function\")return Mt(r,wn(r));if(typeof r==\"string\")return Pe(r);switch(r){case p:return Pe(\"Suspense\");case x:return Pe(\"SuspenseList\")}if(typeof r==\"object\")switch(r.$$typeof){case c:return Rn(r.render);case S:return ke(r.type,l,u);case P:{var m=r,v=m._payload,T=m._init;try{return ke(T(v),l,u)}catch{}}}return\"\"}var pe=Object.prototype.hasOwnProperty,zt={},qt=te.ReactDebugCurrentFrame;function Re(r){if(r){var l=r._owner,u=ke(r.type,r._source,l?l.type:null);qt.setExtraStackFrame(u)}else qt.setExtraStackFrame(null)}function On(r,l,u,m,v){{var T=Function.call.bind(pe);for(var N in r)if(T(r,N)){var y=void 0;try{if(typeof r[N]!=\"function\"){var D=Error((m||\"React class\")+\": \"+u+\" type `\"+N+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof r[N]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw D.name=\"Invariant Violation\",D}y=r[N](l,N,m,u,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(E){y=E}y&&!(y instanceof Error)&&(Re(v),_(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",m||\"React class\",u,N,typeof y),Re(null)),y instanceof Error&&!(y.message in zt)&&(zt[y.message]=!0,Re(v),_(\"Failed %s type: %s\",u,y.message),Re(null))}}}var Dn=Array.isArray;function Me(r){return Dn(r)}function Sn(r){{var l=typeof Symbol==\"function\"&&Symbol.toStringTag,u=l&&r[Symbol.toStringTag]||r.constructor.name||\"Object\";return u}}function Cn(r){try{return Ft(r),!1}catch{return!0}}function Ft(r){return\"\"+r}function Vt(r){if(Cn(r))return _(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",Sn(r)),Ft(r)}var me=te.ReactCurrentOwner,jn={key:!0,ref:!0,__self:!0,__source:!0},$t,Yt,ze;ze={};function An(r){if(pe.call(r,\"ref\")){var l=Object.getOwnPropertyDescriptor(r,\"ref\").get;if(l&&l.isReactWarning)return!1}return r.ref!==void 0}function In(r){if(pe.call(r,\"key\")){var l=Object.getOwnPropertyDescriptor(r,\"key\").get;if(l&&l.isReactWarning)return!1}return r.key!==void 0}function Un(r,l){if(typeof r.ref==\"string\"&&me.current&&l&&me.current.stateNode!==l){var u=w(me.current.type);ze[u]||(_('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',w(me.current.type),r.ref),ze[u]=!0)}}function Ln(r,l){{var u=function(){$t||($t=!0,_(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",l))};u.isReactWarning=!0,Object.defineProperty(r,\"key\",{get:u,configurable:!0})}}function Wn(r,l){{var u=function(){Yt||(Yt=!0,_(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",l))};u.isReactWarning=!0,Object.defineProperty(r,\"ref\",{get:u,configurable:!0})}}var Mn=function(r,l,u,m,v,T,N){var y={$$typeof:e,type:r,key:l,ref:u,props:N,_owner:T};return y._store={},Object.defineProperty(y._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(y,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:m}),Object.defineProperty(y,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:v}),Object.freeze&&(Object.freeze(y.props),Object.freeze(y)),y};function zn(r,l,u,m,v){{var T,N={},y=null,D=null;u!==void 0&&(Vt(u),y=\"\"+u),In(l)&&(Vt(l.key),y=\"\"+l.key),An(l)&&(D=l.ref,Un(l,v));for(T in l)pe.call(l,T)&&!jn.hasOwnProperty(T)&&(N[T]=l[T]);if(r&&r.defaultProps){var E=r.defaultProps;for(T in E)N[T]===void 0&&(N[T]=E[T])}if(y||D){var k=typeof r==\"function\"?r.displayName||r.name||\"Unknown\":r;y&&Ln(N,k),D&&Wn(N,k)}return Mn(r,y,D,v,m,me.current,N)}}var qe=te.ReactCurrentOwner,Ht=te.ReactDebugCurrentFrame;function ce(r){if(r){var l=r._owner,u=ke(r.type,r._source,l?l.type:null);Ht.setExtraStackFrame(u)}else Ht.setExtraStackFrame(null)}var Fe;Fe=!1;function Ve(r){return typeof r==\"object\"&&r!==null&&r.$$typeof===e}function Bt(){{if(qe.current){var r=w(qe.current.type);if(r)return`\n\nCheck the render method of \\``+r+\"`.\"}return\"\"}}function qn(r){{if(r!==void 0){var l=r.fileName.replace(/^.*[\\\\\\/]/,\"\"),u=r.lineNumber;return`\n\nCheck your code at `+l+\":\"+u+\".\"}return\"\"}}var Kt={};function Fn(r){{var l=Bt();if(!l){var u=typeof r==\"string\"?r:r.displayName||r.name;u&&(l=`\n\nCheck the top-level render call using <`+u+\">.\")}return l}}function Gt(r,l){{if(!r._store||r._store.validated||r.key!=null)return;r._store.validated=!0;var u=Fn(l);if(Kt[u])return;Kt[u]=!0;var m=\"\";r&&r._owner&&r._owner!==qe.current&&(m=\" It was passed a child from \"+w(r._owner.type)+\".\"),ce(r),_('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',u,m),ce(null)}}function Xt(r,l){{if(typeof r!=\"object\")return;if(Me(r))for(var u=0;u<r.length;u++){var m=r[u];Ve(m)&&Gt(m,l)}else if(Ve(r))r._store&&(r._store.validated=!0);else if(r){var v=I(r);if(typeof v==\"function\"&&v!==r.entries)for(var T=v.call(r),N;!(N=T.next()).done;)Ve(N.value)&&Gt(N.value,l)}}}function Vn(r){{var l=r.type;if(l==null||typeof l==\"string\")return;var u;if(typeof l==\"function\")u=l.propTypes;else if(typeof l==\"object\"&&(l.$$typeof===c||l.$$typeof===S))u=l.propTypes;else return;if(u){var m=w(l);On(u,r.props,\"prop\",m,r)}else if(l.PropTypes!==void 0&&!Fe){Fe=!0;var v=w(l);_(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",v||\"Unknown\")}typeof l.getDefaultProps==\"function\"&&!l.getDefaultProps.isReactClassApproved&&_(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function $n(r){{for(var l=Object.keys(r.props),u=0;u<l.length;u++){var m=l[u];if(m!==\"children\"&&m!==\"key\"){ce(r),_(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",m),ce(null);break}}r.ref!==null&&(ce(r),_(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),ce(null))}}var Qt={};function Yn(r,l,u,m,v,T){{var N=ye(r);if(!N){var y=\"\";(r===void 0||typeof r==\"object\"&&r!==null&&Object.keys(r).length===0)&&(y+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var D=qn(v);D?y+=D:y+=Bt();var E;r===null?E=\"null\":Me(r)?E=\"array\":r!==void 0&&r.$$typeof===e?(E=\"<\"+(w(r.type)||\"Unknown\")+\" />\",y=\" Did you accidentally export a JSX literal instead of a component?\"):E=typeof r,_(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",E,y)}var k=zn(r,l,u,v,T);if(k==null)return k;if(N){var L=l.children;if(L!==void 0)if(m)if(Me(L)){for(var ue=0;ue<L.length;ue++)Xt(L[ue],r);Object.freeze&&Object.freeze(L)}else _(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else Xt(L,r)}if(pe.call(l,\"key\")){var ae=w(r),j=Object.keys(l).filter(function(Kn){return Kn!==\"key\"}),$e=j.length>0?\"{key: someKey, \"+j.join(\": ..., \")+\": ...}\":\"{key: someKey}\";if(!Qt[ae+$e]){var Bn=j.length>0?\"{\"+j.join(\": ..., \")+\": ...}\":\"{}\";_(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,$e,ae,Bn,ae),Qt[ae+$e]=!0}}return r===i?$n(k):Vn(k),k}}var Hn=Yn;Ye.Fragment=i,Ye.jsxDEV=Hn})()});var nr=h((xa,rr)=>{\"use strict\";rr.exports=tr()});var Oe=h(Be=>{\"use strict\";Be._=Be._interop_require_default=ri;function ri(t){return t&&t.__esModule?t:{default:t}}});var Ge=h(Ke=>{\"use strict\";Object.defineProperty(Ke,\"__esModule\",{value:!0});function ni(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}ni(Ke,{searchParamsToUrlQuery:function(){return ii},urlQueryToSearchParams:function(){return oi},assign:function(){return ai}});function ii(t){let e={};return t.forEach((n,i)=>{typeof e[i]>\"u\"?e[i]=n:Array.isArray(e[i])?e[i].push(n):e[i]=[e[i],n]}),e}function ir(t){return typeof t==\"string\"||typeof t==\"number\"&&!isNaN(t)||typeof t==\"boolean\"?String(t):\"\"}function oi(t){let e=new URLSearchParams;return Object.entries(t).forEach(n=>{let[i,o]=n;Array.isArray(o)?o.forEach(a=>e.append(i,ir(a))):e.set(i,ir(o))}),e}function ai(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return n.forEach(o=>{Array.from(o.keys()).forEach(a=>t.delete(a)),o.forEach((a,f)=>t.append(f,a))}),t}});var ar=h(Xe=>{\"use strict\";function or(t){if(typeof WeakMap!=\"function\")return null;var e=new WeakMap,n=new WeakMap;return(or=function(i){return i?n:e})(t)}Xe._=Xe._interop_require_wildcard=si;function si(t,e){if(!e&&t&&t.__esModule)return t;if(t===null||typeof t!=\"object\"&&typeof t!=\"function\")return{default:t};var n=or(e);if(n&&n.has(t))return n.get(t);var i={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in t)if(a!==\"default\"&&Object.prototype.hasOwnProperty.call(t,a)){var f=o?Object.getOwnPropertyDescriptor(t,a):null;f&&(f.get||f.set)?Object.defineProperty(i,a,f):i[a]=t[a]}return i.default=t,n&&n.set(t,i),i}});var Ze=h(Qe=>{\"use strict\";Object.defineProperty(Qe,\"__esModule\",{value:!0});function li(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}li(Qe,{formatUrl:function(){return sr},urlObjectKeys:function(){return lr},formatWithValidation:function(){return fi}});var ci=ar(),ui=ci._(Ge()),di=/https?|ftp|gopher|file/;function sr(t){let{auth:e,hostname:n}=t,i=t.protocol||\"\",o=t.pathname||\"\",a=t.hash||\"\",f=t.query||\"\",d=!1;e=e?encodeURIComponent(e).replace(/%3A/i,\":\")+\"@\":\"\",t.host?d=e+t.host:n&&(d=e+(~n.indexOf(\":\")?\"[\"+n+\"]\":n),t.port&&(d+=\":\"+t.port)),f&&typeof f==\"object\"&&(f=String(ui.urlQueryToSearchParams(f)));let c=t.search||f&&\"?\"+f||\"\";return i&&!i.endsWith(\":\")&&(i+=\":\"),t.slashes||(!i||di.test(i))&&d!==!1?(d=\"//\"+(d||\"\"),o&&o[0]!==\"/\"&&(o=\"/\"+o)):d||(d=\"\"),a&&a[0]!==\"#\"&&(a=\"#\"+a),c&&c[0]!==\"?\"&&(c=\"?\"+c),o=o.replace(/[?#]/g,encodeURIComponent),c=c.replace(\"#\",\"%23\"),\"\"+i+d+o+c+a}var lr=[\"auth\",\"hash\",\"host\",\"hostname\",\"href\",\"path\",\"pathname\",\"port\",\"protocol\",\"query\",\"search\",\"slashes\"];function fi(t){return t!==null&&typeof t==\"object\"&&Object.keys(t).forEach(e=>{lr.includes(e)||console.warn(\"Unknown key passed via urlObject into url.format: \"+e)}),sr(t)}});var cr=h(Je=>{\"use strict\";Object.defineProperty(Je,\"__esModule\",{value:!0});Object.defineProperty(Je,\"omit\",{enumerable:!0,get:function(){return pi}});function pi(t,e){let n={};return Object.keys(t).forEach(i=>{e.includes(i)||(n[i]=t[i])}),n}});var he=h(ot=>{\"use strict\";Object.defineProperty(ot,\"__esModule\",{value:!0});function mi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}mi(ot,{WEB_VITALS:function(){return hi},execOnce:function(){return bi},isAbsoluteUrl:function(){return gi},getLocationOrigin:function(){return ur},getURL:function(){return yi},getDisplayName:function(){return De},isResSent:function(){return dr},normalizeRepeatedSlashes:function(){return Ni},loadGetInitialProps:function(){return fr},SP:function(){return pr},ST:function(){return vi},DecodeError:function(){return et},NormalizeError:function(){return tt},PageNotFoundError:function(){return rt},MissingStaticPage:function(){return nt},MiddlewareNotFoundError:function(){return it},stringifyError:function(){return xi}});var hi=[\"CLS\",\"FCP\",\"FID\",\"INP\",\"LCP\",\"TTFB\"];function bi(t){let e=!1,n;return function(){for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return e||(e=!0,n=t(...o)),n}}var _i=/^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/,gi=t=>_i.test(t);function ur(){let{protocol:t,hostname:e,port:n}=window.location;return t+\"//\"+e+(n?\":\"+n:\"\")}function yi(){let{href:t}=window.location,e=ur();return t.substring(e.length)}function De(t){return typeof t==\"string\"?t:t.displayName||t.name||\"Unknown\"}function dr(t){return t.finished||t.headersSent}function Ni(t){let e=t.split(\"?\");return e[0].replace(/\\\\/g,\"/\").replace(/\\/\\/+/g,\"/\")+(e[1]?\"?\"+e.slice(1).join(\"?\"):\"\")}async function fr(t,e){var n;if((n=t.prototype)!=null&&n.getInitialProps){let a='\"'+De(t)+'.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';throw new Error(a)}let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await fr(e.Component,e.ctx)}:{};let o=await t.getInitialProps(e);if(i&&dr(i))return o;if(!o){let a='\"'+De(t)+'.getInitialProps()\" should resolve to an object. But found \"'+o+'\" instead.';throw new Error(a)}return Object.keys(o).length===0&&!e.ctx&&console.warn(\"\"+De(t)+\" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\"),o}var pr=typeof performance<\"u\",vi=pr&&[\"mark\",\"measure\",\"getEntriesByName\"].every(t=>typeof performance[t]==\"function\"),et=class extends Error{},tt=class extends Error{},rt=class extends Error{constructor(e){super(),this.code=\"ENOENT\",this.name=\"PageNotFoundError\",this.message=\"Cannot find module for page: \"+e}},nt=class extends Error{constructor(e,n){super(),this.message=\"Failed to load static file for page: \"+e+\" \"+n}},it=class extends Error{constructor(){super(),this.code=\"ENOENT\",this.message=\"Cannot find the middleware module\"}};function xi(t){return JSON.stringify({message:t.message,stack:t.stack})}});var st=h(at=>{\"use strict\";Object.defineProperty(at,\"__esModule\",{value:!0});Object.defineProperty(at,\"removeTrailingSlash\",{enumerable:!0,get:function(){return Ti}});function Ti(t){return t.replace(/\\/$/,\"\")||\"/\"}});var Se=h(lt=>{\"use strict\";Object.defineProperty(lt,\"__esModule\",{value:!0});Object.defineProperty(lt,\"parsePath\",{enumerable:!0,get:function(){return Pi}});function Pi(t){let e=t.indexOf(\"#\"),n=t.indexOf(\"?\"),i=n>-1&&(e<0||n<e);return i||e>-1?{pathname:t.substring(0,i?n:e),query:i?t.substring(n,e>-1?e:void 0):\"\",hash:e>-1?t.slice(e):\"\"}:{pathname:t,query:\"\",hash:\"\"}}});var be=h((q,hr)=>{\"use strict\";Object.defineProperty(q,\"__esModule\",{value:!0});Object.defineProperty(q,\"normalizePathTrailingSlash\",{enumerable:!0,get:function(){return ki}});var mr=st(),Ei=Se(),ki=t=>{if(!t.startsWith(\"/\")||process.env.__NEXT_MANUAL_TRAILING_SLASH)return t;let{pathname:e,query:n,hash:i}=(0,Ei.parsePath)(t);return process.env.__NEXT_TRAILING_SLASH?/\\.[^/]+\\/?$/.test(e)?\"\"+(0,mr.removeTrailingSlash)(e)+n+i:e.endsWith(\"/\")?\"\"+e+n+i:e+\"/\"+n+i:\"\"+(0,mr.removeTrailingSlash)(e)+n+i};(typeof q.default==\"function\"||typeof q.default==\"object\"&&q.default!==null)&&typeof q.default.__esModule>\"u\"&&(Object.defineProperty(q.default,\"__esModule\",{value:!0}),Object.assign(q.default,q),hr.exports=q.default)});var ut=h(ct=>{\"use strict\";Object.defineProperty(ct,\"__esModule\",{value:!0});Object.defineProperty(ct,\"pathHasPrefix\",{enumerable:!0,get:function(){return wi}});var Ri=Se();function wi(t,e){if(typeof t!=\"string\")return!1;let{pathname:n}=(0,Ri.parsePath)(t);return n===e||n.startsWith(e+\"/\")}});var _r=h((F,br)=>{\"use strict\";Object.defineProperty(F,\"__esModule\",{value:!0});Object.defineProperty(F,\"hasBasePath\",{enumerable:!0,get:function(){return Si}});var Oi=ut(),Di=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Si(t){return(0,Oi.pathHasPrefix)(t,Di)}(typeof F.default==\"function\"||typeof F.default==\"object\"&&F.default!==null)&&typeof F.default.__esModule>\"u\"&&(Object.defineProperty(F.default,\"__esModule\",{value:!0}),Object.assign(F.default,F),br.exports=F.default)});var ft=h(dt=>{\"use strict\";Object.defineProperty(dt,\"__esModule\",{value:!0});Object.defineProperty(dt,\"isLocalURL\",{enumerable:!0,get:function(){return ji}});var gr=he(),Ci=_r();function ji(t){if(!(0,gr.isAbsoluteUrl)(t))return!0;try{let e=(0,gr.getLocationOrigin)(),n=new URL(t,e);return n.origin===e&&(0,Ci.hasBasePath)(n.pathname)}catch{return!1}}});var yr=h(mt=>{\"use strict\";Object.defineProperty(mt,\"__esModule\",{value:!0});Object.defineProperty(mt,\"getSortedRoutes\",{enumerable:!0,get:function(){return Ai}});var pt=class t{insert(e){this._insert(e.split(\"/\").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){e===void 0&&(e=\"/\");let n=[...this.children.keys()].sort();this.slugName!==null&&n.splice(n.indexOf(\"[]\"),1),this.restSlugName!==null&&n.splice(n.indexOf(\"[...]\"),1),this.optionalRestSlugName!==null&&n.splice(n.indexOf(\"[[...]]\"),1);let i=n.map(o=>this.children.get(o)._smoosh(\"\"+e+o+\"/\")).reduce((o,a)=>[...o,...a],[]);if(this.slugName!==null&&i.push(...this.children.get(\"[]\")._smoosh(e+\"[\"+this.slugName+\"]/\")),!this.placeholder){let o=e===\"/\"?\"/\":e.slice(0,-1);if(this.optionalRestSlugName!=null)throw new Error('You cannot define a route with the same specificity as a optional catch-all route (\"'+o+'\" and \"'+o+\"[[...\"+this.optionalRestSlugName+']]\").');i.unshift(o)}return this.restSlugName!==null&&i.push(...this.children.get(\"[...]\")._smoosh(e+\"[...\"+this.restSlugName+\"]/\")),this.optionalRestSlugName!==null&&i.push(...this.children.get(\"[[...]]\")._smoosh(e+\"[[...\"+this.optionalRestSlugName+\"]]/\")),i}_insert(e,n,i){if(e.length===0){this.placeholder=!1;return}if(i)throw new Error(\"Catch-all must be the last part of the URL.\");let o=e[0];if(o.startsWith(\"[\")&&o.endsWith(\"]\")){let d=function(c,p){if(c!==null&&c!==p)throw new Error(\"You cannot use different slug names for the same dynamic path ('\"+c+\"' !== '\"+p+\"').\");n.forEach(x=>{if(x===p)throw new Error('You cannot have the same slug name \"'+p+'\" repeat within a single dynamic path');if(x.replace(/\\W/g,\"\")===o.replace(/\\W/g,\"\"))throw new Error('You cannot have the slug names \"'+x+'\" and \"'+p+'\" differ only by non-word symbols within a single dynamic path')}),n.push(p)},a=o.slice(1,-1),f=!1;if(a.startsWith(\"[\")&&a.endsWith(\"]\")&&(a=a.slice(1,-1),f=!0),a.startsWith(\"...\")&&(a=a.substring(3),i=!0),a.startsWith(\"[\")||a.endsWith(\"]\"))throw new Error(\"Segment names may not start or end with extra brackets ('\"+a+\"').\");if(a.startsWith(\".\"))throw new Error(\"Segment names may not start with erroneous periods ('\"+a+\"').\");if(i)if(f){if(this.restSlugName!=null)throw new Error('You cannot use both an required and optional catch-all route at the same level (\"[...'+this.restSlugName+']\" and \"'+e[0]+'\" ).');d(this.optionalRestSlugName,a),this.optionalRestSlugName=a,o=\"[[...]]\"}else{if(this.optionalRestSlugName!=null)throw new Error('You cannot use both an optional and required catch-all route at the same level (\"[[...'+this.optionalRestSlugName+']]\" and \"'+e[0]+'\").');d(this.restSlugName,a),this.restSlugName=a,o=\"[...]\"}else{if(f)throw new Error('Optional route parameters are not yet supported (\"'+e[0]+'\").');d(this.slugName,a),this.slugName=a,o=\"[]\"}}this.children.has(o)||this.children.set(o,new t),this.children.get(o)._insert(e.slice(1),n,i)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}};function Ai(t){let e=new pt;return t.forEach(n=>e.insert(n)),e.smoosh()}});var Nr=h(ht=>{\"use strict\";Object.defineProperty(ht,\"__esModule\",{value:!0});Object.defineProperty(ht,\"isDynamicRoute\",{enumerable:!0,get:function(){return Ui}});var Ii=/\\/\\[[^/]+?\\](?=\\/|$)/;function Ui(t){return Ii.test(t)}});var vr=h(bt=>{\"use strict\";Object.defineProperty(bt,\"__esModule\",{value:!0});function Li(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Li(bt,{getSortedRoutes:function(){return Wi.getSortedRoutes},isDynamicRoute:function(){return Mi.isDynamicRoute}});var Wi=yr(),Mi=Nr()});var xr=h(_t=>{\"use strict\";Object.defineProperty(_t,\"__esModule\",{value:!0});Object.defineProperty(_t,\"getRouteMatcher\",{enumerable:!0,get:function(){return qi}});var zi=he();function qi(t){let{re:e,groups:n}=t;return i=>{let o=e.exec(i);if(!o)return!1;let a=d=>{try{return decodeURIComponent(d)}catch{throw new zi.DecodeError(\"failed to decode param\")}},f={};return Object.keys(n).forEach(d=>{let c=n[d],p=o[c.pos];p!==void 0&&(f[d]=~p.indexOf(\"/\")?p.split(\"/\").map(x=>a(x)):c.repeat?[a(p)]:a(p))}),f}}});var Tr=h(gt=>{\"use strict\";Object.defineProperty(gt,\"__esModule\",{value:!0});Object.defineProperty(gt,\"ensureLeadingSlash\",{enumerable:!0,get:function(){return Fi}});function Fi(t){return t.startsWith(\"/\")?t:\"/\"+t}});var Pr=h(yt=>{\"use strict\";Object.defineProperty(yt,\"__esModule\",{value:!0});Object.defineProperty(yt,\"isGroupSegment\",{enumerable:!0,get:function(){return Vi}});function Vi(t){return t[0]===\"(\"&&t.endsWith(\")\")}});var Er=h(Nt=>{\"use strict\";Object.defineProperty(Nt,\"__esModule\",{value:!0});function $i(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}$i(Nt,{normalizeAppPath:function(){return Bi},normalizeRscPath:function(){return Ki}});var Yi=Tr(),Hi=Pr();function Bi(t){return(0,Yi.ensureLeadingSlash)(t.split(\"/\").reduce((e,n,i,o)=>!n||(0,Hi.isGroupSegment)(n)||n[0]===\"@\"||(n===\"page\"||n===\"route\")&&i===o.length-1?e:e+\"/\"+n,\"\"))}function Ki(t,e){return e?t.replace(/\\.rsc($|\\?)/,\"$1\"):t}});var kr=h(xt=>{\"use strict\";Object.defineProperty(xt,\"__esModule\",{value:!0});function Gi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Gi(xt,{INTERCEPTION_ROUTE_MARKERS:function(){return vt},isInterceptionRouteAppPath:function(){return Qi},extractInterceptionRouteInformation:function(){return Zi}});var Xi=Er(),vt=[\"(..)(..)\",\"(.)\",\"(..)\",\"(...)\"];function Qi(t){return t.split(\"/\").find(e=>vt.find(n=>e.startsWith(n)))!==void 0}function Zi(t){let e,n,i;for(let o of t.split(\"/\"))if(n=vt.find(a=>o.startsWith(a)),n){[e,i]=t.split(n,2);break}if(!e||!n||!i)throw new Error(`Invalid interception route: ${t}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(e=(0,Xi.normalizeAppPath)(e),n){case\"(.)\":e===\"/\"?i=`/${i}`:i=e+\"/\"+i;break;case\"(..)\":if(e===\"/\")throw new Error(`Invalid interception route: ${t}. Cannot use (..) marker at the root level, use (.) instead.`);i=e.split(\"/\").slice(0,-1).concat(i).join(\"/\");break;case\"(...)\":i=\"/\"+i;break;case\"(..)(..)\":let o=e.split(\"/\");if(o.length<=2)throw new Error(`Invalid interception route: ${t}. Cannot use (..)(..) marker at the root level or one level up.`);i=o.slice(0,-2).concat(i).join(\"/\");break;default:throw new Error(\"Invariant: unexpected marker\")}return{interceptingRoute:e,interceptedRoute:i}}});var Rr=h(Tt=>{\"use strict\";Object.defineProperty(Tt,\"__esModule\",{value:!0});Object.defineProperty(Tt,\"escapeStringRegexp\",{enumerable:!0,get:function(){return to}});var Ji=/[|\\\\{}()[\\]^$+*?.-]/,eo=/[|\\\\{}()[\\]^$+*?.-]/g;function to(t){return Ji.test(t)?t.replace(eo,\"\\\\$&\"):t}});var Ar=h(kt=>{\"use strict\";Object.defineProperty(kt,\"__esModule\",{value:!0});function ro(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}ro(kt,{getRouteRegex:function(){return Cr},getNamedRouteRegex:function(){return ao},getNamedMiddlewareRegex:function(){return so}});var Or=kr(),Pt=Rr(),Dr=st(),no=\"nxtP\",io=\"nxtI\";function Et(t){let e=t.startsWith(\"[\")&&t.endsWith(\"]\");e&&(t=t.slice(1,-1));let n=t.startsWith(\"...\");return n&&(t=t.slice(3)),{key:t,repeat:n,optional:e}}function Sr(t){let e=(0,Dr.removeTrailingSlash)(t).slice(1).split(\"/\"),n={},i=1;return{parameterizedRoute:e.map(o=>{let a=Or.INTERCEPTION_ROUTE_MARKERS.find(d=>o.startsWith(d)),f=o.match(/\\[((?:\\[.*\\])|.+)\\]/);if(a&&f){let{key:d,optional:c,repeat:p}=Et(f[1]);return n[d]={pos:i++,repeat:p,optional:c},\"/\"+(0,Pt.escapeStringRegexp)(a)+\"([^/]+?)\"}else if(f){let{key:d,repeat:c,optional:p}=Et(f[1]);return n[d]={pos:i++,repeat:c,optional:p},c?p?\"(?:/(.+?))?\":\"/(.+?)\":\"/([^/]+?)\"}else return\"/\"+(0,Pt.escapeStringRegexp)(o)}).join(\"\"),groups:n}}function Cr(t){let{parameterizedRoute:e,groups:n}=Sr(t);return{re:new RegExp(\"^\"+e+\"(?:/)?$\"),groups:n}}function oo(){let t=0;return()=>{let e=\"\",n=++t;for(;n>0;)e+=String.fromCharCode(97+(n-1)%26),n=Math.floor((n-1)/26);return e}}function wr(t){let{getSafeRouteKey:e,segment:n,routeKeys:i,keyPrefix:o}=t,{key:a,optional:f,repeat:d}=Et(n),c=a.replace(/\\W/g,\"\");o&&(c=\"\"+o+c);let p=!1;return(c.length===0||c.length>30)&&(p=!0),isNaN(parseInt(c.slice(0,1)))||(p=!0),p&&(c=e()),o?i[c]=\"\"+o+a:i[c]=\"\"+a,d?f?\"(?:/(?<\"+c+\">.+?))?\":\"/(?<\"+c+\">.+?)\":\"/(?<\"+c+\">[^/]+?)\"}function jr(t,e){let n=(0,Dr.removeTrailingSlash)(t).slice(1).split(\"/\"),i=oo(),o={};return{namedParameterizedRoute:n.map(a=>{let f=Or.INTERCEPTION_ROUTE_MARKERS.some(c=>a.startsWith(c)),d=a.match(/\\[((?:\\[.*\\])|.+)\\]/);return f&&d?wr({getSafeRouteKey:i,segment:d[1],routeKeys:o,keyPrefix:e?io:void 0}):d?wr({getSafeRouteKey:i,segment:d[1],routeKeys:o,keyPrefix:e?no:void 0}):\"/\"+(0,Pt.escapeStringRegexp)(a)}).join(\"\"),routeKeys:o}}function ao(t,e){let n=jr(t,e);return{...Cr(t),namedRegex:\"^\"+n.namedParameterizedRoute+\"(?:/)?$\",routeKeys:n.routeKeys}}function so(t,e){let{parameterizedRoute:n}=Sr(t),{catchAll:i=!0}=e;if(n===\"/\")return{namedRegex:\"^/\"+(i?\".*\":\"\")+\"$\"};let{namedParameterizedRoute:o}=jr(t,!1),a=i?\"(?:(/.*)?)\":\"\";return{namedRegex:\"^\"+o+a+\"$\"}}});var Ir=h(Rt=>{\"use strict\";Object.defineProperty(Rt,\"__esModule\",{value:!0});Object.defineProperty(Rt,\"interpolateAs\",{enumerable:!0,get:function(){return uo}});var lo=xr(),co=Ar();function uo(t,e,n){let i=\"\",o=(0,co.getRouteRegex)(t),a=o.groups,f=(e!==t?(0,lo.getRouteMatcher)(o)(e):\"\")||n;i=t;let d=Object.keys(a);return d.every(c=>{let p=f[c]||\"\",{repeat:x,optional:S}=a[c],P=\"[\"+(x?\"...\":\"\")+c+\"]\";return S&&(P=(p?\"\":\"/\")+\"[\"+P+\"]\"),x&&!Array.isArray(p)&&(p=[p]),(S||c in f)&&(i=i.replace(P,x?p.map(U=>encodeURIComponent(U)).join(\"/\"):encodeURIComponent(p))||\"/\")})||(i=\"\"),{params:d,result:i}}});var Wr=h((V,Lr)=>{\"use strict\";Object.defineProperty(V,\"__esModule\",{value:!0});Object.defineProperty(V,\"resolveHref\",{enumerable:!0,get:function(){return yo}});var fo=Ge(),Ur=Ze(),po=cr(),mo=he(),ho=be(),bo=ft(),_o=vr(),go=Ir();function yo(t,e,n){let i,o=typeof e==\"string\"?e:(0,Ur.formatWithValidation)(e),a=o.match(/^[a-zA-Z]{1,}:\\/\\//),f=a?o.slice(a[0].length):o;if((f.split(\"?\")[0]||\"\").match(/(\\/\\/|\\\\)/)){console.error(\"Invalid href '\"+o+\"' passed to next/router in page: '\"+t.pathname+\"'. Repeated forward-slashes (//) or backslashes \\\\ are not valid in the href.\");let c=(0,mo.normalizeRepeatedSlashes)(f);o=(a?a[0]:\"\")+c}if(!(0,bo.isLocalURL)(o))return n?[o]:o;try{i=new URL(o.startsWith(\"#\")?t.asPath:t.pathname,\"http://n\")}catch{i=new URL(\"/\",\"http://n\")}try{let c=new URL(o,i);c.pathname=(0,ho.normalizePathTrailingSlash)(c.pathname);let p=\"\";if((0,_o.isDynamicRoute)(c.pathname)&&c.searchParams&&n){let S=(0,fo.searchParamsToUrlQuery)(c.searchParams),{result:P,params:U}=(0,go.interpolateAs)(c.pathname,c.pathname,S);P&&(p=(0,Ur.formatWithValidation)({pathname:P,hash:c.hash,query:(0,po.omit)(S,U)}))}let x=c.origin===i.origin?c.href.slice(c.origin.length):c.href;return n?[x,p||x]:x}catch{return n?[o]:o}}(typeof V.default==\"function\"||typeof V.default==\"object\"&&V.default!==null)&&typeof V.default.__esModule>\"u\"&&(Object.defineProperty(V.default,\"__esModule\",{value:!0}),Object.assign(V.default,V),Lr.exports=V.default)});var Ot=h(wt=>{\"use strict\";Object.defineProperty(wt,\"__esModule\",{value:!0});Object.defineProperty(wt,\"addPathPrefix\",{enumerable:!0,get:function(){return vo}});var No=Se();function vo(t,e){if(!t.startsWith(\"/\")||!e)return t;let{pathname:n,query:i,hash:o}=(0,No.parsePath)(t);return\"\"+e+n+i+o}});var zr=h(Dt=>{\"use strict\";Object.defineProperty(Dt,\"__esModule\",{value:!0});Object.defineProperty(Dt,\"addLocale\",{enumerable:!0,get:function(){return To}});var xo=Ot(),Mr=ut();function To(t,e,n,i){if(!e||e===n)return t;let o=t.toLowerCase();return!i&&((0,Mr.pathHasPrefix)(o,\"/api\")||(0,Mr.pathHasPrefix)(o,\"/\"+e.toLowerCase()))?t:(0,xo.addPathPrefix)(t,\"/\"+e)}});var Fr=h(($,qr)=>{\"use strict\";Object.defineProperty($,\"__esModule\",{value:!0});Object.defineProperty($,\"addLocale\",{enumerable:!0,get:function(){return Eo}});var Po=be(),Eo=function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return process.env.__NEXT_I18N_SUPPORT?(0,Po.normalizePathTrailingSlash)(zr().addLocale(t,...n)):t};(typeof $.default==\"function\"||typeof $.default==\"object\"&&$.default!==null)&&typeof $.default.__esModule>\"u\"&&(Object.defineProperty($.default,\"__esModule\",{value:!0}),Object.assign($.default,$),qr.exports=$.default)});var $r=h(St=>{\"use strict\";Object.defineProperty(St,\"__esModule\",{value:!0});Object.defineProperty(St,\"RouterContext\",{enumerable:!0,get:function(){return Vr}});var ko=Oe(),Ro=ko._(de()),Vr=Ro.default.createContext(null);Vr.displayName=\"RouterContext\"});var Gr=h(jt=>{\"use client\";\"use strict\";Object.defineProperty(jt,\"__esModule\",{value:!0});function wo(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}wo(jt,{CacheStates:function(){return Ct},AppRouterContext:function(){return Yr},LayoutRouterContext:function(){return Hr},GlobalLayoutRouterContext:function(){return Br},TemplateContext:function(){return Kr}});var Oo=Oe(),Ce=Oo._(de()),Ct;(function(t){t.LAZY_INITIALIZED=\"LAZYINITIALIZED\",t.DATA_FETCH=\"DATAFETCH\",t.READY=\"READY\"})(Ct||(Ct={}));var Yr=Ce.default.createContext(null),Hr=Ce.default.createContext(null),Br=Ce.default.createContext(null),Kr=Ce.default.createContext(null);Yr.displayName=\"AppRouterContext\",Hr.displayName=\"LayoutRouterContext\",Br.displayName=\"GlobalLayoutRouterContext\",Kr.displayName=\"TemplateContext\"});var Qr=h((Y,Xr)=>{\"use strict\";Object.defineProperty(Y,\"__esModule\",{value:!0});function Do(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Do(Y,{requestIdleCallback:function(){return So},cancelIdleCallback:function(){return Co}});var So=typeof self<\"u\"&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(t){let e=Date.now();return self.setTimeout(function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})},1)},Co=typeof self<\"u\"&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(t){return clearTimeout(t)};(typeof Y.default==\"function\"||typeof Y.default==\"object\"&&Y.default!==null)&&typeof Y.default.__esModule>\"u\"&&(Object.defineProperty(Y.default,\"__esModule\",{value:!0}),Object.assign(Y.default,Y),Xr.exports=Y.default)});var tn=h((H,en)=>{\"use strict\";Object.defineProperty(H,\"__esModule\",{value:!0});Object.defineProperty(H,\"useIntersection\",{enumerable:!0,get:function(){return Io}});var _e=de(),Zr=Qr(),Jr=typeof IntersectionObserver==\"function\",At=new Map,je=[];function jo(t){let e={root:t.root||null,margin:t.rootMargin||\"\"},n=je.find(f=>f.root===e.root&&f.margin===e.margin),i;if(n&&(i=At.get(n),i))return i;let o=new Map,a=new IntersectionObserver(f=>{f.forEach(d=>{let c=o.get(d.target),p=d.isIntersecting||d.intersectionRatio>0;c&&p&&c(p)})},t);return i={id:e,observer:a,elements:o},je.push(e),At.set(e,i),i}function Ao(t,e,n){let{id:i,observer:o,elements:a}=jo(n);return a.set(t,e),o.observe(t),function(){if(a.delete(t),o.unobserve(t),a.size===0){o.disconnect(),At.delete(i);let d=je.findIndex(c=>c.root===i.root&&c.margin===i.margin);d>-1&&je.splice(d,1)}}}function Io(t){let{rootRef:e,rootMargin:n,disabled:i}=t,o=i||!Jr,[a,f]=(0,_e.useState)(!1),d=(0,_e.useRef)(null),c=(0,_e.useCallback)(x=>{d.current=x},[]);(0,_e.useEffect)(()=>{if(Jr){if(o||a)return;let x=d.current;if(x&&x.tagName)return Ao(x,P=>P&&f(P),{root:e?.current,rootMargin:n})}else if(!a){let x=(0,Zr.requestIdleCallback)(()=>f(!0));return()=>(0,Zr.cancelIdleCallback)(x)}},[o,n,e,a,d.current]);let p=(0,_e.useCallback)(()=>{f(!1)},[]);return[c,a,p]}(typeof H.default==\"function\"||typeof H.default==\"object\"&&H.default!==null)&&typeof H.default.__esModule>\"u\"&&(Object.defineProperty(H.default,\"__esModule\",{value:!0}),Object.assign(H.default,H),en.exports=H.default)});var rn=h(It=>{\"use strict\";Object.defineProperty(It,\"__esModule\",{value:!0});Object.defineProperty(It,\"normalizeLocalePath\",{enumerable:!0,get:function(){return Uo}});function Uo(t,e){let n,i=t.split(\"/\");return(e||[]).some(o=>i[1]&&i[1].toLowerCase()===o.toLowerCase()?(n=o,i.splice(1,1),t=i.join(\"/\")||\"/\",!0):!1),{pathname:t,detectedLocale:n}}});var on=h((B,nn)=>{\"use strict\";Object.defineProperty(B,\"__esModule\",{value:!0});Object.defineProperty(B,\"normalizeLocalePath\",{enumerable:!0,get:function(){return Lo}});var Lo=(t,e)=>process.env.__NEXT_I18N_SUPPORT?rn().normalizeLocalePath(t,e):{pathname:t,detectedLocale:void 0};(typeof B.default==\"function\"||typeof B.default==\"object\"&&B.default!==null)&&typeof B.default.__esModule>\"u\"&&(Object.defineProperty(B.default,\"__esModule\",{value:!0}),Object.assign(B.default,B),nn.exports=B.default)});var an=h(Ut=>{\"use strict\";Object.defineProperty(Ut,\"__esModule\",{value:!0});Object.defineProperty(Ut,\"detectDomainLocale\",{enumerable:!0,get:function(){return Wo}});function Wo(t,e,n){if(t){n&&(n=n.toLowerCase());for(let a of t){var i,o;let f=(i=a.domain)==null?void 0:i.split(\":\")[0].toLowerCase();if(e===f||n===a.defaultLocale.toLowerCase()||(o=a.locales)!=null&&o.some(d=>d.toLowerCase()===n))return a}}}});var ln=h((K,sn)=>{\"use strict\";Object.defineProperty(K,\"__esModule\",{value:!0});Object.defineProperty(K,\"detectDomainLocale\",{enumerable:!0,get:function(){return Mo}});var Mo=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(process.env.__NEXT_I18N_SUPPORT)return an().detectDomainLocale(...e)};(typeof K.default==\"function\"||typeof K.default==\"object\"&&K.default!==null)&&typeof K.default.__esModule>\"u\"&&(Object.defineProperty(K.default,\"__esModule\",{value:!0}),Object.assign(K.default,K),sn.exports=K.default)});var un=h((G,cn)=>{\"use strict\";Object.defineProperty(G,\"__esModule\",{value:!0});Object.defineProperty(G,\"getDomainLocale\",{enumerable:!0,get:function(){return Fo}});var zo=be(),qo=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Fo(t,e,n,i){if(process.env.__NEXT_I18N_SUPPORT){let o=on().normalizeLocalePath,a=ln().detectDomainLocale,f=e||o(t,n).detectedLocale,d=a(i,void 0,f);if(d){let c=\"http\"+(d.http?\"\":\"s\")+\"://\",p=f===d.defaultLocale?\"\":\"/\"+f;return\"\"+c+d.domain+(0,zo.normalizePathTrailingSlash)(\"\"+qo+p+t)}return!1}else return!1}(typeof G.default==\"function\"||typeof G.default==\"object\"&&G.default!==null)&&typeof G.default.__esModule>\"u\"&&(Object.defineProperty(G.default,\"__esModule\",{value:!0}),Object.assign(G.default,G),cn.exports=G.default)});var fn=h((X,dn)=>{\"use strict\";Object.defineProperty(X,\"__esModule\",{value:!0});Object.defineProperty(X,\"addBasePath\",{enumerable:!0,get:function(){return Ho}});var Vo=Ot(),$o=be(),Yo=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Ho(t,e){return(0,$o.normalizePathTrailingSlash)(process.env.__NEXT_MANUAL_CLIENT_BASE_PATH&&!e?t:(0,Vo.addPathPrefix)(t,Yo))}(typeof X.default==\"function\"||typeof X.default==\"object\"&&X.default!==null)&&typeof X.default.__esModule>\"u\"&&(Object.defineProperty(X.default,\"__esModule\",{value:!0}),Object.assign(X.default,X),dn.exports=X.default)});var mn=h((Q,pn)=>{\"use strict\";Object.defineProperty(Q,\"__esModule\",{value:!0});function Bo(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Bo(Q,{PrefetchKind:function(){return Lt},ACTION_REFRESH:function(){return Ko},ACTION_NAVIGATE:function(){return Go},ACTION_RESTORE:function(){return Xo},ACTION_SERVER_PATCH:function(){return Qo},ACTION_PREFETCH:function(){return Zo},ACTION_FAST_REFRESH:function(){return Jo},ACTION_SERVER_ACTION:function(){return ea}});var Ko=\"refresh\",Go=\"navigate\",Xo=\"restore\",Qo=\"server-patch\",Zo=\"prefetch\",Jo=\"fast-refresh\",ea=\"server-action\",Lt;(function(t){t.AUTO=\"auto\",t.FULL=\"full\",t.TEMPORARY=\"temporary\"})(Lt||(Lt={}));(typeof Q.default==\"function\"||typeof Q.default==\"object\"&&Q.default!==null)&&typeof Q.default.__esModule>\"u\"&&(Object.defineProperty(Q.default,\"__esModule\",{value:!0}),Object.assign(Q.default,Q),pn.exports=Q.default)});var vn=h((Z,Nn)=>{\"use client\";\"use strict\";Object.defineProperty(Z,\"__esModule\",{value:!0});Object.defineProperty(Z,\"default\",{enumerable:!0,get:function(){return pa}});var ta=Oe(),A=ta._(de()),hn=Wr(),yn=ft(),ra=Ze(),na=he(),ia=Fr(),oa=$r(),aa=Gr(),sa=tn(),la=un(),ca=fn(),bn=mn(),_n=new Set;function Wt(t,e,n,i,o,a){if(typeof window>\"u\"||!a&&!(0,yn.isLocalURL)(e))return;if(!i.bypassPrefetchedCheck){let d=typeof i.locale<\"u\"?i.locale:\"locale\"in t?t.locale:void 0,c=e+\"%\"+n+\"%\"+d;if(_n.has(c))return;_n.add(c)}let f=a?t.prefetch(e,o):t.prefetch(e,n,i);Promise.resolve(f).catch(d=>{throw d})}function ua(t){let n=t.currentTarget.getAttribute(\"target\");return n&&n!==\"_self\"||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&t.nativeEvent.which===2}function da(t,e,n,i,o,a,f,d,c,p){let{nodeName:x}=t.currentTarget;if(x.toUpperCase()===\"A\"&&(ua(t)||!c&&!(0,yn.isLocalURL)(n)))return;t.preventDefault();let P=()=>{let U=f??!0;\"beforePopState\"in e?e[o?\"replace\":\"push\"](n,i,{shallow:a,locale:d,scroll:U}):e[o?\"replace\":\"push\"](i||n,{forceOptimisticNavigation:!p,scroll:U})};c?A.default.startTransition(P):P()}function gn(t){return typeof t==\"string\"?t:(0,ra.formatUrl)(t)}var fa=A.default.forwardRef(function(e,n){let i,{href:o,as:a,children:f,prefetch:d=null,passHref:c,replace:p,shallow:x,scroll:S,locale:P,onClick:U,onMouseEnter:se,onTouchStart:ge,legacyBehavior:I=!1,...te}=e;i=f,I&&(typeof i==\"string\"||typeof i==\"number\")&&(i=A.default.createElement(\"a\",null,i));let _=A.default.useContext(oa.RouterContext),Ie=A.default.useContext(aa.AppRouterContext),W=_??Ie,M=!_,ie=d!==!1,le=d===null?bn.PrefetchKind.AUTO:bn.PrefetchKind.FULL;{let b=function(g){return new Error(\"Failed prop type: The prop `\"+g.key+\"` expects a \"+g.expected+\" in `<Link>`, but got `\"+g.actual+\"` instead.\"+(typeof window<\"u\"?`\nOpen your browser's console to view the Component stack trace.`:\"\"))};Object.keys({href:!0}).forEach(g=>{if(g===\"href\"){if(e[g]==null||typeof e[g]!=\"string\"&&typeof e[g]!=\"object\")throw b({key:g,expected:\"`string` or `object`\",actual:e[g]===null?\"null\":typeof e[g]})}else{let C=g}}),Object.keys({as:!0,replace:!0,scroll:!0,shallow:!0,passHref:!0,prefetch:!0,locale:!0,onClick:!0,onMouseEnter:!0,onTouchStart:!0,legacyBehavior:!0}).forEach(g=>{let C=typeof e[g];if(g===\"as\"){if(e[g]&&C!==\"string\"&&C!==\"object\")throw b({key:g,expected:\"`string` or `object`\",actual:C})}else if(g===\"locale\"){if(e[g]&&C!==\"string\")throw b({key:g,expected:\"`string`\",actual:C})}else if(g===\"onClick\"||g===\"onMouseEnter\"||g===\"onTouchStart\"){if(e[g]&&C!==\"function\")throw b({key:g,expected:\"`function`\",actual:C})}else if(g===\"replace\"||g===\"scroll\"||g===\"shallow\"||g===\"passHref\"||g===\"prefetch\"||g===\"legacyBehavior\"){if(e[g]!=null&&C!==\"boolean\")throw b({key:g,expected:\"`boolean`\",actual:C})}else{let Te=g}});let xe=A.default.useRef(!1);e.prefetch&&!xe.current&&!M&&(xe.current=!0,console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\"))}if(M&&!a){let b;if(typeof o==\"string\"?b=o:typeof o==\"object\"&&typeof o.pathname==\"string\"&&(b=o.pathname),b&&b.split(\"/\").some(ee=>ee.startsWith(\"[\")&&ee.endsWith(\"]\")))throw new Error(\"Dynamic href `\"+b+\"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\")}let{href:z,as:O}=A.default.useMemo(()=>{if(!_){let ee=gn(o);return{href:ee,as:a?gn(a):ee}}let[b,ne]=(0,hn.resolveHref)(_,o,!0);return{href:b,as:a?(0,hn.resolveHref)(_,a):ne||b}},[_,o,a]),ye=A.default.useRef(z),Ne=A.default.useRef(O),R;if(I){U&&console.warn('\"onClick\" was passed to <Link> with `href` of `'+o+'` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link'),se&&console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `'+o+'` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');try{R=A.default.Children.only(i)}catch{throw i?new Error(\"Multiple children were passed to <Link> with `href` of `\"+o+\"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\"+(typeof window<\"u\"?` \nOpen your browser's console to view the Component stack trace.`:\"\")):new Error(\"No children were passed to <Link> with `href` of `\"+o+\"` but one child is required https://nextjs.org/docs/messages/link-no-children\")}}else if(i?.type===\"a\")throw new Error(`Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor`);let w=I?R&&typeof R==\"object\"&&R.ref:n,[J,re,fe]=(0,sa.useIntersection)({rootMargin:\"200px\"}),ve=A.default.useCallback(b=>{(Ne.current!==O||ye.current!==z)&&(fe(),Ne.current=O,ye.current=z),J(b),w&&(typeof w==\"function\"?w(b):typeof w==\"object\"&&(w.current=b))},[O,w,z,fe,J]);A.default.useEffect(()=>{},[O,z,re,P,ie,_?.locale,W,M,le]);let oe={ref:ve,onClick(b){if(!b)throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');!I&&typeof U==\"function\"&&U(b),I&&R.props&&typeof R.props.onClick==\"function\"&&R.props.onClick(b),W&&(b.defaultPrevented||da(b,W,z,O,p,x,S,P,M,ie))},onMouseEnter(b){!I&&typeof se==\"function\"&&se(b),I&&R.props&&typeof R.props.onMouseEnter==\"function\"&&R.props.onMouseEnter(b),W&&((!ie||!0)&&M||Wt(W,z,O,{locale:P,priority:!0,bypassPrefetchedCheck:!0},{kind:le},M))},onTouchStart(b){!I&&typeof ge==\"function\"&&ge(b),I&&R.props&&typeof R.props.onTouchStart==\"function\"&&R.props.onTouchStart(b),W&&(!ie&&M||Wt(W,z,O,{locale:P,priority:!0,bypassPrefetchedCheck:!0},{kind:le},M))}};if((0,na.isAbsoluteUrl)(O))oe.href=O;else if(!I||c||R.type===\"a\"&&!(\"href\"in R.props)){let b=typeof P<\"u\"?P:_?.locale,ne=_?.isLocaleDomain&&(0,la.getDomainLocale)(O,b,_?.locales,_?.domainLocales);oe.href=ne||(0,ca.addBasePath)((0,ia.addLocale)(O,b,_?.defaultLocale))}return I?A.default.cloneElement(R,oe):A.default.createElement(\"a\",{...te,...oe},i)}),pa=fa;(typeof Z.default==\"function\"||typeof Z.default==\"object\"&&Z.default!==null)&&typeof Z.default.__esModule>\"u\"&&(Object.defineProperty(Z.default,\"__esModule\",{value:!0}),Object.assign(Z.default,Z),Nn.exports=Z.default)});var Tn=h((Qa,xn)=>{xn.exports=vn()});var ga={};ei(ga,{default:()=>_a,faqData:()=>ha,frontmatter:()=>ma});var s=Jt(nr());function He({children:t,quizSlug:e}){return React.createElement(\"article\",{className:\"max-w-4xl mx-auto text-gray-900\"},React.createElement(\"div\",{className:\"prose prose-lg max-w-none text-gray-900 [&>*]:text-gray-900\"},t))}var Pn=Jt(Tn());function Ae({task:t,href:e}){let n=e||`/quiz/${t}`;return React.createElement(\"div\",{className:\"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\"},React.createElement(\"div\",{className:\"text-center\"},React.createElement(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-2\"},\"Not sure which AI fits your workflow?\"),React.createElement(\"p\",{className:\"text-gray-600 mb-4\"},\"Take our 30-second quiz to get a personalized recommendation\"),React.createElement(Pn.default,{href:n,className:\"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\"},\"Take the \",t.charAt(0).toUpperCase()+t.slice(1),\" Quiz \\u2192\")))}var ma={title:\"Best AI Image & Texture Generators (2025) \\u2014 Midjourney vs Stable Diffusion, Ideogram & DALL-E\",description:\"Professional comparison of Midjourney, Stable Diffusion, Ideogram, and DALL-E for game developers, digital artists, and marketers. Find your perfect AI image generator.\",slug:\"best-ai-image-generators-2025-midjourney-vs-stable-diffusion-ideogram-dalle\",template:\"pillar\",cluster:\"image\",priority:\"Medium\",lastUpdated:\"2025-07-22\"},ha=[{q:\"What is the best AI for generating photorealistic images?\",a:\"Midjourney is widely regarded as the leader for photorealistic images due to its advanced aesthetic model and parameters like --style raw. However, DALL-E 4 and Stable Diffusion XL can also produce high-quality realistic images, especially with careful prompting.\"},{q:\"Which AI image generator is best for creating logos with text?\",a:\"Ideogram is the specialist for generating images with accurate and well-composed text. Its models are specifically trained for typography, making it the top choice for logos, posters, and marketing materials with text. DALL-E 4 is also improving in this area.\"},{q:\"Can I use images from Midjourney or DALL-E for commercial purposes?\",a:\"Yes, both platforms allow commercial use, but with important conditions. You own the assets you create. However, Midjourney requires a Pro or Mega plan for companies with over $1M in annual revenue. Furthermore, AI-generated images currently exist in a legal gray area and may not be eligible for copyright protection.\"},{q:\"What is the cheapest way to generate a lot of AI images?\",a:\"Using an API for a speed-optimized model like Stable Diffusion XL Lightning is often the most cost-effective method for high-volume generation, with costs potentially under $0.002 per image. However, this requires technical setup. For subscription models, Ideogram's paid plans offer a large number of credits for a relatively low monthly cost.\"},{q:\"What's the difference between Midjourney and Stable Diffusion?\",a:\"The main difference is control versus ease of use. Midjourney offers a curated, high-quality aesthetic experience through its Discord interface. Stable Diffusion is an open-source model that provides maximum control and customization (e.g., training your own models, using ControlNet) but has a much steeper learning curve and often requires a powerful local computer or API integration.\"},{q:\"Which AI image generator is best for game development?\",a:\"A hybrid approach works best: Midjourney for concept art and hero images, Stable Diffusion for bulk asset generation and textures. Stable Diffusion's ControlNet feature is particularly valuable for maintaining consistency across character designs and creating tileable textures.\"}];function En(t){let e=Object.assign({h2:\"h2\",p:\"p\",strong:\"strong\",hr:\"hr\",em:\"em\",h3:\"h3\"},t.components);return(0,s.jsxDEV)(He,{quizSlug:\"image\",children:[(0,s.jsxDEV)(Ae,{task:\"image\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:16,columnNumber:1},this),(0,s.jsxDEV)(e.h2,{children:\"The New Creative Frontier: Beyond Novelty to Professional Integration\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:18,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:\"The era of AI image generation has moved beyond novelty and into a phase of serious professional integration. For game developers, digital artists, and marketers, these tools are no longer experimental toys but powerful assets capable of accelerating workflows, inspiring new concepts, and producing commercial-grade visuals.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:20,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[\"The market has matured into a competitive landscape where the leading platforms\\u2014\",(0,s.jsxDEV)(e.strong,{children:\"Midjourney, Stable Diffusion, Ideogram, and DALL-E\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:22,columnNumber:81},this),'\\u2014each champion a distinct approach to creation. This analysis reveals that there is no single \"best\" generator. The optimal choice balances ',(0,s.jsxDEV)(e.strong,{children:\"Aesthetic Quality, Technical Control, Typographic Fidelity, and Commercial Viability\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:22,columnNumber:275},this),\".\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:22,columnNumber:1},this),(0,s.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:24,columnNumber:1},this),(0,s.jsxDEV)(e.h2,{children:\"The Gauntlet: A Unified Prompt Showdown\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:26,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:\"To test the limits of each model, we used a single, complex prompt designed to evaluate photorealism, object detail, lighting, depth of field, and in-image text generation.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:28,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"The Prompt:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:30,columnNumber:1},this),\" \",(0,s.jsxDEV)(e.em,{children:`\"Photorealistic product shot for a marketing campaign. A sleek, matte black bottle of high-end perfume named 'Etherea' sits on a wet, reflective marble surface. In the background, out of focus, are glowing neon orchids. The words 'Etherea: The Scent of Tomorrow' are elegantly displayed in a modern sans-serif font at the bottom.\"`},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:30,columnNumber:17},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:30,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:\"The Results: Side-by-Side Visual Analysis\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:32,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Midjourney v6:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:34,columnNumber:1},this),' Delivers unparalleled photorealism. The lighting on the wet marble, the texture of the matte bottle, and the soft bokeh of the neon orchids are exceptionally cinematic and \"art-directed\". However, it struggles with the text, rendering a stylized but illegible version of the brand name and tagline.']},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:34,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Stable Diffusion XL Lightning:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:36,columnNumber:1},this),\" Produces a high-quality image with remarkable speed, a testament to its efficient architecture. The composition is strong, but the fine details and lighting nuances lack the hyper-realistic polish of Midjourney. It represents a powerful balance of speed and quality, ideal for rapid iteration.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:36,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Ideogram:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:38,columnNumber:1},this),' Excels where others fail. The text \"Etherea: The Scent of Tomorrow\" is rendered with near-perfect clarity and elegant composition, demonstrating its core strength. The surrounding image is competent but less photorealistic than Midjourney or DALL-E, appearing more like a high-quality digital illustration than a photograph.']},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:38,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"DALL-E 4 (via GPT-4o):\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:40,columnNumber:1},this),\" Shows superior prompt comprehension. It successfully interprets and renders every element of the prompt correctly\\u2014the matte bottle, wet surface, neon bokeh, and the text. The integration with ChatGPT allows for this nuanced understanding. While the text is legible, Ideogram's is more typographically refined.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:40,columnNumber:1},this),(0,s.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:42,columnNumber:1},this),(0,s.jsxDEV)(e.h2,{children:\"Prompt Showdown Scorecard\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:44,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[`| Platform | Photorealism (1-5) | Prompt Adherence (1-5) | Typography (1-5) | Overall Aesthetic (1-5) | Verdict |\n|----------|---------------------|-------------------------|-------------------|-------------------------|---------|\n| `,(0,s.jsxDEV)(e.strong,{children:\"Midjourney v6\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:48,columnNumber:3},this),` | 5 | 4 | 2 | 5 | The Artist: Unmatched beauty, but can't write. |\n| `,(0,s.jsxDEV)(e.strong,{children:\"SDXL Lightning\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:49,columnNumber:3},this),` | 4 | 4 | 2 | 4 | The Engineer: Fast and capable, a workhorse. |\n| `,(0,s.jsxDEV)(e.strong,{children:\"Ideogram\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:50,columnNumber:3},this),` | 3 | 4 | 5 | 4 | The Typographer: Flawless text, good-enough image. |\n| `,(0,s.jsxDEV)(e.strong,{children:\"DALL-E 4\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:51,columnNumber:3},this),\" | 4 | 5 | 4 | 4 | The Co-Creator: Understands everything, a true generalist. |\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:46,columnNumber:1},this),(0,s.jsxDEV)(\"div\",{style:{textAlign:\"right\",fontSize:\"0.9rem\"},children:(0,s.jsxDEV)(\"a\",{href:\"/export/image-scorecard.csv\",children:\"Export to Sheets \\u2192\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:53,columnNumber:52},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:53,columnNumber:1},this),(0,s.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:55,columnNumber:1},this),(0,s.jsxDEV)(e.h2,{children:\"The Business Imperative: Cost vs. Commercial Rights\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:57,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:\"Choosing a platform involves more than just creative output; it requires a careful analysis of cost and legal considerations.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:59,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:\"Economic Analysis: The Cost of 1,000 Images\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:61,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:\"The cost per image varies dramatically depending on the platform's pricing model\\u2014subscription, credits, or pay-per-image API calls.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:63,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[`| Platform | Recommended Plan/Method | Total Cost for 1,000 Images | Effective Cost-per-Image | Key Considerations |\n|----------|-------------------------|------------------------------|--------------------------|-------------------|\n| `,(0,s.jsxDEV)(e.strong,{children:\"Midjourney\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:67,columnNumber:3},this),` | Standard Plan ($30/mo) | ~$30 | ~$0.03 | Subscription includes ~900 fast generations. |\n| `,(0,s.jsxDEV)(e.strong,{children:\"SDXL Lightning\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:68,columnNumber:3},this),` | Replicate API | ~$1.40 | ~$0.0014 | Requires technical setup; API pricing varies by provider. |\n| `,(0,s.jsxDEV)(e.strong,{children:\"Ideogram\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:69,columnNumber:3},this),` | Plus Plan ($16/mo, billed yearly) | ~$16 | ~$0.016 | Includes 1,000 priority credits/month. |\n| `,(0,s.jsxDEV)(e.strong,{children:\"DALL-E 4 (API)\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:70,columnNumber:3},this),\" | OpenAI API (DALL-E 3) | ~$40 | $0.04 | Pay-as-you-go; price is for standard quality. |\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:65,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:\"Legal Framework: Commercial Licensing\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:72,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:\"The ability to use AI-generated images commercially is complex. Under U.S. law, AI-generated images generally cannot be copyrighted, making the platform's Terms of Service paramount.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:74,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[`| Platform | Ownership of Output | Commercial Use Allowed? | Key Restrictions & Revenue Caps | Private Generation? |\n|----------|---------------------|-------------------------|--------------------------------|-------------------|\n| `,(0,s.jsxDEV)(e.strong,{children:\"Midjourney\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:78,columnNumber:3},this),` | You own assets you create. | Yes, with paid plans. | Businesses with >$1M gross annual revenue must use Pro/Mega plan. | Yes (Pro/Mega plans) |\n| `,(0,s.jsxDEV)(e.strong,{children:\"Stable Diffusion\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:79,columnNumber:3},this),` | You own the output. | Yes, with license compliance. | Use is restricted from specific harmful applications. Enterprise license needed for companies >$1M revenue. | N/A (local/private) |\n| `,(0,s.jsxDEV)(e.strong,{children:\"Ideogram\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:80,columnNumber:3},this),` | You are free to use images. | Yes, on all plans. | Free plan generations are public by default, posing a risk for proprietary work. | Yes (Plus/Pro plans) |\n| `,(0,s.jsxDEV)(e.strong,{children:\"DALL-E\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:81,columnNumber:3},this),\" | You own assets you create. | Yes, per OpenAI's terms. | Subject to OpenAI's Content Policy; no explicit revenue caps mentioned for basic use. | Yes (via API/ChatGPT) |\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:76,columnNumber:1},this),(0,s.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:83,columnNumber:1},this),(0,s.jsxDEV)(e.h2,{children:\"Strategic Recommendations by Professional Persona\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:85,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:[\"\\u{1F3AE} \",(0,s.jsxDEV)(e.strong,{children:\"For the Game Developer\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:87,columnNumber:8},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:87,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[\"A hybrid approach is best. Use \",(0,s.jsxDEV)(e.strong,{children:\"Midjourney\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:88,columnNumber:32},this),\" for high-fidelity concept art and marketing visuals. For asset creation, \",(0,s.jsxDEV)(e.strong,{children:\"Stable Diffusion\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:88,columnNumber:120},this),\" is superior for generating tileable textures, large asset batches, and maintaining character consistency with tools like ControlNet.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:88,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:[\"\\u{1F3A8} \",(0,s.jsxDEV)(e.strong,{children:\"For the Digital Artist / Illustrator\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:90,columnNumber:8},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:90,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Midjourney\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:91,columnNumber:1},this),\" is the primary tool for its unparalleled aesthetic control and painterly results. Use \",(0,s.jsxDEV)(e.strong,{children:\"DALL-E 4\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:91,columnNumber:102},this),\" for its conversational interface to rapidly brainstorm and overcome creative blocks.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:91,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:[\"\\u{1F4C8} \",(0,s.jsxDEV)(e.strong,{children:\"For the Marketer / Designer\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:93,columnNumber:8},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:93,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[\"Start with \",(0,s.jsxDEV)(e.strong,{children:\"Ideogram\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:94,columnNumber:12},this),\" for any asset requiring text, such as social media graphics or logos. Use \",(0,s.jsxDEV)(e.strong,{children:\"DALL-E 4\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:94,columnNumber:99},this),\" for quickly generating blog headers and A/B testing ad concepts. For high-stakes campaign hero images, Midjourney's Pro plan ensures both top-tier quality and privacy.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:94,columnNumber:1},this),(0,s.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:96,columnNumber:1},this),(0,s.jsxDEV)(e.h2,{children:\"Deep-dive profiles\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:98,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:[\"Midjourney \\u2014 \",(0,s.jsxDEV)(e.em,{children:\"the aesthetic perfectionist\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:100,columnNumber:18},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:100,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Strengths.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:102,columnNumber:1},this),` Unmatched photorealism and artistic quality; intuitive Discord interface; strong community and prompt sharing.\n`,(0,s.jsxDEV)(e.strong,{children:\"Weaknesses.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:103,columnNumber:1},this),` Poor text rendering; requires Discord; limited fine-tuning control.\n`,(0,s.jsxDEV)(e.em,{children:\"Perfect for: Concept art, marketing hero images, artistic illustrations.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:104,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:102,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:[\"Stable Diffusion \\u2014 \",(0,s.jsxDEV)(e.em,{children:\"the technical powerhouse\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:106,columnNumber:24},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:106,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Strengths.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:108,columnNumber:1},this),` Open-source flexibility; ControlNet for precise control; can run locally; extensive model ecosystem.\n`,(0,s.jsxDEV)(e.strong,{children:\"Weaknesses.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:109,columnNumber:1},this),` Steep learning curve; requires technical setup; inconsistent quality without expertise.\n`,(0,s.jsxDEV)(e.em,{children:\"Perfect for: Bulk generation, custom training, technical workflows.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:110,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:108,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:[\"Ideogram \\u2014 \",(0,s.jsxDEV)(e.em,{children:\"the typography specialist\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:112,columnNumber:16},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:112,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Strengths.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:114,columnNumber:1},this),` Exceptional text rendering; clean, modern aesthetic; affordable pricing; good prompt adherence.\n`,(0,s.jsxDEV)(e.strong,{children:\"Weaknesses.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:115,columnNumber:1},this),` Less photorealistic than competitors; smaller community; newer platform.\n`,(0,s.jsxDEV)(e.em,{children:\"Perfect for: Logos, social media graphics, text-heavy designs.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:116,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:114,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:[\"DALL-E 4 \\u2014 \",(0,s.jsxDEV)(e.em,{children:\"the conversational creator\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:118,columnNumber:16},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:118,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Strengths.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:120,columnNumber:1},this),` Superior prompt understanding; ChatGPT integration; consistent quality; ethical safeguards.\n`,(0,s.jsxDEV)(e.strong,{children:\"Weaknesses.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:121,columnNumber:1},this),` More expensive than alternatives; less artistic flair than Midjourney; API-dependent.\n`,(0,s.jsxDEV)(e.em,{children:\"Perfect for: Rapid ideation, conversational workflows, general-purpose generation.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:122,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:120,columnNumber:1},this),(0,s.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:124,columnNumber:1},this),(0,s.jsxDEV)(Ae,{task:\"image\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:126,columnNumber:1},this),(0,s.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:128,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:14,columnNumber:1},this)}function ba(t={}){let{wrapper:e}=t.components||{};return e?(0,s.jsxDEV)(e,Object.assign({},t,{children:(0,s.jsxDEV)(En,t,void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\"},this):En(t)}var _a=ba;return ti(ga);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "pillars/best-ai-image-generators.mdx", "_raw": {"sourceFilePath": "pillars/best-ai-image-generators.mdx", "sourceFileName": "best-ai-image-generators.mdx", "sourceFileDir": "pillars", "contentType": "mdx", "flattenedPath": "pillars/best-ai-image-generators"}, "type": "<PERSON><PERSON>", "url": "/best-ai-image-generators-2025-midjourney-vs-stable-diffusion-ideogram-dalle", "slugFromPath": "pillars/best-ai-image-generators"}