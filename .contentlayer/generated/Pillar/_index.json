[{"title": "Best AI for Writing (2025) — <PERSON> 3.5 vs GPT-4o, Gemini & more", "description": "Comprehensive comparison of Claude 3.5, GPT-4o, Gemini, Perplexity & Grok for bloggers, students, and marketers. Find your perfect AI writing assistant.", "slug": "best-ai-for-writing-2025-claude-3-5-vs-gpt-4o-gemini-more", "cluster": "text", "template": "pillar", "priority": "Low", "lastUpdated": "2025-07-22", "body": {"raw": "\nimport PillarLayout from \"@/templates/PillarLayout\"\nimport Quiz<PERSON>ta from \"@/components/QuizCta\"\n\n<PillarLayout quizSlug=\"writing\">\n\n<QuizCta task=\"writing\" />\n\n## Who are you writing for ?\n\n**The Blogger**\n\n- **Pain** – needs original long-form content that won't feel robotic or earn an SEO penalty.\n- **Ideal output** – an AI blog generator that keeps a consistent tone.\n- **Killer feature** – a huge context window to track details across thousands of words.\n\n**The Student**\n\n- **Pain** – must research, structure, and cite accurately while avoiding plagiarism.\n- **Ideal output** – an AI essay writer that returns verifiable facts with citations.\n- **Killer feature** – can ingest PDFs and analyse them directly.\n\n**The Marketer**\n\n- **Pain** – high-volume, mixed-format content plus brand-voice consistency.\n- **Ideal output** – a tool that plugs into Google Workspace and accelerates campaigns.\n- **Killer feature** – analyses spreadsheet data and builds project plans.\n\n---\n\n## A market of specialists, not one \"best\" model\n\nPerplexity is an **answer engine**, <PERSON> a **creative prose specialist**, and Gemini a **productivity layer** for Docs, Sheets, and Gmail. The takeaway: _choose by task_, not by raw IQ.\n\n> ### ⚠ Premium trap\n> The $20 Pro tiers are no longer the cutting edge. OpenAI, Anthropic, Perplexity, and xAI now sell $100–$300 \"Max / Heavy\" plans aimed at enterprises. For most writers the $20 tier remains the ROI sweet-spot, but know you're not buying the absolute top model.\n\n---\n\n## 2025 AI-writer scorecard\n\n| Model | Best for (archetype) | Stand-out feature | Context window | Free tier | Pro price | Key limitation |\n|-------|----------------------|-------------------|---------------|-----------|-----------|----------------|\n| **Claude 3.5 Sonnet** | Creative writing (Poet) | \"Artifacts\" live editor | 200 k tokens | Yes (daily cap) | $20 | No native real-time web search |\n| **GPT-4o** | Generalist (Polymath) | Multimodal + Custom GPTs | 128 k tokens | Yes (cap) | $20 | Output can feel robotic |\n| **Gemini Advanced** | Productivity (Producer) | Deep Workspace integration | 1 M + tokens | Yes (std) | $19.99 | Creative flair weaker than Claude |\n| **Perplexity Pro** | Research (Professor) | Clickable citations, Deep Research | — | Yes (cap) | $20 | Not a creative writer |\n| **Grok** | Real-time insights (Provocateur) | Live X / Twitter data | — | Yes (cap) | $30 | Pricey; edgy tone not for all |\n\n<div style={{textAlign:'right',fontSize:'0.9rem'}}><a href=\"/export/scorecard.csv\">Export to Sheets →</a></div>\n\n---\n\n## Speed test ⚡\n\n*[Speed comparison GIF placeholder - GPT-4o vs Claude 3.5 vs Gemini]*\n\nGPT-4o starts generating sooner and streams tokens faster, a win for brainstorms and quick Q & A. Claude is ~2× faster than its predecessor Opus but still trails GPT-4o on sheer responsiveness.\n\n---\n\n## Deep-dive profiles\n\n### Claude 3.5 Sonnet — _the creative wordsmith_\n\n**Strengths.** Thoughtful, expressive prose; 200 k-token context; \"Artifacts\" side-panel for iterative editing.\n**Weaknesses.** No built-in web browsing; free tier message cap.\n_Read the full [Claude 3.5 blogging review](/claude-3-5-for-blogging-review)._\n\n---\n\n### GPT-4o — _the versatile all-rounder_\n\nHandles writing, code, data and images in one chat. Custom GPTs unlock niche workflows.\nCriticisms: increasing verbosity, smaller window than Claude, privacy concerns.\n\n---\n\n### Gemini Advanced — _the integrated productivity engine_\n\nNative in Docs, Sheets, Gmail. Perfect for campaign tables, email summarising, Drive file look-ups.\nDeep dive: [Gemini for marketers](/gemini-advanced-for-marketers-guide).\n\n---\n\n### Perplexity Pro — _the research powerhouse_\n\nDelivers answers with numbered citations; \"Deep Research\" builds exhaustive reports.\nGuide: [How to use Perplexity for academic research](/how-to-use-perplexity-for-academic-research).\n\n---\n\n### Grok — _the real-time provocateur_\n\nLive social-media pulse plus a snarky attitude. Great for trend analysts, overkill for everyday writing.\n\n---\n\n<QuizCta task=\"writing\" />\n\n---\n\nexport const faqData = [\n  { q: \"What is the best free AI for writing?\", a: \"Perplexity offers the strongest free tier for fact-based writing, while Claude's free tier is best for creative prose.\" },\n  { q: \"Can Google penalise AI-generated content?\", a: \"Google ranks helpful content regardless of how it's produced; thin or spammy AI text can be penalised.\" },\n  { q: \"What's a context window and why does it matter?\", a: \"It's the amount of text an AI can 'remember'. Bigger windows (e.g., Claude's 200 k tokens) keep long documents coherent.\" },\n  { q: \"Which AI is best for creative writing?\", a: \"Claude 3.5 Sonnet consistently produces the most human-like, nuanced prose.\" },\n  { q: \"Which AI provides reliable citations?\", a: \"Perplexity Pro surfaces sources and clickable references by default.\" },\n  { q: \"Is GPT-4o still king in 2025?\", a: \"It's the best all-rounder, but Claude wins on style and Perplexity on accuracy.\" }\n]\n\n</PillarLayout>\n", "code": "var Component=(()=>{var Bn=Object.create;var we=Object.defineProperty;var Xn=Object.getOwnPropertyDescriptor;var Qn=Object.getOwnPropertyNames;var Zn=Object.getPrototypeOf,Jn=Object.prototype.hasOwnProperty;var h=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),ei=(t,e)=>{for(var n in e)we(t,n,{get:e[n],enumerable:!0})},Zt=(t,e,n,i)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let o of Qn(e))!Jn.call(t,o)&&o!==n&&we(t,o,{get:()=>e[o],enumerable:!(i=Xn(e,o))||i.enumerable});return t};var Jt=(t,e,n)=>(n=t!=null?Bn(Zn(t)):{},Zt(e||!t||!t.__esModule?we(n,\"default\",{value:t,enumerable:!0}):n,t)),ti=t=>Zt(we({},\"__esModule\",{value:!0}),t);var de=h((Ns,er)=>{er.exports=React});var tr=h($e=>{\"use strict\";(function(){\"use strict\";var t=de(),e=Symbol.for(\"react.element\"),n=Symbol.for(\"react.portal\"),i=Symbol.for(\"react.fragment\"),o=Symbol.for(\"react.strict_mode\"),s=Symbol.for(\"react.profiler\"),f=Symbol.for(\"react.provider\"),d=Symbol.for(\"react.context\"),u=Symbol.for(\"react.forward_ref\"),p=Symbol.for(\"react.suspense\"),x=Symbol.for(\"react.suspense_list\"),S=Symbol.for(\"react.memo\"),T=Symbol.for(\"react.lazy\"),U=Symbol.for(\"react.offscreen\"),le=Symbol.iterator,ge=\"@@iterator\";function I(r){if(r===null||typeof r!=\"object\")return null;var l=le&&r[le]||r[ge];return typeof l==\"function\"?l:null}var te=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function _(r){{for(var l=arguments.length,c=new Array(l>1?l-1:0),m=1;m<l;m++)c[m-1]=arguments[m];Ie(\"error\",r,c)}}function Ie(r,l,c){{var m=te.ReactDebugCurrentFrame,v=m.getStackAddendum();v!==\"\"&&(l+=\"%s\",c=c.concat([v]));var P=c.map(function(N){return String(N)});P.unshift(\"Warning: \"+l),Function.prototype.apply.call(console[r],console,P)}}var z=!1,M=!1,ie=!1,ae=!1,L=!1,O;O=Symbol.for(\"react.module.reference\");function ye(r){return!!(typeof r==\"string\"||typeof r==\"function\"||r===i||r===s||L||r===o||r===p||r===x||ae||r===U||z||M||ie||typeof r==\"object\"&&r!==null&&(r.$$typeof===T||r.$$typeof===S||r.$$typeof===f||r.$$typeof===d||r.$$typeof===u||r.$$typeof===O||r.getModuleId!==void 0))}function Ne(r,l,c){var m=r.displayName;if(m)return m;var v=l.displayName||l.name||\"\";return v!==\"\"?c+\"(\"+v+\")\":c}function E(r){return r.displayName||\"Context\"}function w(r){if(r==null)return null;if(typeof r.tag==\"number\"&&_(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof r==\"function\")return r.displayName||r.name||null;if(typeof r==\"string\")return r;switch(r){case i:return\"Fragment\";case n:return\"Portal\";case s:return\"Profiler\";case o:return\"StrictMode\";case p:return\"Suspense\";case x:return\"SuspenseList\"}if(typeof r==\"object\")switch(r.$$typeof){case d:var l=r;return E(l)+\".Consumer\";case f:var c=r;return E(c._context)+\".Provider\";case u:return Ne(r,r.render,\"ForwardRef\");case S:var m=r.displayName||null;return m!==null?m:w(r.type)||\"Memo\";case T:{var v=r,P=v._payload,N=v._init;try{return w(N(P))}catch{return null}}}return null}var J=Object.assign,re=0,fe,ve,oe,b,ne,ee,Ue;function We(){}We.__reactDisabledLog=!0;function xe(){{if(re===0){fe=console.log,ve=console.info,oe=console.warn,b=console.error,ne=console.group,ee=console.groupCollapsed,Ue=console.groupEnd;var r={configurable:!0,enumerable:!0,value:We,writable:!0};Object.defineProperties(console,{info:r,log:r,warn:r,error:r,group:r,groupCollapsed:r,groupEnd:r})}re++}}function g(){{if(re--,re===0){var r={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:J({},r,{value:fe}),info:J({},r,{value:ve}),warn:J({},r,{value:oe}),error:J({},r,{value:b}),group:J({},r,{value:ne}),groupCollapsed:J({},r,{value:ee}),groupEnd:J({},r,{value:Ue})})}re<0&&_(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var D=te.ReactCurrentDispatcher,Pe;function Te(r,l,c){{if(Pe===void 0)try{throw Error()}catch(v){var m=v.stack.trim().match(/\\n( *(at )?)/);Pe=m&&m[1]||\"\"}return`\n`+Pe+r}}var ze=!1,Re;{var kn=typeof WeakMap==\"function\"?WeakMap:Map;Re=new kn}function Mt(r,l){if(!r||ze)return\"\";{var c=Re.get(r);if(c!==void 0)return c}var m;ze=!0;var v=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var P;P=D.current,D.current=null,xe();try{if(l){var N=function(){throw Error()};if(Object.defineProperty(N.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(N,[])}catch(j){m=j}Reflect.construct(r,[],N)}else{try{N.call()}catch(j){m=j}r.call(N.prototype)}}else{try{throw Error()}catch(j){m=j}r()}}catch(j){if(j&&m&&typeof j.stack==\"string\"){for(var y=j.stack.split(`\n`),C=m.stack.split(`\n`),R=y.length-1,k=C.length-1;R>=1&&k>=0&&y[R]!==C[k];)k--;for(;R>=1&&k>=0;R--,k--)if(y[R]!==C[k]){if(R!==1||k!==1)do if(R--,k--,k<0||y[R]!==C[k]){var W=`\n`+y[R].replace(\" at new \",\" at \");return r.displayName&&W.includes(\"<anonymous>\")&&(W=W.replace(\"<anonymous>\",r.displayName)),typeof r==\"function\"&&Re.set(r,W),W}while(R>=1&&k>=0);break}}}finally{ze=!1,D.current=P,g(),Error.prepareStackTrace=v}var ce=r?r.displayName||r.name:\"\",se=ce?Te(ce):\"\";return typeof r==\"function\"&&Re.set(r,se),se}function En(r,l,c){return Mt(r,!1)}function wn(r){var l=r.prototype;return!!(l&&l.isReactComponent)}function ke(r,l,c){if(r==null)return\"\";if(typeof r==\"function\")return Mt(r,wn(r));if(typeof r==\"string\")return Te(r);switch(r){case p:return Te(\"Suspense\");case x:return Te(\"SuspenseList\")}if(typeof r==\"object\")switch(r.$$typeof){case u:return En(r.render);case S:return ke(r.type,l,c);case T:{var m=r,v=m._payload,P=m._init;try{return ke(P(v),l,c)}catch{}}}return\"\"}var pe=Object.prototype.hasOwnProperty,Lt={},qt=te.ReactDebugCurrentFrame;function Ee(r){if(r){var l=r._owner,c=ke(r.type,r._source,l?l.type:null);qt.setExtraStackFrame(c)}else qt.setExtraStackFrame(null)}function On(r,l,c,m,v){{var P=Function.call.bind(pe);for(var N in r)if(P(r,N)){var y=void 0;try{if(typeof r[N]!=\"function\"){var C=Error((m||\"React class\")+\": \"+c+\" type `\"+N+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof r[N]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw C.name=\"Invariant Violation\",C}y=r[N](l,N,m,c,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(R){y=R}y&&!(y instanceof Error)&&(Ee(v),_(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",m||\"React class\",c,N,typeof y),Ee(null)),y instanceof Error&&!(y.message in Lt)&&(Lt[y.message]=!0,Ee(v),_(\"Failed %s type: %s\",c,y.message),Ee(null))}}}var Cn=Array.isArray;function Me(r){return Cn(r)}function Sn(r){{var l=typeof Symbol==\"function\"&&Symbol.toStringTag,c=l&&r[Symbol.toStringTag]||r.constructor.name||\"Object\";return c}}function Dn(r){try{return Ft(r),!1}catch{return!0}}function Ft(r){return\"\"+r}function Gt(r){if(Dn(r))return _(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",Sn(r)),Ft(r)}var me=te.ReactCurrentOwner,jn={key:!0,ref:!0,__self:!0,__source:!0},Vt,$t,Le;Le={};function An(r){if(pe.call(r,\"ref\")){var l=Object.getOwnPropertyDescriptor(r,\"ref\").get;if(l&&l.isReactWarning)return!1}return r.ref!==void 0}function In(r){if(pe.call(r,\"key\")){var l=Object.getOwnPropertyDescriptor(r,\"key\").get;if(l&&l.isReactWarning)return!1}return r.key!==void 0}function Un(r,l){if(typeof r.ref==\"string\"&&me.current&&l&&me.current.stateNode!==l){var c=w(me.current.type);Le[c]||(_('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',w(me.current.type),r.ref),Le[c]=!0)}}function Wn(r,l){{var c=function(){Vt||(Vt=!0,_(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",l))};c.isReactWarning=!0,Object.defineProperty(r,\"key\",{get:c,configurable:!0})}}function zn(r,l){{var c=function(){$t||($t=!0,_(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",l))};c.isReactWarning=!0,Object.defineProperty(r,\"ref\",{get:c,configurable:!0})}}var Mn=function(r,l,c,m,v,P,N){var y={$$typeof:e,type:r,key:l,ref:c,props:N,_owner:P};return y._store={},Object.defineProperty(y._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(y,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:m}),Object.defineProperty(y,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:v}),Object.freeze&&(Object.freeze(y.props),Object.freeze(y)),y};function Ln(r,l,c,m,v){{var P,N={},y=null,C=null;c!==void 0&&(Gt(c),y=\"\"+c),In(l)&&(Gt(l.key),y=\"\"+l.key),An(l)&&(C=l.ref,Un(l,v));for(P in l)pe.call(l,P)&&!jn.hasOwnProperty(P)&&(N[P]=l[P]);if(r&&r.defaultProps){var R=r.defaultProps;for(P in R)N[P]===void 0&&(N[P]=R[P])}if(y||C){var k=typeof r==\"function\"?r.displayName||r.name||\"Unknown\":r;y&&Wn(N,k),C&&zn(N,k)}return Mn(r,y,C,v,m,me.current,N)}}var qe=te.ReactCurrentOwner,Ht=te.ReactDebugCurrentFrame;function ue(r){if(r){var l=r._owner,c=ke(r.type,r._source,l?l.type:null);Ht.setExtraStackFrame(c)}else Ht.setExtraStackFrame(null)}var Fe;Fe=!1;function Ge(r){return typeof r==\"object\"&&r!==null&&r.$$typeof===e}function Kt(){{if(qe.current){var r=w(qe.current.type);if(r)return`\n\nCheck the render method of \\``+r+\"`.\"}return\"\"}}function qn(r){{if(r!==void 0){var l=r.fileName.replace(/^.*[\\\\\\/]/,\"\"),c=r.lineNumber;return`\n\nCheck your code at `+l+\":\"+c+\".\"}return\"\"}}var Yt={};function Fn(r){{var l=Kt();if(!l){var c=typeof r==\"string\"?r:r.displayName||r.name;c&&(l=`\n\nCheck the top-level render call using <`+c+\">.\")}return l}}function Bt(r,l){{if(!r._store||r._store.validated||r.key!=null)return;r._store.validated=!0;var c=Fn(l);if(Yt[c])return;Yt[c]=!0;var m=\"\";r&&r._owner&&r._owner!==qe.current&&(m=\" It was passed a child from \"+w(r._owner.type)+\".\"),ue(r),_('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',c,m),ue(null)}}function Xt(r,l){{if(typeof r!=\"object\")return;if(Me(r))for(var c=0;c<r.length;c++){var m=r[c];Ge(m)&&Bt(m,l)}else if(Ge(r))r._store&&(r._store.validated=!0);else if(r){var v=I(r);if(typeof v==\"function\"&&v!==r.entries)for(var P=v.call(r),N;!(N=P.next()).done;)Ge(N.value)&&Bt(N.value,l)}}}function Gn(r){{var l=r.type;if(l==null||typeof l==\"string\")return;var c;if(typeof l==\"function\")c=l.propTypes;else if(typeof l==\"object\"&&(l.$$typeof===u||l.$$typeof===S))c=l.propTypes;else return;if(c){var m=w(l);On(c,r.props,\"prop\",m,r)}else if(l.PropTypes!==void 0&&!Fe){Fe=!0;var v=w(l);_(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",v||\"Unknown\")}typeof l.getDefaultProps==\"function\"&&!l.getDefaultProps.isReactClassApproved&&_(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function Vn(r){{for(var l=Object.keys(r.props),c=0;c<l.length;c++){var m=l[c];if(m!==\"children\"&&m!==\"key\"){ue(r),_(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",m),ue(null);break}}r.ref!==null&&(ue(r),_(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),ue(null))}}var Qt={};function $n(r,l,c,m,v,P){{var N=ye(r);if(!N){var y=\"\";(r===void 0||typeof r==\"object\"&&r!==null&&Object.keys(r).length===0)&&(y+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var C=qn(v);C?y+=C:y+=Kt();var R;r===null?R=\"null\":Me(r)?R=\"array\":r!==void 0&&r.$$typeof===e?(R=\"<\"+(w(r.type)||\"Unknown\")+\" />\",y=\" Did you accidentally export a JSX literal instead of a component?\"):R=typeof r,_(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",R,y)}var k=Ln(r,l,c,v,P);if(k==null)return k;if(N){var W=l.children;if(W!==void 0)if(m)if(Me(W)){for(var ce=0;ce<W.length;ce++)Xt(W[ce],r);Object.freeze&&Object.freeze(W)}else _(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else Xt(W,r)}if(pe.call(l,\"key\")){var se=w(r),j=Object.keys(l).filter(function(Yn){return Yn!==\"key\"}),Ve=j.length>0?\"{key: someKey, \"+j.join(\": ..., \")+\": ...}\":\"{key: someKey}\";if(!Qt[se+Ve]){var Kn=j.length>0?\"{\"+j.join(\": ..., \")+\": ...}\":\"{}\";_(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,Ve,se,Kn,se),Qt[se+Ve]=!0}}return r===i?Vn(k):Gn(k),k}}var Hn=$n;$e.Fragment=i,$e.jsxDEV=Hn})()});var nr=h((xs,rr)=>{\"use strict\";rr.exports=tr()});var Oe=h(Ke=>{\"use strict\";Ke._=Ke._interop_require_default=ri;function ri(t){return t&&t.__esModule?t:{default:t}}});var Be=h(Ye=>{\"use strict\";Object.defineProperty(Ye,\"__esModule\",{value:!0});function ni(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}ni(Ye,{searchParamsToUrlQuery:function(){return ii},urlQueryToSearchParams:function(){return oi},assign:function(){return si}});function ii(t){let e={};return t.forEach((n,i)=>{typeof e[i]>\"u\"?e[i]=n:Array.isArray(e[i])?e[i].push(n):e[i]=[e[i],n]}),e}function ir(t){return typeof t==\"string\"||typeof t==\"number\"&&!isNaN(t)||typeof t==\"boolean\"?String(t):\"\"}function oi(t){let e=new URLSearchParams;return Object.entries(t).forEach(n=>{let[i,o]=n;Array.isArray(o)?o.forEach(s=>e.append(i,ir(s))):e.set(i,ir(o))}),e}function si(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return n.forEach(o=>{Array.from(o.keys()).forEach(s=>t.delete(s)),o.forEach((s,f)=>t.append(f,s))}),t}});var sr=h(Xe=>{\"use strict\";function or(t){if(typeof WeakMap!=\"function\")return null;var e=new WeakMap,n=new WeakMap;return(or=function(i){return i?n:e})(t)}Xe._=Xe._interop_require_wildcard=li;function li(t,e){if(!e&&t&&t.__esModule)return t;if(t===null||typeof t!=\"object\"&&typeof t!=\"function\")return{default:t};var n=or(e);if(n&&n.has(t))return n.get(t);var i={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if(s!==\"default\"&&Object.prototype.hasOwnProperty.call(t,s)){var f=o?Object.getOwnPropertyDescriptor(t,s):null;f&&(f.get||f.set)?Object.defineProperty(i,s,f):i[s]=t[s]}return i.default=t,n&&n.set(t,i),i}});var Ze=h(Qe=>{\"use strict\";Object.defineProperty(Qe,\"__esModule\",{value:!0});function ai(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}ai(Qe,{formatUrl:function(){return lr},urlObjectKeys:function(){return ar},formatWithValidation:function(){return fi}});var ui=sr(),ci=ui._(Be()),di=/https?|ftp|gopher|file/;function lr(t){let{auth:e,hostname:n}=t,i=t.protocol||\"\",o=t.pathname||\"\",s=t.hash||\"\",f=t.query||\"\",d=!1;e=e?encodeURIComponent(e).replace(/%3A/i,\":\")+\"@\":\"\",t.host?d=e+t.host:n&&(d=e+(~n.indexOf(\":\")?\"[\"+n+\"]\":n),t.port&&(d+=\":\"+t.port)),f&&typeof f==\"object\"&&(f=String(ci.urlQueryToSearchParams(f)));let u=t.search||f&&\"?\"+f||\"\";return i&&!i.endsWith(\":\")&&(i+=\":\"),t.slashes||(!i||di.test(i))&&d!==!1?(d=\"//\"+(d||\"\"),o&&o[0]!==\"/\"&&(o=\"/\"+o)):d||(d=\"\"),s&&s[0]!==\"#\"&&(s=\"#\"+s),u&&u[0]!==\"?\"&&(u=\"?\"+u),o=o.replace(/[?#]/g,encodeURIComponent),u=u.replace(\"#\",\"%23\"),\"\"+i+d+o+u+s}var ar=[\"auth\",\"hash\",\"host\",\"hostname\",\"href\",\"path\",\"pathname\",\"port\",\"protocol\",\"query\",\"search\",\"slashes\"];function fi(t){return t!==null&&typeof t==\"object\"&&Object.keys(t).forEach(e=>{ar.includes(e)||console.warn(\"Unknown key passed via urlObject into url.format: \"+e)}),lr(t)}});var ur=h(Je=>{\"use strict\";Object.defineProperty(Je,\"__esModule\",{value:!0});Object.defineProperty(Je,\"omit\",{enumerable:!0,get:function(){return pi}});function pi(t,e){let n={};return Object.keys(t).forEach(i=>{e.includes(i)||(n[i]=t[i])}),n}});var he=h(ot=>{\"use strict\";Object.defineProperty(ot,\"__esModule\",{value:!0});function mi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}mi(ot,{WEB_VITALS:function(){return hi},execOnce:function(){return bi},isAbsoluteUrl:function(){return gi},getLocationOrigin:function(){return cr},getURL:function(){return yi},getDisplayName:function(){return Ce},isResSent:function(){return dr},normalizeRepeatedSlashes:function(){return Ni},loadGetInitialProps:function(){return fr},SP:function(){return pr},ST:function(){return vi},DecodeError:function(){return et},NormalizeError:function(){return tt},PageNotFoundError:function(){return rt},MissingStaticPage:function(){return nt},MiddlewareNotFoundError:function(){return it},stringifyError:function(){return xi}});var hi=[\"CLS\",\"FCP\",\"FID\",\"INP\",\"LCP\",\"TTFB\"];function bi(t){let e=!1,n;return function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return e||(e=!0,n=t(...o)),n}}var _i=/^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/,gi=t=>_i.test(t);function cr(){let{protocol:t,hostname:e,port:n}=window.location;return t+\"//\"+e+(n?\":\"+n:\"\")}function yi(){let{href:t}=window.location,e=cr();return t.substring(e.length)}function Ce(t){return typeof t==\"string\"?t:t.displayName||t.name||\"Unknown\"}function dr(t){return t.finished||t.headersSent}function Ni(t){let e=t.split(\"?\");return e[0].replace(/\\\\/g,\"/\").replace(/\\/\\/+/g,\"/\")+(e[1]?\"?\"+e.slice(1).join(\"?\"):\"\")}async function fr(t,e){var n;if((n=t.prototype)!=null&&n.getInitialProps){let s='\"'+Ce(t)+'.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';throw new Error(s)}let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await fr(e.Component,e.ctx)}:{};let o=await t.getInitialProps(e);if(i&&dr(i))return o;if(!o){let s='\"'+Ce(t)+'.getInitialProps()\" should resolve to an object. But found \"'+o+'\" instead.';throw new Error(s)}return Object.keys(o).length===0&&!e.ctx&&console.warn(\"\"+Ce(t)+\" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\"),o}var pr=typeof performance<\"u\",vi=pr&&[\"mark\",\"measure\",\"getEntriesByName\"].every(t=>typeof performance[t]==\"function\"),et=class extends Error{},tt=class extends Error{},rt=class extends Error{constructor(e){super(),this.code=\"ENOENT\",this.name=\"PageNotFoundError\",this.message=\"Cannot find module for page: \"+e}},nt=class extends Error{constructor(e,n){super(),this.message=\"Failed to load static file for page: \"+e+\" \"+n}},it=class extends Error{constructor(){super(),this.code=\"ENOENT\",this.message=\"Cannot find the middleware module\"}};function xi(t){return JSON.stringify({message:t.message,stack:t.stack})}});var lt=h(st=>{\"use strict\";Object.defineProperty(st,\"__esModule\",{value:!0});Object.defineProperty(st,\"removeTrailingSlash\",{enumerable:!0,get:function(){return Pi}});function Pi(t){return t.replace(/\\/$/,\"\")||\"/\"}});var Se=h(at=>{\"use strict\";Object.defineProperty(at,\"__esModule\",{value:!0});Object.defineProperty(at,\"parsePath\",{enumerable:!0,get:function(){return Ti}});function Ti(t){let e=t.indexOf(\"#\"),n=t.indexOf(\"?\"),i=n>-1&&(e<0||n<e);return i||e>-1?{pathname:t.substring(0,i?n:e),query:i?t.substring(n,e>-1?e:void 0):\"\",hash:e>-1?t.slice(e):\"\"}:{pathname:t,query:\"\",hash:\"\"}}});var be=h((q,hr)=>{\"use strict\";Object.defineProperty(q,\"__esModule\",{value:!0});Object.defineProperty(q,\"normalizePathTrailingSlash\",{enumerable:!0,get:function(){return ki}});var mr=lt(),Ri=Se(),ki=t=>{if(!t.startsWith(\"/\")||process.env.__NEXT_MANUAL_TRAILING_SLASH)return t;let{pathname:e,query:n,hash:i}=(0,Ri.parsePath)(t);return process.env.__NEXT_TRAILING_SLASH?/\\.[^/]+\\/?$/.test(e)?\"\"+(0,mr.removeTrailingSlash)(e)+n+i:e.endsWith(\"/\")?\"\"+e+n+i:e+\"/\"+n+i:\"\"+(0,mr.removeTrailingSlash)(e)+n+i};(typeof q.default==\"function\"||typeof q.default==\"object\"&&q.default!==null)&&typeof q.default.__esModule>\"u\"&&(Object.defineProperty(q.default,\"__esModule\",{value:!0}),Object.assign(q.default,q),hr.exports=q.default)});var ct=h(ut=>{\"use strict\";Object.defineProperty(ut,\"__esModule\",{value:!0});Object.defineProperty(ut,\"pathHasPrefix\",{enumerable:!0,get:function(){return wi}});var Ei=Se();function wi(t,e){if(typeof t!=\"string\")return!1;let{pathname:n}=(0,Ei.parsePath)(t);return n===e||n.startsWith(e+\"/\")}});var _r=h((F,br)=>{\"use strict\";Object.defineProperty(F,\"__esModule\",{value:!0});Object.defineProperty(F,\"hasBasePath\",{enumerable:!0,get:function(){return Si}});var Oi=ct(),Ci=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Si(t){return(0,Oi.pathHasPrefix)(t,Ci)}(typeof F.default==\"function\"||typeof F.default==\"object\"&&F.default!==null)&&typeof F.default.__esModule>\"u\"&&(Object.defineProperty(F.default,\"__esModule\",{value:!0}),Object.assign(F.default,F),br.exports=F.default)});var ft=h(dt=>{\"use strict\";Object.defineProperty(dt,\"__esModule\",{value:!0});Object.defineProperty(dt,\"isLocalURL\",{enumerable:!0,get:function(){return ji}});var gr=he(),Di=_r();function ji(t){if(!(0,gr.isAbsoluteUrl)(t))return!0;try{let e=(0,gr.getLocationOrigin)(),n=new URL(t,e);return n.origin===e&&(0,Di.hasBasePath)(n.pathname)}catch{return!1}}});var yr=h(mt=>{\"use strict\";Object.defineProperty(mt,\"__esModule\",{value:!0});Object.defineProperty(mt,\"getSortedRoutes\",{enumerable:!0,get:function(){return Ai}});var pt=class t{insert(e){this._insert(e.split(\"/\").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){e===void 0&&(e=\"/\");let n=[...this.children.keys()].sort();this.slugName!==null&&n.splice(n.indexOf(\"[]\"),1),this.restSlugName!==null&&n.splice(n.indexOf(\"[...]\"),1),this.optionalRestSlugName!==null&&n.splice(n.indexOf(\"[[...]]\"),1);let i=n.map(o=>this.children.get(o)._smoosh(\"\"+e+o+\"/\")).reduce((o,s)=>[...o,...s],[]);if(this.slugName!==null&&i.push(...this.children.get(\"[]\")._smoosh(e+\"[\"+this.slugName+\"]/\")),!this.placeholder){let o=e===\"/\"?\"/\":e.slice(0,-1);if(this.optionalRestSlugName!=null)throw new Error('You cannot define a route with the same specificity as a optional catch-all route (\"'+o+'\" and \"'+o+\"[[...\"+this.optionalRestSlugName+']]\").');i.unshift(o)}return this.restSlugName!==null&&i.push(...this.children.get(\"[...]\")._smoosh(e+\"[...\"+this.restSlugName+\"]/\")),this.optionalRestSlugName!==null&&i.push(...this.children.get(\"[[...]]\")._smoosh(e+\"[[...\"+this.optionalRestSlugName+\"]]/\")),i}_insert(e,n,i){if(e.length===0){this.placeholder=!1;return}if(i)throw new Error(\"Catch-all must be the last part of the URL.\");let o=e[0];if(o.startsWith(\"[\")&&o.endsWith(\"]\")){let d=function(u,p){if(u!==null&&u!==p)throw new Error(\"You cannot use different slug names for the same dynamic path ('\"+u+\"' !== '\"+p+\"').\");n.forEach(x=>{if(x===p)throw new Error('You cannot have the same slug name \"'+p+'\" repeat within a single dynamic path');if(x.replace(/\\W/g,\"\")===o.replace(/\\W/g,\"\"))throw new Error('You cannot have the slug names \"'+x+'\" and \"'+p+'\" differ only by non-word symbols within a single dynamic path')}),n.push(p)},s=o.slice(1,-1),f=!1;if(s.startsWith(\"[\")&&s.endsWith(\"]\")&&(s=s.slice(1,-1),f=!0),s.startsWith(\"...\")&&(s=s.substring(3),i=!0),s.startsWith(\"[\")||s.endsWith(\"]\"))throw new Error(\"Segment names may not start or end with extra brackets ('\"+s+\"').\");if(s.startsWith(\".\"))throw new Error(\"Segment names may not start with erroneous periods ('\"+s+\"').\");if(i)if(f){if(this.restSlugName!=null)throw new Error('You cannot use both an required and optional catch-all route at the same level (\"[...'+this.restSlugName+']\" and \"'+e[0]+'\" ).');d(this.optionalRestSlugName,s),this.optionalRestSlugName=s,o=\"[[...]]\"}else{if(this.optionalRestSlugName!=null)throw new Error('You cannot use both an optional and required catch-all route at the same level (\"[[...'+this.optionalRestSlugName+']]\" and \"'+e[0]+'\").');d(this.restSlugName,s),this.restSlugName=s,o=\"[...]\"}else{if(f)throw new Error('Optional route parameters are not yet supported (\"'+e[0]+'\").');d(this.slugName,s),this.slugName=s,o=\"[]\"}}this.children.has(o)||this.children.set(o,new t),this.children.get(o)._insert(e.slice(1),n,i)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}};function Ai(t){let e=new pt;return t.forEach(n=>e.insert(n)),e.smoosh()}});var Nr=h(ht=>{\"use strict\";Object.defineProperty(ht,\"__esModule\",{value:!0});Object.defineProperty(ht,\"isDynamicRoute\",{enumerable:!0,get:function(){return Ui}});var Ii=/\\/\\[[^/]+?\\](?=\\/|$)/;function Ui(t){return Ii.test(t)}});var vr=h(bt=>{\"use strict\";Object.defineProperty(bt,\"__esModule\",{value:!0});function Wi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Wi(bt,{getSortedRoutes:function(){return zi.getSortedRoutes},isDynamicRoute:function(){return Mi.isDynamicRoute}});var zi=yr(),Mi=Nr()});var xr=h(_t=>{\"use strict\";Object.defineProperty(_t,\"__esModule\",{value:!0});Object.defineProperty(_t,\"getRouteMatcher\",{enumerable:!0,get:function(){return qi}});var Li=he();function qi(t){let{re:e,groups:n}=t;return i=>{let o=e.exec(i);if(!o)return!1;let s=d=>{try{return decodeURIComponent(d)}catch{throw new Li.DecodeError(\"failed to decode param\")}},f={};return Object.keys(n).forEach(d=>{let u=n[d],p=o[u.pos];p!==void 0&&(f[d]=~p.indexOf(\"/\")?p.split(\"/\").map(x=>s(x)):u.repeat?[s(p)]:s(p))}),f}}});var Pr=h(gt=>{\"use strict\";Object.defineProperty(gt,\"__esModule\",{value:!0});Object.defineProperty(gt,\"ensureLeadingSlash\",{enumerable:!0,get:function(){return Fi}});function Fi(t){return t.startsWith(\"/\")?t:\"/\"+t}});var Tr=h(yt=>{\"use strict\";Object.defineProperty(yt,\"__esModule\",{value:!0});Object.defineProperty(yt,\"isGroupSegment\",{enumerable:!0,get:function(){return Gi}});function Gi(t){return t[0]===\"(\"&&t.endsWith(\")\")}});var Rr=h(Nt=>{\"use strict\";Object.defineProperty(Nt,\"__esModule\",{value:!0});function Vi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Vi(Nt,{normalizeAppPath:function(){return Ki},normalizeRscPath:function(){return Yi}});var $i=Pr(),Hi=Tr();function Ki(t){return(0,$i.ensureLeadingSlash)(t.split(\"/\").reduce((e,n,i,o)=>!n||(0,Hi.isGroupSegment)(n)||n[0]===\"@\"||(n===\"page\"||n===\"route\")&&i===o.length-1?e:e+\"/\"+n,\"\"))}function Yi(t,e){return e?t.replace(/\\.rsc($|\\?)/,\"$1\"):t}});var kr=h(xt=>{\"use strict\";Object.defineProperty(xt,\"__esModule\",{value:!0});function Bi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Bi(xt,{INTERCEPTION_ROUTE_MARKERS:function(){return vt},isInterceptionRouteAppPath:function(){return Qi},extractInterceptionRouteInformation:function(){return Zi}});var Xi=Rr(),vt=[\"(..)(..)\",\"(.)\",\"(..)\",\"(...)\"];function Qi(t){return t.split(\"/\").find(e=>vt.find(n=>e.startsWith(n)))!==void 0}function Zi(t){let e,n,i;for(let o of t.split(\"/\"))if(n=vt.find(s=>o.startsWith(s)),n){[e,i]=t.split(n,2);break}if(!e||!n||!i)throw new Error(`Invalid interception route: ${t}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(e=(0,Xi.normalizeAppPath)(e),n){case\"(.)\":e===\"/\"?i=`/${i}`:i=e+\"/\"+i;break;case\"(..)\":if(e===\"/\")throw new Error(`Invalid interception route: ${t}. Cannot use (..) marker at the root level, use (.) instead.`);i=e.split(\"/\").slice(0,-1).concat(i).join(\"/\");break;case\"(...)\":i=\"/\"+i;break;case\"(..)(..)\":let o=e.split(\"/\");if(o.length<=2)throw new Error(`Invalid interception route: ${t}. Cannot use (..)(..) marker at the root level or one level up.`);i=o.slice(0,-2).concat(i).join(\"/\");break;default:throw new Error(\"Invariant: unexpected marker\")}return{interceptingRoute:e,interceptedRoute:i}}});var Er=h(Pt=>{\"use strict\";Object.defineProperty(Pt,\"__esModule\",{value:!0});Object.defineProperty(Pt,\"escapeStringRegexp\",{enumerable:!0,get:function(){return to}});var Ji=/[|\\\\{}()[\\]^$+*?.-]/,eo=/[|\\\\{}()[\\]^$+*?.-]/g;function to(t){return Ji.test(t)?t.replace(eo,\"\\\\$&\"):t}});var Ar=h(kt=>{\"use strict\";Object.defineProperty(kt,\"__esModule\",{value:!0});function ro(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}ro(kt,{getRouteRegex:function(){return Dr},getNamedRouteRegex:function(){return so},getNamedMiddlewareRegex:function(){return lo}});var Or=kr(),Tt=Er(),Cr=lt(),no=\"nxtP\",io=\"nxtI\";function Rt(t){let e=t.startsWith(\"[\")&&t.endsWith(\"]\");e&&(t=t.slice(1,-1));let n=t.startsWith(\"...\");return n&&(t=t.slice(3)),{key:t,repeat:n,optional:e}}function Sr(t){let e=(0,Cr.removeTrailingSlash)(t).slice(1).split(\"/\"),n={},i=1;return{parameterizedRoute:e.map(o=>{let s=Or.INTERCEPTION_ROUTE_MARKERS.find(d=>o.startsWith(d)),f=o.match(/\\[((?:\\[.*\\])|.+)\\]/);if(s&&f){let{key:d,optional:u,repeat:p}=Rt(f[1]);return n[d]={pos:i++,repeat:p,optional:u},\"/\"+(0,Tt.escapeStringRegexp)(s)+\"([^/]+?)\"}else if(f){let{key:d,repeat:u,optional:p}=Rt(f[1]);return n[d]={pos:i++,repeat:u,optional:p},u?p?\"(?:/(.+?))?\":\"/(.+?)\":\"/([^/]+?)\"}else return\"/\"+(0,Tt.escapeStringRegexp)(o)}).join(\"\"),groups:n}}function Dr(t){let{parameterizedRoute:e,groups:n}=Sr(t);return{re:new RegExp(\"^\"+e+\"(?:/)?$\"),groups:n}}function oo(){let t=0;return()=>{let e=\"\",n=++t;for(;n>0;)e+=String.fromCharCode(97+(n-1)%26),n=Math.floor((n-1)/26);return e}}function wr(t){let{getSafeRouteKey:e,segment:n,routeKeys:i,keyPrefix:o}=t,{key:s,optional:f,repeat:d}=Rt(n),u=s.replace(/\\W/g,\"\");o&&(u=\"\"+o+u);let p=!1;return(u.length===0||u.length>30)&&(p=!0),isNaN(parseInt(u.slice(0,1)))||(p=!0),p&&(u=e()),o?i[u]=\"\"+o+s:i[u]=\"\"+s,d?f?\"(?:/(?<\"+u+\">.+?))?\":\"/(?<\"+u+\">.+?)\":\"/(?<\"+u+\">[^/]+?)\"}function jr(t,e){let n=(0,Cr.removeTrailingSlash)(t).slice(1).split(\"/\"),i=oo(),o={};return{namedParameterizedRoute:n.map(s=>{let f=Or.INTERCEPTION_ROUTE_MARKERS.some(u=>s.startsWith(u)),d=s.match(/\\[((?:\\[.*\\])|.+)\\]/);return f&&d?wr({getSafeRouteKey:i,segment:d[1],routeKeys:o,keyPrefix:e?io:void 0}):d?wr({getSafeRouteKey:i,segment:d[1],routeKeys:o,keyPrefix:e?no:void 0}):\"/\"+(0,Tt.escapeStringRegexp)(s)}).join(\"\"),routeKeys:o}}function so(t,e){let n=jr(t,e);return{...Dr(t),namedRegex:\"^\"+n.namedParameterizedRoute+\"(?:/)?$\",routeKeys:n.routeKeys}}function lo(t,e){let{parameterizedRoute:n}=Sr(t),{catchAll:i=!0}=e;if(n===\"/\")return{namedRegex:\"^/\"+(i?\".*\":\"\")+\"$\"};let{namedParameterizedRoute:o}=jr(t,!1),s=i?\"(?:(/.*)?)\":\"\";return{namedRegex:\"^\"+o+s+\"$\"}}});var Ir=h(Et=>{\"use strict\";Object.defineProperty(Et,\"__esModule\",{value:!0});Object.defineProperty(Et,\"interpolateAs\",{enumerable:!0,get:function(){return co}});var ao=xr(),uo=Ar();function co(t,e,n){let i=\"\",o=(0,uo.getRouteRegex)(t),s=o.groups,f=(e!==t?(0,ao.getRouteMatcher)(o)(e):\"\")||n;i=t;let d=Object.keys(s);return d.every(u=>{let p=f[u]||\"\",{repeat:x,optional:S}=s[u],T=\"[\"+(x?\"...\":\"\")+u+\"]\";return S&&(T=(p?\"\":\"/\")+\"[\"+T+\"]\"),x&&!Array.isArray(p)&&(p=[p]),(S||u in f)&&(i=i.replace(T,x?p.map(U=>encodeURIComponent(U)).join(\"/\"):encodeURIComponent(p))||\"/\")})||(i=\"\"),{params:d,result:i}}});var zr=h((G,Wr)=>{\"use strict\";Object.defineProperty(G,\"__esModule\",{value:!0});Object.defineProperty(G,\"resolveHref\",{enumerable:!0,get:function(){return yo}});var fo=Be(),Ur=Ze(),po=ur(),mo=he(),ho=be(),bo=ft(),_o=vr(),go=Ir();function yo(t,e,n){let i,o=typeof e==\"string\"?e:(0,Ur.formatWithValidation)(e),s=o.match(/^[a-zA-Z]{1,}:\\/\\//),f=s?o.slice(s[0].length):o;if((f.split(\"?\")[0]||\"\").match(/(\\/\\/|\\\\)/)){console.error(\"Invalid href '\"+o+\"' passed to next/router in page: '\"+t.pathname+\"'. Repeated forward-slashes (//) or backslashes \\\\ are not valid in the href.\");let u=(0,mo.normalizeRepeatedSlashes)(f);o=(s?s[0]:\"\")+u}if(!(0,bo.isLocalURL)(o))return n?[o]:o;try{i=new URL(o.startsWith(\"#\")?t.asPath:t.pathname,\"http://n\")}catch{i=new URL(\"/\",\"http://n\")}try{let u=new URL(o,i);u.pathname=(0,ho.normalizePathTrailingSlash)(u.pathname);let p=\"\";if((0,_o.isDynamicRoute)(u.pathname)&&u.searchParams&&n){let S=(0,fo.searchParamsToUrlQuery)(u.searchParams),{result:T,params:U}=(0,go.interpolateAs)(u.pathname,u.pathname,S);T&&(p=(0,Ur.formatWithValidation)({pathname:T,hash:u.hash,query:(0,po.omit)(S,U)}))}let x=u.origin===i.origin?u.href.slice(u.origin.length):u.href;return n?[x,p||x]:x}catch{return n?[o]:o}}(typeof G.default==\"function\"||typeof G.default==\"object\"&&G.default!==null)&&typeof G.default.__esModule>\"u\"&&(Object.defineProperty(G.default,\"__esModule\",{value:!0}),Object.assign(G.default,G),Wr.exports=G.default)});var Ot=h(wt=>{\"use strict\";Object.defineProperty(wt,\"__esModule\",{value:!0});Object.defineProperty(wt,\"addPathPrefix\",{enumerable:!0,get:function(){return vo}});var No=Se();function vo(t,e){if(!t.startsWith(\"/\")||!e)return t;let{pathname:n,query:i,hash:o}=(0,No.parsePath)(t);return\"\"+e+n+i+o}});var Lr=h(Ct=>{\"use strict\";Object.defineProperty(Ct,\"__esModule\",{value:!0});Object.defineProperty(Ct,\"addLocale\",{enumerable:!0,get:function(){return Po}});var xo=Ot(),Mr=ct();function Po(t,e,n,i){if(!e||e===n)return t;let o=t.toLowerCase();return!i&&((0,Mr.pathHasPrefix)(o,\"/api\")||(0,Mr.pathHasPrefix)(o,\"/\"+e.toLowerCase()))?t:(0,xo.addPathPrefix)(t,\"/\"+e)}});var Fr=h((V,qr)=>{\"use strict\";Object.defineProperty(V,\"__esModule\",{value:!0});Object.defineProperty(V,\"addLocale\",{enumerable:!0,get:function(){return Ro}});var To=be(),Ro=function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return process.env.__NEXT_I18N_SUPPORT?(0,To.normalizePathTrailingSlash)(Lr().addLocale(t,...n)):t};(typeof V.default==\"function\"||typeof V.default==\"object\"&&V.default!==null)&&typeof V.default.__esModule>\"u\"&&(Object.defineProperty(V.default,\"__esModule\",{value:!0}),Object.assign(V.default,V),qr.exports=V.default)});var Vr=h(St=>{\"use strict\";Object.defineProperty(St,\"__esModule\",{value:!0});Object.defineProperty(St,\"RouterContext\",{enumerable:!0,get:function(){return Gr}});var ko=Oe(),Eo=ko._(de()),Gr=Eo.default.createContext(null);Gr.displayName=\"RouterContext\"});var Br=h(jt=>{\"use client\";\"use strict\";Object.defineProperty(jt,\"__esModule\",{value:!0});function wo(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}wo(jt,{CacheStates:function(){return Dt},AppRouterContext:function(){return $r},LayoutRouterContext:function(){return Hr},GlobalLayoutRouterContext:function(){return Kr},TemplateContext:function(){return Yr}});var Oo=Oe(),De=Oo._(de()),Dt;(function(t){t.LAZY_INITIALIZED=\"LAZYINITIALIZED\",t.DATA_FETCH=\"DATAFETCH\",t.READY=\"READY\"})(Dt||(Dt={}));var $r=De.default.createContext(null),Hr=De.default.createContext(null),Kr=De.default.createContext(null),Yr=De.default.createContext(null);$r.displayName=\"AppRouterContext\",Hr.displayName=\"LayoutRouterContext\",Kr.displayName=\"GlobalLayoutRouterContext\",Yr.displayName=\"TemplateContext\"});var Qr=h(($,Xr)=>{\"use strict\";Object.defineProperty($,\"__esModule\",{value:!0});function Co(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Co($,{requestIdleCallback:function(){return So},cancelIdleCallback:function(){return Do}});var So=typeof self<\"u\"&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(t){let e=Date.now();return self.setTimeout(function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})},1)},Do=typeof self<\"u\"&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(t){return clearTimeout(t)};(typeof $.default==\"function\"||typeof $.default==\"object\"&&$.default!==null)&&typeof $.default.__esModule>\"u\"&&(Object.defineProperty($.default,\"__esModule\",{value:!0}),Object.assign($.default,$),Xr.exports=$.default)});var tn=h((H,en)=>{\"use strict\";Object.defineProperty(H,\"__esModule\",{value:!0});Object.defineProperty(H,\"useIntersection\",{enumerable:!0,get:function(){return Io}});var _e=de(),Zr=Qr(),Jr=typeof IntersectionObserver==\"function\",At=new Map,je=[];function jo(t){let e={root:t.root||null,margin:t.rootMargin||\"\"},n=je.find(f=>f.root===e.root&&f.margin===e.margin),i;if(n&&(i=At.get(n),i))return i;let o=new Map,s=new IntersectionObserver(f=>{f.forEach(d=>{let u=o.get(d.target),p=d.isIntersecting||d.intersectionRatio>0;u&&p&&u(p)})},t);return i={id:e,observer:s,elements:o},je.push(e),At.set(e,i),i}function Ao(t,e,n){let{id:i,observer:o,elements:s}=jo(n);return s.set(t,e),o.observe(t),function(){if(s.delete(t),o.unobserve(t),s.size===0){o.disconnect(),At.delete(i);let d=je.findIndex(u=>u.root===i.root&&u.margin===i.margin);d>-1&&je.splice(d,1)}}}function Io(t){let{rootRef:e,rootMargin:n,disabled:i}=t,o=i||!Jr,[s,f]=(0,_e.useState)(!1),d=(0,_e.useRef)(null),u=(0,_e.useCallback)(x=>{d.current=x},[]);(0,_e.useEffect)(()=>{if(Jr){if(o||s)return;let x=d.current;if(x&&x.tagName)return Ao(x,T=>T&&f(T),{root:e?.current,rootMargin:n})}else if(!s){let x=(0,Zr.requestIdleCallback)(()=>f(!0));return()=>(0,Zr.cancelIdleCallback)(x)}},[o,n,e,s,d.current]);let p=(0,_e.useCallback)(()=>{f(!1)},[]);return[u,s,p]}(typeof H.default==\"function\"||typeof H.default==\"object\"&&H.default!==null)&&typeof H.default.__esModule>\"u\"&&(Object.defineProperty(H.default,\"__esModule\",{value:!0}),Object.assign(H.default,H),en.exports=H.default)});var rn=h(It=>{\"use strict\";Object.defineProperty(It,\"__esModule\",{value:!0});Object.defineProperty(It,\"normalizeLocalePath\",{enumerable:!0,get:function(){return Uo}});function Uo(t,e){let n,i=t.split(\"/\");return(e||[]).some(o=>i[1]&&i[1].toLowerCase()===o.toLowerCase()?(n=o,i.splice(1,1),t=i.join(\"/\")||\"/\",!0):!1),{pathname:t,detectedLocale:n}}});var on=h((K,nn)=>{\"use strict\";Object.defineProperty(K,\"__esModule\",{value:!0});Object.defineProperty(K,\"normalizeLocalePath\",{enumerable:!0,get:function(){return Wo}});var Wo=(t,e)=>process.env.__NEXT_I18N_SUPPORT?rn().normalizeLocalePath(t,e):{pathname:t,detectedLocale:void 0};(typeof K.default==\"function\"||typeof K.default==\"object\"&&K.default!==null)&&typeof K.default.__esModule>\"u\"&&(Object.defineProperty(K.default,\"__esModule\",{value:!0}),Object.assign(K.default,K),nn.exports=K.default)});var sn=h(Ut=>{\"use strict\";Object.defineProperty(Ut,\"__esModule\",{value:!0});Object.defineProperty(Ut,\"detectDomainLocale\",{enumerable:!0,get:function(){return zo}});function zo(t,e,n){if(t){n&&(n=n.toLowerCase());for(let s of t){var i,o;let f=(i=s.domain)==null?void 0:i.split(\":\")[0].toLowerCase();if(e===f||n===s.defaultLocale.toLowerCase()||(o=s.locales)!=null&&o.some(d=>d.toLowerCase()===n))return s}}}});var an=h((Y,ln)=>{\"use strict\";Object.defineProperty(Y,\"__esModule\",{value:!0});Object.defineProperty(Y,\"detectDomainLocale\",{enumerable:!0,get:function(){return Mo}});var Mo=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(process.env.__NEXT_I18N_SUPPORT)return sn().detectDomainLocale(...e)};(typeof Y.default==\"function\"||typeof Y.default==\"object\"&&Y.default!==null)&&typeof Y.default.__esModule>\"u\"&&(Object.defineProperty(Y.default,\"__esModule\",{value:!0}),Object.assign(Y.default,Y),ln.exports=Y.default)});var cn=h((B,un)=>{\"use strict\";Object.defineProperty(B,\"__esModule\",{value:!0});Object.defineProperty(B,\"getDomainLocale\",{enumerable:!0,get:function(){return Fo}});var Lo=be(),qo=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Fo(t,e,n,i){if(process.env.__NEXT_I18N_SUPPORT){let o=on().normalizeLocalePath,s=an().detectDomainLocale,f=e||o(t,n).detectedLocale,d=s(i,void 0,f);if(d){let u=\"http\"+(d.http?\"\":\"s\")+\"://\",p=f===d.defaultLocale?\"\":\"/\"+f;return\"\"+u+d.domain+(0,Lo.normalizePathTrailingSlash)(\"\"+qo+p+t)}return!1}else return!1}(typeof B.default==\"function\"||typeof B.default==\"object\"&&B.default!==null)&&typeof B.default.__esModule>\"u\"&&(Object.defineProperty(B.default,\"__esModule\",{value:!0}),Object.assign(B.default,B),un.exports=B.default)});var fn=h((X,dn)=>{\"use strict\";Object.defineProperty(X,\"__esModule\",{value:!0});Object.defineProperty(X,\"addBasePath\",{enumerable:!0,get:function(){return Ho}});var Go=Ot(),Vo=be(),$o=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Ho(t,e){return(0,Vo.normalizePathTrailingSlash)(process.env.__NEXT_MANUAL_CLIENT_BASE_PATH&&!e?t:(0,Go.addPathPrefix)(t,$o))}(typeof X.default==\"function\"||typeof X.default==\"object\"&&X.default!==null)&&typeof X.default.__esModule>\"u\"&&(Object.defineProperty(X.default,\"__esModule\",{value:!0}),Object.assign(X.default,X),dn.exports=X.default)});var mn=h((Q,pn)=>{\"use strict\";Object.defineProperty(Q,\"__esModule\",{value:!0});function Ko(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Ko(Q,{PrefetchKind:function(){return Wt},ACTION_REFRESH:function(){return Yo},ACTION_NAVIGATE:function(){return Bo},ACTION_RESTORE:function(){return Xo},ACTION_SERVER_PATCH:function(){return Qo},ACTION_PREFETCH:function(){return Zo},ACTION_FAST_REFRESH:function(){return Jo},ACTION_SERVER_ACTION:function(){return es}});var Yo=\"refresh\",Bo=\"navigate\",Xo=\"restore\",Qo=\"server-patch\",Zo=\"prefetch\",Jo=\"fast-refresh\",es=\"server-action\",Wt;(function(t){t.AUTO=\"auto\",t.FULL=\"full\",t.TEMPORARY=\"temporary\"})(Wt||(Wt={}));(typeof Q.default==\"function\"||typeof Q.default==\"object\"&&Q.default!==null)&&typeof Q.default.__esModule>\"u\"&&(Object.defineProperty(Q.default,\"__esModule\",{value:!0}),Object.assign(Q.default,Q),pn.exports=Q.default)});var vn=h((Z,Nn)=>{\"use client\";\"use strict\";Object.defineProperty(Z,\"__esModule\",{value:!0});Object.defineProperty(Z,\"default\",{enumerable:!0,get:function(){return ps}});var ts=Oe(),A=ts._(de()),hn=zr(),yn=ft(),rs=Ze(),ns=he(),is=Fr(),os=Vr(),ss=Br(),ls=tn(),as=cn(),us=fn(),bn=mn(),_n=new Set;function zt(t,e,n,i,o,s){if(typeof window>\"u\"||!s&&!(0,yn.isLocalURL)(e))return;if(!i.bypassPrefetchedCheck){let d=typeof i.locale<\"u\"?i.locale:\"locale\"in t?t.locale:void 0,u=e+\"%\"+n+\"%\"+d;if(_n.has(u))return;_n.add(u)}let f=s?t.prefetch(e,o):t.prefetch(e,n,i);Promise.resolve(f).catch(d=>{throw d})}function cs(t){let n=t.currentTarget.getAttribute(\"target\");return n&&n!==\"_self\"||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&t.nativeEvent.which===2}function ds(t,e,n,i,o,s,f,d,u,p){let{nodeName:x}=t.currentTarget;if(x.toUpperCase()===\"A\"&&(cs(t)||!u&&!(0,yn.isLocalURL)(n)))return;t.preventDefault();let T=()=>{let U=f??!0;\"beforePopState\"in e?e[o?\"replace\":\"push\"](n,i,{shallow:s,locale:d,scroll:U}):e[o?\"replace\":\"push\"](i||n,{forceOptimisticNavigation:!p,scroll:U})};u?A.default.startTransition(T):T()}function gn(t){return typeof t==\"string\"?t:(0,rs.formatUrl)(t)}var fs=A.default.forwardRef(function(e,n){let i,{href:o,as:s,children:f,prefetch:d=null,passHref:u,replace:p,shallow:x,scroll:S,locale:T,onClick:U,onMouseEnter:le,onTouchStart:ge,legacyBehavior:I=!1,...te}=e;i=f,I&&(typeof i==\"string\"||typeof i==\"number\")&&(i=A.default.createElement(\"a\",null,i));let _=A.default.useContext(os.RouterContext),Ie=A.default.useContext(ss.AppRouterContext),z=_??Ie,M=!_,ie=d!==!1,ae=d===null?bn.PrefetchKind.AUTO:bn.PrefetchKind.FULL;{let b=function(g){return new Error(\"Failed prop type: The prop `\"+g.key+\"` expects a \"+g.expected+\" in `<Link>`, but got `\"+g.actual+\"` instead.\"+(typeof window<\"u\"?`\nOpen your browser's console to view the Component stack trace.`:\"\"))};Object.keys({href:!0}).forEach(g=>{if(g===\"href\"){if(e[g]==null||typeof e[g]!=\"string\"&&typeof e[g]!=\"object\")throw b({key:g,expected:\"`string` or `object`\",actual:e[g]===null?\"null\":typeof e[g]})}else{let D=g}}),Object.keys({as:!0,replace:!0,scroll:!0,shallow:!0,passHref:!0,prefetch:!0,locale:!0,onClick:!0,onMouseEnter:!0,onTouchStart:!0,legacyBehavior:!0}).forEach(g=>{let D=typeof e[g];if(g===\"as\"){if(e[g]&&D!==\"string\"&&D!==\"object\")throw b({key:g,expected:\"`string` or `object`\",actual:D})}else if(g===\"locale\"){if(e[g]&&D!==\"string\")throw b({key:g,expected:\"`string`\",actual:D})}else if(g===\"onClick\"||g===\"onMouseEnter\"||g===\"onTouchStart\"){if(e[g]&&D!==\"function\")throw b({key:g,expected:\"`function`\",actual:D})}else if(g===\"replace\"||g===\"scroll\"||g===\"shallow\"||g===\"passHref\"||g===\"prefetch\"||g===\"legacyBehavior\"){if(e[g]!=null&&D!==\"boolean\")throw b({key:g,expected:\"`boolean`\",actual:D})}else{let Pe=g}});let xe=A.default.useRef(!1);e.prefetch&&!xe.current&&!M&&(xe.current=!0,console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\"))}if(M&&!s){let b;if(typeof o==\"string\"?b=o:typeof o==\"object\"&&typeof o.pathname==\"string\"&&(b=o.pathname),b&&b.split(\"/\").some(ee=>ee.startsWith(\"[\")&&ee.endsWith(\"]\")))throw new Error(\"Dynamic href `\"+b+\"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\")}let{href:L,as:O}=A.default.useMemo(()=>{if(!_){let ee=gn(o);return{href:ee,as:s?gn(s):ee}}let[b,ne]=(0,hn.resolveHref)(_,o,!0);return{href:b,as:s?(0,hn.resolveHref)(_,s):ne||b}},[_,o,s]),ye=A.default.useRef(L),Ne=A.default.useRef(O),E;if(I){U&&console.warn('\"onClick\" was passed to <Link> with `href` of `'+o+'` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link'),le&&console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `'+o+'` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');try{E=A.default.Children.only(i)}catch{throw i?new Error(\"Multiple children were passed to <Link> with `href` of `\"+o+\"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\"+(typeof window<\"u\"?` \nOpen your browser's console to view the Component stack trace.`:\"\")):new Error(\"No children were passed to <Link> with `href` of `\"+o+\"` but one child is required https://nextjs.org/docs/messages/link-no-children\")}}else if(i?.type===\"a\")throw new Error(`Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor`);let w=I?E&&typeof E==\"object\"&&E.ref:n,[J,re,fe]=(0,ls.useIntersection)({rootMargin:\"200px\"}),ve=A.default.useCallback(b=>{(Ne.current!==O||ye.current!==L)&&(fe(),Ne.current=O,ye.current=L),J(b),w&&(typeof w==\"function\"?w(b):typeof w==\"object\"&&(w.current=b))},[O,w,L,fe,J]);A.default.useEffect(()=>{},[O,L,re,T,ie,_?.locale,z,M,ae]);let oe={ref:ve,onClick(b){if(!b)throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');!I&&typeof U==\"function\"&&U(b),I&&E.props&&typeof E.props.onClick==\"function\"&&E.props.onClick(b),z&&(b.defaultPrevented||ds(b,z,L,O,p,x,S,T,M,ie))},onMouseEnter(b){!I&&typeof le==\"function\"&&le(b),I&&E.props&&typeof E.props.onMouseEnter==\"function\"&&E.props.onMouseEnter(b),z&&((!ie||!0)&&M||zt(z,L,O,{locale:T,priority:!0,bypassPrefetchedCheck:!0},{kind:ae},M))},onTouchStart(b){!I&&typeof ge==\"function\"&&ge(b),I&&E.props&&typeof E.props.onTouchStart==\"function\"&&E.props.onTouchStart(b),z&&(!ie&&M||zt(z,L,O,{locale:T,priority:!0,bypassPrefetchedCheck:!0},{kind:ae},M))}};if((0,ns.isAbsoluteUrl)(O))oe.href=O;else if(!I||u||E.type===\"a\"&&!(\"href\"in E.props)){let b=typeof T<\"u\"?T:_?.locale,ne=_?.isLocaleDomain&&(0,as.getDomainLocale)(O,b,_?.locales,_?.domainLocales);oe.href=ne||(0,us.addBasePath)((0,is.addLocale)(O,b,_?.defaultLocale))}return I?A.default.cloneElement(E,oe):A.default.createElement(\"a\",{...te,...oe},i)}),ps=fs;(typeof Z.default==\"function\"||typeof Z.default==\"object\"&&Z.default!==null)&&typeof Z.default.__esModule>\"u\"&&(Object.defineProperty(Z.default,\"__esModule\",{value:!0}),Object.assign(Z.default,Z),Nn.exports=Z.default)});var Pn=h((Qs,xn)=>{xn.exports=vn()});var gs={};ei(gs,{default:()=>_s,faqData:()=>hs,frontmatter:()=>ms});var a=Jt(nr());function He({children:t,quizSlug:e}){return React.createElement(\"article\",{className:\"max-w-4xl mx-auto text-gray-900\"},React.createElement(\"div\",{className:\"prose prose-lg max-w-none text-gray-900 [&>*]:text-gray-900\"},t))}var Tn=Jt(Pn());function Ae({task:t,href:e}){let n=e||`/quiz/${t}`;return React.createElement(\"div\",{className:\"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\"},React.createElement(\"div\",{className:\"text-center\"},React.createElement(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-2\"},\"Not sure which AI fits your workflow?\"),React.createElement(\"p\",{className:\"text-gray-600 mb-4\"},\"Take our 30-second quiz to get a personalized recommendation\"),React.createElement(Tn.default,{href:n,className:\"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\"},\"Take the \",t.charAt(0).toUpperCase()+t.slice(1),\" Quiz \\u2192\")))}var ms={title:\"Best AI for Writing (2025) \\u2014 Claude 3.5 vs GPT-4o, Gemini & more\",description:\"Comprehensive comparison of Claude 3.5, GPT-4o, Gemini, Perplexity & Grok for bloggers, students, and marketers. Find your perfect AI writing assistant.\",slug:\"best-ai-for-writing-2025-claude-3-5-vs-gpt-4o-gemini-more\",template:\"pillar\",cluster:\"text\",priority:\"Low\",lastUpdated:\"2025-07-22\"},hs=[{q:\"What is the best free AI for writing?\",a:\"Perplexity offers the strongest free tier for fact-based writing, while Claude's free tier is best for creative prose.\"},{q:\"Can Google penalise AI-generated content?\",a:\"Google ranks helpful content regardless of how it's produced; thin or spammy AI text can be penalised.\"},{q:\"What's a context window and why does it matter?\",a:\"It's the amount of text an AI can 'remember'. Bigger windows (e.g., Claude's 200 k tokens) keep long documents coherent.\"},{q:\"Which AI is best for creative writing?\",a:\"Claude 3.5 Sonnet consistently produces the most human-like, nuanced prose.\"},{q:\"Which AI provides reliable citations?\",a:\"Perplexity Pro surfaces sources and clickable references by default.\"},{q:\"Is GPT-4o still king in 2025?\",a:\"It's the best all-rounder, but Claude wins on style and Perplexity on accuracy.\"}];function Rn(t){let e=Object.assign({h2:\"h2\",p:\"p\",strong:\"strong\",ul:\"ul\",li:\"li\",hr:\"hr\",em:\"em\",blockquote:\"blockquote\",h3:\"h3\",a:\"a\"},t.components);return(0,a.jsxDEV)(He,{quizSlug:\"writing\",children:[(0,a.jsxDEV)(Ae,{task:\"writing\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:16,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\"Who are you writing for ?\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:18,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.strong,{children:\"The Blogger\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:20,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:20,columnNumber:1},this),(0,a.jsxDEV)(e.ul,{children:[`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Pain\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:22,columnNumber:3},this),\" \\u2013 needs original long-form content that won't feel robotic or earn an SEO penalty.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:22,columnNumber:1},this),`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Ideal output\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:23,columnNumber:3},this),\" \\u2013 an AI blog generator that keeps a consistent tone.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Killer feature\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:24,columnNumber:3},this),\" \\u2013 a huge context window to track details across thousands of words.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:24,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:22,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.strong,{children:\"The Student\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:26,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:26,columnNumber:1},this),(0,a.jsxDEV)(e.ul,{children:[`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Pain\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:28,columnNumber:3},this),\" \\u2013 must research, structure, and cite accurately while avoiding plagiarism.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:28,columnNumber:1},this),`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Ideal output\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:29,columnNumber:3},this),\" \\u2013 an AI essay writer that returns verifiable facts with citations.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Killer feature\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:30,columnNumber:3},this),\" \\u2013 can ingest PDFs and analyse them directly.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:30,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:28,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.strong,{children:\"The Marketer\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:32,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:32,columnNumber:1},this),(0,a.jsxDEV)(e.ul,{children:[`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Pain\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:34,columnNumber:3},this),\" \\u2013 high-volume, mixed-format content plus brand-voice consistency.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:34,columnNumber:1},this),`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Ideal output\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:35,columnNumber:3},this),\" \\u2013 a tool that plugs into Google Workspace and accelerates campaigns.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:35,columnNumber:1},this),`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Killer feature\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:36,columnNumber:3},this),\" \\u2013 analyses spreadsheet data and builds project plans.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:36,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:34,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:38,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:'A market of specialists, not one \"best\" model'},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:40,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[\"Perplexity is an \",(0,a.jsxDEV)(e.strong,{children:\"answer engine\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:42,columnNumber:18},this),\", Claude a \",(0,a.jsxDEV)(e.strong,{children:\"creative prose specialist\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:42,columnNumber:46},this),\", and Gemini a \",(0,a.jsxDEV)(e.strong,{children:\"productivity layer\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:42,columnNumber:90},this),\" for Docs, Sheets, and Gmail. The takeaway: \",(0,a.jsxDEV)(e.em,{children:\"choose by task\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:42,columnNumber:156},this),\", not by raw IQ.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:42,columnNumber:1},this),(0,a.jsxDEV)(e.blockquote,{children:[`\n`,(0,a.jsxDEV)(e.h3,{children:\"\\u26A0 Premium trap\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:44,columnNumber:3},this),`\n`,(0,a.jsxDEV)(e.p,{children:`The $20 Pro tiers are no longer the cutting edge. OpenAI, Anthropic, Perplexity, and xAI now sell $100\\u2013$300 \"Max / Heavy\" plans aimed at enterprises. For most writers the $20 tier remains the ROI sweet-spot, but know you're not buying the absolute top model.`},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:45,columnNumber:3},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:44,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:47,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\"2025 AI-writer scorecard\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:49,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[`| Model | Best for (archetype) | Stand-out feature | Context window | Free tier | Pro price | Key limitation |\n|-------|----------------------|-------------------|---------------|-----------|-----------|----------------|\n| `,(0,a.jsxDEV)(e.strong,{children:\"Claude 3.5 Sonnet\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:53,columnNumber:3},this),` | Creative writing (Poet) | \"Artifacts\" live editor | 200 k tokens | Yes (daily cap) | $20 | No native real-time web search |\n| `,(0,a.jsxDEV)(e.strong,{children:\"GPT-4o\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:54,columnNumber:3},this),` | Generalist (Polymath) | Multimodal + Custom GPTs | 128 k tokens | Yes (cap) | $20 | Output can feel robotic |\n| `,(0,a.jsxDEV)(e.strong,{children:\"Gemini Advanced\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:55,columnNumber:3},this),` | Productivity (Producer) | Deep Workspace integration | 1 M + tokens | Yes (std) | $19.99 | Creative flair weaker than Claude |\n| `,(0,a.jsxDEV)(e.strong,{children:\"Perplexity Pro\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:56,columnNumber:3},this),` | Research (Professor) | Clickable citations, Deep Research | \\u2014 | Yes (cap) | $20 | Not a creative writer |\n| `,(0,a.jsxDEV)(e.strong,{children:\"Grok\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:57,columnNumber:3},this),\" | Real-time insights (Provocateur) | Live X / Twitter data | \\u2014 | Yes (cap) | $30 | Pricey; edgy tone not for all |\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:51,columnNumber:1},this),(0,a.jsxDEV)(\"div\",{style:{textAlign:\"right\",fontSize:\"0.9rem\"},children:(0,a.jsxDEV)(\"a\",{href:\"/export/scorecard.csv\",children:\"Export to Sheets \\u2192\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:59,columnNumber:52},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:59,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:61,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\"Speed test \\u26A1\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:63,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.em,{children:\"[Speed comparison GIF placeholder - GPT-4o vs Claude 3.5 vs Gemini]\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:65,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:65,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\"GPT-4o starts generating sooner and streams tokens faster, a win for brainstorms and quick Q & A. Claude is ~2\\xD7 faster than its predecessor Opus but still trails GPT-4o on sheer responsiveness.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:67,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:69,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\"Deep-dive profiles\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:71,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\"Claude 3.5 Sonnet \\u2014 \",(0,a.jsxDEV)(e.em,{children:\"the creative wordsmith\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:73,columnNumber:25},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:73,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\"Strengths.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:75,columnNumber:1},this),` Thoughtful, expressive prose; 200 k-token context; \"Artifacts\" side-panel for iterative editing.\n`,(0,a.jsxDEV)(e.strong,{children:\"Weaknesses.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:76,columnNumber:1},this),` No built-in web browsing; free tier message cap.\n`,(0,a.jsxDEV)(e.em,{children:[\"Read the full \",(0,a.jsxDEV)(e.a,{href:\"/claude-3-5-for-blogging-review\",children:\"Claude 3.5 blogging review\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:77,columnNumber:16},this),\".\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:77,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:75,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:79,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\"GPT-4o \\u2014 \",(0,a.jsxDEV)(e.em,{children:\"the versatile all-rounder\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:81,columnNumber:14},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:81,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:`Handles writing, code, data and images in one chat. Custom GPTs unlock niche workflows.\nCriticisms: increasing verbosity, smaller window than Claude, privacy concerns.`},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:83,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:86,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\"Gemini Advanced \\u2014 \",(0,a.jsxDEV)(e.em,{children:\"the integrated productivity engine\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:88,columnNumber:23},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:88,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[`Native in Docs, Sheets, Gmail. Perfect for campaign tables, email summarising, Drive file look-ups.\nDeep dive: `,(0,a.jsxDEV)(e.a,{href:\"/gemini-advanced-for-marketers-guide\",children:\"Gemini for marketers\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:91,columnNumber:12},this),\".\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:90,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:93,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\"Perplexity Pro \\u2014 \",(0,a.jsxDEV)(e.em,{children:\"the research powerhouse\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:95,columnNumber:22},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:95,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[`Delivers answers with numbered citations; \"Deep Research\" builds exhaustive reports.\nGuide: `,(0,a.jsxDEV)(e.a,{href:\"/how-to-use-perplexity-for-academic-research\",children:\"How to use Perplexity for academic research\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:98,columnNumber:8},this),\".\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:97,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:100,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\"Grok \\u2014 \",(0,a.jsxDEV)(e.em,{children:\"the real-time provocateur\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:102,columnNumber:12},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:102,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\"Live social-media pulse plus a snarky attitude. Great for trend analysts, overkill for everyday writing.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:104,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:106,columnNumber:1},this),(0,a.jsxDEV)(Ae,{task:\"writing\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:108,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:110,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:14,columnNumber:1},this)}function bs(t={}){let{wrapper:e}=t.components||{};return e?(0,a.jsxDEV)(e,Object.assign({},t,{children:(0,a.jsxDEV)(Rn,t,void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\"},this):Rn(t)}var _s=bs;return ti(gs);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "pillars/best-ai-for-writing.mdx", "_raw": {"sourceFilePath": "pillars/best-ai-for-writing.mdx", "sourceFileName": "best-ai-for-writing.mdx", "sourceFileDir": "pillars", "contentType": "mdx", "flattenedPath": "pillars/best-ai-for-writing"}, "type": "<PERSON><PERSON>", "url": "/best-ai-for-writing-2025-claude-3-5-vs-gpt-4o-gemini-more", "slugFromPath": "pillars/best-ai-for-writing"}]