{"cacheItemsMap": {"pillars/best-ai-for-coding.mdx": {"document": {"title": "Best AI for Coding & Debugging (2025) — GPT-4o vs Claude 3.5, GitHub Copilot & more", "description": "Developer-focused comparison of GPT-4o, Claude 3.5 Sonnet, GitHub Copilot, and Replit Ghostwriter. Find your perfect AI coding assistant.", "slug": "best-ai-for-coding-2025-gpt-4o-vs-claude-github-copilot-more", "cluster": "code", "template": "pillar", "priority": "High", "lastUpdated": "2025-07-22", "body": {"raw": "\nimport PillarLayout from \"@/templates/PillarLayout\"\nimport Quiz<PERSON><PERSON> from \"@/components/QuizCta\"\n\n<PillarLayout quizSlug=\"coding\">\n\n<QuizCta task=\"coding\" />\n\n## The AI Coding Landscape in 2025\n\nThe conversation around AI coding assistants has moved far beyond simple autocomplete. In 2025, these tools are powerful collaborators capable of architecting systems, debugging multi-file repositories, and accelerating development cycles. But the fragmented market means the \"best\" AI is no longer a simple choice. The right tool depends entirely on your specific needs: low latency to maintain flow state, a massive context window for complex codebases, a deep plug-in ecosystem for your existing workflow, and robust licensing for enterprise security.\n\nThis guide provides a developer-focused comparison of the top contenders—GPT-4o, Claude 3.5 Sonnet, GitHub Copilot, and Replit Ghostwriter—to help you select the right AI co-pilot for your next project.\n\n---\n\n## The AI Coder's Scorecard: Specs at a Glance\n\nFor developers, specs matter. This chart breaks down the key models by what you care about most: cost, context, and core strengths.\n\n| Model | Pricing (per user/month) | Context Window | Key Strength / Ecosystem |\n|-------|--------------------------|----------------|--------------------------|\n| **GPT-4o** | ~$20 (API is usage-based) | 128k tokens | Versatility; a powerful \"second brain\" for logic and algorithms. |\n| **Claude 3.5 Sonnet** | ~$20 (API is usage-based) | 200k tokens | Massive context for codebase analysis and complex refactoring. |\n| **GitHub Copilot** | $19 (Business) / $39 (Enterprise) | Varies (uses GPT-4) | Deep integration with GitHub, VS Code, and the PR lifecycle. |\n| **Replit Ghostwriter** | $20 (Pro) / $50 (Teams) | Varies | Native to the Replit cloud IDE for seamless prototyping. |\n\n<div style={{textAlign:'right',fontSize:'0.9rem'}}><a href=\"/export/coding-scorecard.csv\">Export to Sheets →</a></div>\n\n---\n\n## The Code Challenge: Simple Bugs vs. High-Context Flaws\n\nNot all bugs are created equal. Some are simple typos, while others are subtle logical flaws that hide deep within a large codebase. We tested the leading models with two distinct challenges to see where they shine and where they falter.\n\n### Snippet 1: The Flawless Fix\n\nThis simple Python function is meant to calculate the total price of items in a cart but has a common off-by-one error.\n\n**Buggy Code:**\n\n```python\ndef calculate_cart_total(prices):\n  total = 0\n  # Bug: range stops before the last index\n  for i in range(len(prices) - 1):\n    total += prices[i]\n  return total\n\ncart = [10, 25, 15, 5]\nprint(f\"Total: ${calculate_cart_total(cart)}\") \n# Expected output: $55\n# Actual output: $50\n```\n\n**Result:** Every model tested—GPT-4o, Claude, Copilot, and Ghostwriter—fixed this instantly. They correctly identified that the loop failed to include the last item and adjusted `range(len(prices) - 1)` to `range(len(prices))`. This is the table-stakes capability you should expect from any modern AI code generator.\n\n### Snippet 2: The High-Context Challenge\n\nThis is where premium models prove their worth. The bug here is subtle. A utility function `process_data` incorrectly uses a global `TRANSACTION_FEE` variable, but this is only apparent when you see how `process_data` is called by another function that has already applied a separate, regional tax. Only an AI that can hold the entire call stack in its context can spot the double charge.\n\n**Buggy Code (in a large file):**\n\n```javascript\n// Defined 500 lines earlier...\nconst TRANSACTION_FEE = 0.02; // 2% processing fee\n\nfunction process_data(items) {\n  let subtotal = items.reduce((acc, item) => acc + item.price, 0);\n  // Bug: This fee is applied redundantly, as the calling function handles taxes.\n  return subtotal * (1 + TRANSACTION_FEE); \n}\n\n// ... much later in the file ...\n\nfunction checkout_for_region(cart, region_config) {\n  let regional_total = cart.reduce((acc, item) => acc + item.price, 0);\n  \n  // Apply regional tax correctly\n  regional_total *= (1 + region_config.tax_rate);\n\n  // Send to processing, unaware that it adds another fee\n  const final_price = process_data(cart); // Should pass regional_total\n  \n  console.log(`Final price is: ${final_price.toFixed(2)}`);\n}\n```\n\n**Result:**\n\n- **Lower-Context Models** typically suggest fixing `process_data` in isolation, perhaps by adding a parameter to toggle the fee. They miss the reason it's wrong—the redundant call inside `checkout_for_region`.\n\n- **High-Context Models** (Claude 3.5 Sonnet & GPT-4o) excelled. They identified the core issue: `checkout_for_region` performs its own calculation and then calls `process_data` with the original cart, causing a redundant calculation and an extra fee. Claude, in particular, suggested refactoring `checkout_for_region` to pass the `regional_total` into `process_data` and removing the fee logic from `process_data` entirely, demonstrating a deep understanding of the entire file's logic.\n\n---\n\n## The Enterprise Developer's Checklist\n\nFor teams, choosing an AI coding assistant involves more than just performance—it's about security, licensing, and integration. Before committing, run your choice through this permissions checklist.\n\n☐ **Data Privacy & Training**: Does the provider offer a zero-retention policy, guaranteeing your proprietary code is never used for training their models? (Look for Enterprise or Business tiers).\n\n☐ **Licensing & Indemnification**: Are the terms clear about the ownership of AI-generated code? Does the provider (like GitHub Copilot) offer intellectual property indemnification to protect your company from potential lawsuits?\n\n☐ **Seat Management & SSO**: Can you manage user licenses from a central dashboard and integrate with your existing Single Sign-On (SSO) solution for secure access?\n\n☐ **Security Compliance**: Is the tool compliant with industry standards like SOC 2 Type 2? This is non-negotiable for most enterprise environments.\n\n☐ **IDE & Toolchain Integration**: Does it offer first-party extensions for your team's preferred IDEs (VS Code, JetBrains) and version control systems? Seamless integration is key to adoption.\n\n---\n\n## Deep-dive profiles\n\n### GPT-4o — _the versatile problem-solver_\n\n**Strengths.** Excellent logical reasoning; handles multiple programming languages; strong algorithmic thinking.\n**Weaknesses.** Smaller context window than Claude; can be verbose in explanations.\n*Perfect for: General development, algorithm design, multi-language projects.*\n\n### Claude 3.5 Sonnet — _the codebase analyst_\n\n**Strengths.** Massive 200k token context; excellent at understanding large file relationships; thoughtful refactoring suggestions.\n**Weaknesses.** No native IDE integration yet; API-only access.\n*Perfect for: Large codebase analysis, complex refactoring, architectural decisions.*\n\n### GitHub Copilot — _the workflow integrator_\n\n**Strengths.** Seamless VS Code integration; understands Git context; PR and issue integration.\n**Weaknesses.** Limited to GitHub ecosystem; enterprise pricing can be steep.\n*Perfect for: GitHub-based teams, VS Code users, integrated development workflows.*\n\n### Replit Ghostwriter — _the rapid prototyper_\n\n**Strengths.** Instant deployment; browser-based development; great for learning and experimentation.\n**Weaknesses.** Limited to Replit environment; less suitable for complex enterprise projects.\n*Perfect for: Rapid prototyping, educational projects, web-based development.*\n\n---\n\n<QuizCta task=\"coding\" />\n\n---\n\nexport const faqData = [\n  { q: \"What's the cheapest AI coder?\", a: \"For free options, GitHub Copilot offers free access for students and open-source contributors. For paid plans, most AI coding assistants are around $20/month, with GitHub Copilot Business at $19/month being slightly cheaper.\" },\n  { q: \"Can AI write a full application?\", a: \"While AI can generate significant portions of an application, including boilerplate, functions, and UI components, it cannot yet write a complete, production-ready application from a single prompt without human supervision. It excels as a 'co-pilot' for assistance.\" },\n  { q: \"Is GPT-4o good for debugging complex code?\", a: \"Yes, GPT-4o is excellent for debugging complex code due to its strong logical reasoning. However, for extremely large codebases, Claude 3.5 Sonnet's larger context window may have an advantage for understanding file relationships.\" },\n  { q: \"Does GitHub Copilot steal your code?\", a: \"No, GitHub Copilot does not 'steal' your code. For enterprise users, GitHub has a strict policy that private code is not used to train public models. Enterprise licenses include IP indemnification for legal protection.\" },\n  { q: \"Which AI is best for Python development?\", a: \"All major AI coding assistants handle Python well. GPT-4o excels at algorithmic problems, Claude 3.5 is great for large Python projects, and GitHub Copilot offers the best IDE integration for Python development.\" },\n  { q: \"Can AI help with code reviews?\", a: \"Yes, AI can assist with code reviews by identifying potential bugs, suggesting improvements, and checking for best practices. GitHub Copilot integrates directly with PR workflows, while Claude 3.5's large context window is excellent for reviewing entire files.\" }\n]\n\n</PillarLayout>\n", "code": "var Component=(()=>{var Yr=Object.create;var we=Object.defineProperty;var Xr=Object.getOwnPropertyDescriptor;var Qr=Object.getOwnPropertyNames;var Zr=Object.getPrototypeOf,Jr=Object.prototype.hasOwnProperty;var h=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),ei=(t,e)=>{for(var r in e)we(t,r,{get:e[r],enumerable:!0})},Zt=(t,e,r,i)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let o of Qr(e))!Jr.call(t,o)&&o!==r&&we(t,o,{get:()=>e[o],enumerable:!(i=Xr(e,o))||i.enumerable});return t};var Jt=(t,e,r)=>(r=t!=null?Yr(Zr(t)):{},Zt(e||!t||!t.__esModule?we(r,\"default\",{value:t,enumerable:!0}):r,t)),ti=t=>Zt(we({},\"__esModule\",{value:!0}),t);var de=h((Ns,en)=>{en.exports=React});var tn=h(Ve=>{\"use strict\";(function(){\"use strict\";var t=de(),e=Symbol.for(\"react.element\"),r=Symbol.for(\"react.portal\"),i=Symbol.for(\"react.fragment\"),o=Symbol.for(\"react.strict_mode\"),s=Symbol.for(\"react.profiler\"),f=Symbol.for(\"react.provider\"),d=Symbol.for(\"react.context\"),u=Symbol.for(\"react.forward_ref\"),p=Symbol.for(\"react.suspense\"),v=Symbol.for(\"react.suspense_list\"),S=Symbol.for(\"react.memo\"),P=Symbol.for(\"react.lazy\"),U=Symbol.for(\"react.offscreen\"),ae=Symbol.iterator,ge=\"@@iterator\";function A(n){if(n===null||typeof n!=\"object\")return null;var l=ae&&n[ae]||n[ge];return typeof l==\"function\"?l:null}var te=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function _(n){{for(var l=arguments.length,c=new Array(l>1?l-1:0),m=1;m<l;m++)c[m-1]=arguments[m];Ae(\"error\",n,c)}}function Ae(n,l,c){{var m=te.ReactDebugCurrentFrame,x=m.getStackAddendum();x!==\"\"&&(l+=\"%s\",c=c.concat([x]));var T=c.map(function(N){return String(N)});T.unshift(\"Warning: \"+l),Function.prototype.apply.call(console[n],console,T)}}var z=!1,M=!1,ie=!1,le=!1,L=!1,O;O=Symbol.for(\"react.module.reference\");function ye(n){return!!(typeof n==\"string\"||typeof n==\"function\"||n===i||n===s||L||n===o||n===p||n===v||le||n===U||z||M||ie||typeof n==\"object\"&&n!==null&&(n.$$typeof===P||n.$$typeof===S||n.$$typeof===f||n.$$typeof===d||n.$$typeof===u||n.$$typeof===O||n.getModuleId!==void 0))}function Ne(n,l,c){var m=n.displayName;if(m)return m;var x=l.displayName||l.name||\"\";return x!==\"\"?c+\"(\"+x+\")\":c}function R(n){return n.displayName||\"Context\"}function w(n){if(n==null)return null;if(typeof n.tag==\"number\"&&_(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof n==\"function\")return n.displayName||n.name||null;if(typeof n==\"string\")return n;switch(n){case i:return\"Fragment\";case r:return\"Portal\";case s:return\"Profiler\";case o:return\"StrictMode\";case p:return\"Suspense\";case v:return\"SuspenseList\"}if(typeof n==\"object\")switch(n.$$typeof){case d:var l=n;return R(l)+\".Consumer\";case f:var c=n;return R(c._context)+\".Provider\";case u:return Ne(n,n.render,\"ForwardRef\");case S:var m=n.displayName||null;return m!==null?m:w(n.type)||\"Memo\";case P:{var x=n,T=x._payload,N=x._init;try{return w(N(T))}catch{return null}}}return null}var J=Object.assign,ne=0,fe,xe,oe,b,re,ee,Ue;function We(){}We.__reactDisabledLog=!0;function ve(){{if(ne===0){fe=console.log,xe=console.info,oe=console.warn,b=console.error,re=console.group,ee=console.groupCollapsed,Ue=console.groupEnd;var n={configurable:!0,enumerable:!0,value:We,writable:!0};Object.defineProperties(console,{info:n,log:n,warn:n,error:n,group:n,groupCollapsed:n,groupEnd:n})}ne++}}function g(){{if(ne--,ne===0){var n={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:J({},n,{value:fe}),info:J({},n,{value:xe}),warn:J({},n,{value:oe}),error:J({},n,{value:b}),group:J({},n,{value:re}),groupCollapsed:J({},n,{value:ee}),groupEnd:J({},n,{value:Ue})})}ne<0&&_(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var D=te.ReactCurrentDispatcher,Te;function Pe(n,l,c){{if(Te===void 0)try{throw Error()}catch(x){var m=x.stack.trim().match(/\\n( *(at )?)/);Te=m&&m[1]||\"\"}return`\n`+Te+n}}var ze=!1,Ee;{var kr=typeof WeakMap==\"function\"?WeakMap:Map;Ee=new kr}function Mt(n,l){if(!n||ze)return\"\";{var c=Ee.get(n);if(c!==void 0)return c}var m;ze=!0;var x=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var T;T=D.current,D.current=null,ve();try{if(l){var N=function(){throw Error()};if(Object.defineProperty(N.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(N,[])}catch(j){m=j}Reflect.construct(n,[],N)}else{try{N.call()}catch(j){m=j}n.call(N.prototype)}}else{try{throw Error()}catch(j){m=j}n()}}catch(j){if(j&&m&&typeof j.stack==\"string\"){for(var y=j.stack.split(`\n`),C=m.stack.split(`\n`),E=y.length-1,k=C.length-1;E>=1&&k>=0&&y[E]!==C[k];)k--;for(;E>=1&&k>=0;E--,k--)if(y[E]!==C[k]){if(E!==1||k!==1)do if(E--,k--,k<0||y[E]!==C[k]){var W=`\n`+y[E].replace(\" at new \",\" at \");return n.displayName&&W.includes(\"<anonymous>\")&&(W=W.replace(\"<anonymous>\",n.displayName)),typeof n==\"function\"&&Ee.set(n,W),W}while(E>=1&&k>=0);break}}}finally{ze=!1,D.current=T,g(),Error.prepareStackTrace=x}var ce=n?n.displayName||n.name:\"\",se=ce?Pe(ce):\"\";return typeof n==\"function\"&&Ee.set(n,se),se}function Rr(n,l,c){return Mt(n,!1)}function wr(n){var l=n.prototype;return!!(l&&l.isReactComponent)}function ke(n,l,c){if(n==null)return\"\";if(typeof n==\"function\")return Mt(n,wr(n));if(typeof n==\"string\")return Pe(n);switch(n){case p:return Pe(\"Suspense\");case v:return Pe(\"SuspenseList\")}if(typeof n==\"object\")switch(n.$$typeof){case u:return Rr(n.render);case S:return ke(n.type,l,c);case P:{var m=n,x=m._payload,T=m._init;try{return ke(T(x),l,c)}catch{}}}return\"\"}var pe=Object.prototype.hasOwnProperty,Lt={},qt=te.ReactDebugCurrentFrame;function Re(n){if(n){var l=n._owner,c=ke(n.type,n._source,l?l.type:null);qt.setExtraStackFrame(c)}else qt.setExtraStackFrame(null)}function Or(n,l,c,m,x){{var T=Function.call.bind(pe);for(var N in n)if(T(n,N)){var y=void 0;try{if(typeof n[N]!=\"function\"){var C=Error((m||\"React class\")+\": \"+c+\" type `\"+N+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof n[N]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw C.name=\"Invariant Violation\",C}y=n[N](l,N,m,c,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(E){y=E}y&&!(y instanceof Error)&&(Re(x),_(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",m||\"React class\",c,N,typeof y),Re(null)),y instanceof Error&&!(y.message in Lt)&&(Lt[y.message]=!0,Re(x),_(\"Failed %s type: %s\",c,y.message),Re(null))}}}var Cr=Array.isArray;function Me(n){return Cr(n)}function Sr(n){{var l=typeof Symbol==\"function\"&&Symbol.toStringTag,c=l&&n[Symbol.toStringTag]||n.constructor.name||\"Object\";return c}}function Dr(n){try{return Ft(n),!1}catch{return!0}}function Ft(n){return\"\"+n}function Gt(n){if(Dr(n))return _(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",Sr(n)),Ft(n)}var me=te.ReactCurrentOwner,jr={key:!0,ref:!0,__self:!0,__source:!0},Ht,Vt,Le;Le={};function Ir(n){if(pe.call(n,\"ref\")){var l=Object.getOwnPropertyDescriptor(n,\"ref\").get;if(l&&l.isReactWarning)return!1}return n.ref!==void 0}function Ar(n){if(pe.call(n,\"key\")){var l=Object.getOwnPropertyDescriptor(n,\"key\").get;if(l&&l.isReactWarning)return!1}return n.key!==void 0}function Ur(n,l){if(typeof n.ref==\"string\"&&me.current&&l&&me.current.stateNode!==l){var c=w(me.current.type);Le[c]||(_('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',w(me.current.type),n.ref),Le[c]=!0)}}function Wr(n,l){{var c=function(){Ht||(Ht=!0,_(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",l))};c.isReactWarning=!0,Object.defineProperty(n,\"key\",{get:c,configurable:!0})}}function zr(n,l){{var c=function(){Vt||(Vt=!0,_(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",l))};c.isReactWarning=!0,Object.defineProperty(n,\"ref\",{get:c,configurable:!0})}}var Mr=function(n,l,c,m,x,T,N){var y={$$typeof:e,type:n,key:l,ref:c,props:N,_owner:T};return y._store={},Object.defineProperty(y._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(y,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:m}),Object.defineProperty(y,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:x}),Object.freeze&&(Object.freeze(y.props),Object.freeze(y)),y};function Lr(n,l,c,m,x){{var T,N={},y=null,C=null;c!==void 0&&(Gt(c),y=\"\"+c),Ar(l)&&(Gt(l.key),y=\"\"+l.key),Ir(l)&&(C=l.ref,Ur(l,x));for(T in l)pe.call(l,T)&&!jr.hasOwnProperty(T)&&(N[T]=l[T]);if(n&&n.defaultProps){var E=n.defaultProps;for(T in E)N[T]===void 0&&(N[T]=E[T])}if(y||C){var k=typeof n==\"function\"?n.displayName||n.name||\"Unknown\":n;y&&Wr(N,k),C&&zr(N,k)}return Mr(n,y,C,x,m,me.current,N)}}var qe=te.ReactCurrentOwner,$t=te.ReactDebugCurrentFrame;function ue(n){if(n){var l=n._owner,c=ke(n.type,n._source,l?l.type:null);$t.setExtraStackFrame(c)}else $t.setExtraStackFrame(null)}var Fe;Fe=!1;function Ge(n){return typeof n==\"object\"&&n!==null&&n.$$typeof===e}function Bt(){{if(qe.current){var n=w(qe.current.type);if(n)return`\n\nCheck the render method of \\``+n+\"`.\"}return\"\"}}function qr(n){{if(n!==void 0){var l=n.fileName.replace(/^.*[\\\\\\/]/,\"\"),c=n.lineNumber;return`\n\nCheck your code at `+l+\":\"+c+\".\"}return\"\"}}var Kt={};function Fr(n){{var l=Bt();if(!l){var c=typeof n==\"string\"?n:n.displayName||n.name;c&&(l=`\n\nCheck the top-level render call using <`+c+\">.\")}return l}}function Yt(n,l){{if(!n._store||n._store.validated||n.key!=null)return;n._store.validated=!0;var c=Fr(l);if(Kt[c])return;Kt[c]=!0;var m=\"\";n&&n._owner&&n._owner!==qe.current&&(m=\" It was passed a child from \"+w(n._owner.type)+\".\"),ue(n),_('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',c,m),ue(null)}}function Xt(n,l){{if(typeof n!=\"object\")return;if(Me(n))for(var c=0;c<n.length;c++){var m=n[c];Ge(m)&&Yt(m,l)}else if(Ge(n))n._store&&(n._store.validated=!0);else if(n){var x=A(n);if(typeof x==\"function\"&&x!==n.entries)for(var T=x.call(n),N;!(N=T.next()).done;)Ge(N.value)&&Yt(N.value,l)}}}function Gr(n){{var l=n.type;if(l==null||typeof l==\"string\")return;var c;if(typeof l==\"function\")c=l.propTypes;else if(typeof l==\"object\"&&(l.$$typeof===u||l.$$typeof===S))c=l.propTypes;else return;if(c){var m=w(l);Or(c,n.props,\"prop\",m,n)}else if(l.PropTypes!==void 0&&!Fe){Fe=!0;var x=w(l);_(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",x||\"Unknown\")}typeof l.getDefaultProps==\"function\"&&!l.getDefaultProps.isReactClassApproved&&_(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function Hr(n){{for(var l=Object.keys(n.props),c=0;c<l.length;c++){var m=l[c];if(m!==\"children\"&&m!==\"key\"){ue(n),_(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",m),ue(null);break}}n.ref!==null&&(ue(n),_(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),ue(null))}}var Qt={};function Vr(n,l,c,m,x,T){{var N=ye(n);if(!N){var y=\"\";(n===void 0||typeof n==\"object\"&&n!==null&&Object.keys(n).length===0)&&(y+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var C=qr(x);C?y+=C:y+=Bt();var E;n===null?E=\"null\":Me(n)?E=\"array\":n!==void 0&&n.$$typeof===e?(E=\"<\"+(w(n.type)||\"Unknown\")+\" />\",y=\" Did you accidentally export a JSX literal instead of a component?\"):E=typeof n,_(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",E,y)}var k=Lr(n,l,c,x,T);if(k==null)return k;if(N){var W=l.children;if(W!==void 0)if(m)if(Me(W)){for(var ce=0;ce<W.length;ce++)Xt(W[ce],n);Object.freeze&&Object.freeze(W)}else _(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else Xt(W,n)}if(pe.call(l,\"key\")){var se=w(n),j=Object.keys(l).filter(function(Kr){return Kr!==\"key\"}),He=j.length>0?\"{key: someKey, \"+j.join(\": ..., \")+\": ...}\":\"{key: someKey}\";if(!Qt[se+He]){var Br=j.length>0?\"{\"+j.join(\": ..., \")+\": ...}\":\"{}\";_(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,He,se,Br,se),Qt[se+He]=!0}}return n===i?Hr(k):Gr(k),k}}var $r=Vr;Ve.Fragment=i,Ve.jsxDEV=$r})()});var rn=h((vs,nn)=>{\"use strict\";nn.exports=tn()});var Oe=h(Be=>{\"use strict\";Be._=Be._interop_require_default=ni;function ni(t){return t&&t.__esModule?t:{default:t}}});var Ye=h(Ke=>{\"use strict\";Object.defineProperty(Ke,\"__esModule\",{value:!0});function ri(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}ri(Ke,{searchParamsToUrlQuery:function(){return ii},urlQueryToSearchParams:function(){return oi},assign:function(){return si}});function ii(t){let e={};return t.forEach((r,i)=>{typeof e[i]>\"u\"?e[i]=r:Array.isArray(e[i])?e[i].push(r):e[i]=[e[i],r]}),e}function on(t){return typeof t==\"string\"||typeof t==\"number\"&&!isNaN(t)||typeof t==\"boolean\"?String(t):\"\"}function oi(t){let e=new URLSearchParams;return Object.entries(t).forEach(r=>{let[i,o]=r;Array.isArray(o)?o.forEach(s=>e.append(i,on(s))):e.set(i,on(o))}),e}function si(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];return r.forEach(o=>{Array.from(o.keys()).forEach(s=>t.delete(s)),o.forEach((s,f)=>t.append(f,s))}),t}});var an=h(Xe=>{\"use strict\";function sn(t){if(typeof WeakMap!=\"function\")return null;var e=new WeakMap,r=new WeakMap;return(sn=function(i){return i?r:e})(t)}Xe._=Xe._interop_require_wildcard=ai;function ai(t,e){if(!e&&t&&t.__esModule)return t;if(t===null||typeof t!=\"object\"&&typeof t!=\"function\")return{default:t};var r=sn(e);if(r&&r.has(t))return r.get(t);var i={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if(s!==\"default\"&&Object.prototype.hasOwnProperty.call(t,s)){var f=o?Object.getOwnPropertyDescriptor(t,s):null;f&&(f.get||f.set)?Object.defineProperty(i,s,f):i[s]=t[s]}return i.default=t,r&&r.set(t,i),i}});var Ze=h(Qe=>{\"use strict\";Object.defineProperty(Qe,\"__esModule\",{value:!0});function li(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}li(Qe,{formatUrl:function(){return ln},urlObjectKeys:function(){return un},formatWithValidation:function(){return fi}});var ui=an(),ci=ui._(Ye()),di=/https?|ftp|gopher|file/;function ln(t){let{auth:e,hostname:r}=t,i=t.protocol||\"\",o=t.pathname||\"\",s=t.hash||\"\",f=t.query||\"\",d=!1;e=e?encodeURIComponent(e).replace(/%3A/i,\":\")+\"@\":\"\",t.host?d=e+t.host:r&&(d=e+(~r.indexOf(\":\")?\"[\"+r+\"]\":r),t.port&&(d+=\":\"+t.port)),f&&typeof f==\"object\"&&(f=String(ci.urlQueryToSearchParams(f)));let u=t.search||f&&\"?\"+f||\"\";return i&&!i.endsWith(\":\")&&(i+=\":\"),t.slashes||(!i||di.test(i))&&d!==!1?(d=\"//\"+(d||\"\"),o&&o[0]!==\"/\"&&(o=\"/\"+o)):d||(d=\"\"),s&&s[0]!==\"#\"&&(s=\"#\"+s),u&&u[0]!==\"?\"&&(u=\"?\"+u),o=o.replace(/[?#]/g,encodeURIComponent),u=u.replace(\"#\",\"%23\"),\"\"+i+d+o+u+s}var un=[\"auth\",\"hash\",\"host\",\"hostname\",\"href\",\"path\",\"pathname\",\"port\",\"protocol\",\"query\",\"search\",\"slashes\"];function fi(t){return t!==null&&typeof t==\"object\"&&Object.keys(t).forEach(e=>{un.includes(e)||console.warn(\"Unknown key passed via urlObject into url.format: \"+e)}),ln(t)}});var cn=h(Je=>{\"use strict\";Object.defineProperty(Je,\"__esModule\",{value:!0});Object.defineProperty(Je,\"omit\",{enumerable:!0,get:function(){return pi}});function pi(t,e){let r={};return Object.keys(t).forEach(i=>{e.includes(i)||(r[i]=t[i])}),r}});var he=h(ot=>{\"use strict\";Object.defineProperty(ot,\"__esModule\",{value:!0});function mi(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}mi(ot,{WEB_VITALS:function(){return hi},execOnce:function(){return bi},isAbsoluteUrl:function(){return gi},getLocationOrigin:function(){return dn},getURL:function(){return yi},getDisplayName:function(){return Ce},isResSent:function(){return fn},normalizeRepeatedSlashes:function(){return Ni},loadGetInitialProps:function(){return pn},SP:function(){return mn},ST:function(){return xi},DecodeError:function(){return et},NormalizeError:function(){return tt},PageNotFoundError:function(){return nt},MissingStaticPage:function(){return rt},MiddlewareNotFoundError:function(){return it},stringifyError:function(){return vi}});var hi=[\"CLS\",\"FCP\",\"FID\",\"INP\",\"LCP\",\"TTFB\"];function bi(t){let e=!1,r;return function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return e||(e=!0,r=t(...o)),r}}var _i=/^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/,gi=t=>_i.test(t);function dn(){let{protocol:t,hostname:e,port:r}=window.location;return t+\"//\"+e+(r?\":\"+r:\"\")}function yi(){let{href:t}=window.location,e=dn();return t.substring(e.length)}function Ce(t){return typeof t==\"string\"?t:t.displayName||t.name||\"Unknown\"}function fn(t){return t.finished||t.headersSent}function Ni(t){let e=t.split(\"?\");return e[0].replace(/\\\\/g,\"/\").replace(/\\/\\/+/g,\"/\")+(e[1]?\"?\"+e.slice(1).join(\"?\"):\"\")}async function pn(t,e){var r;if((r=t.prototype)!=null&&r.getInitialProps){let s='\"'+Ce(t)+'.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';throw new Error(s)}let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await pn(e.Component,e.ctx)}:{};let o=await t.getInitialProps(e);if(i&&fn(i))return o;if(!o){let s='\"'+Ce(t)+'.getInitialProps()\" should resolve to an object. But found \"'+o+'\" instead.';throw new Error(s)}return Object.keys(o).length===0&&!e.ctx&&console.warn(\"\"+Ce(t)+\" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\"),o}var mn=typeof performance<\"u\",xi=mn&&[\"mark\",\"measure\",\"getEntriesByName\"].every(t=>typeof performance[t]==\"function\"),et=class extends Error{},tt=class extends Error{},nt=class extends Error{constructor(e){super(),this.code=\"ENOENT\",this.name=\"PageNotFoundError\",this.message=\"Cannot find module for page: \"+e}},rt=class extends Error{constructor(e,r){super(),this.message=\"Failed to load static file for page: \"+e+\" \"+r}},it=class extends Error{constructor(){super(),this.code=\"ENOENT\",this.message=\"Cannot find the middleware module\"}};function vi(t){return JSON.stringify({message:t.message,stack:t.stack})}});var at=h(st=>{\"use strict\";Object.defineProperty(st,\"__esModule\",{value:!0});Object.defineProperty(st,\"removeTrailingSlash\",{enumerable:!0,get:function(){return Ti}});function Ti(t){return t.replace(/\\/$/,\"\")||\"/\"}});var Se=h(lt=>{\"use strict\";Object.defineProperty(lt,\"__esModule\",{value:!0});Object.defineProperty(lt,\"parsePath\",{enumerable:!0,get:function(){return Pi}});function Pi(t){let e=t.indexOf(\"#\"),r=t.indexOf(\"?\"),i=r>-1&&(e<0||r<e);return i||e>-1?{pathname:t.substring(0,i?r:e),query:i?t.substring(r,e>-1?e:void 0):\"\",hash:e>-1?t.slice(e):\"\"}:{pathname:t,query:\"\",hash:\"\"}}});var be=h((q,bn)=>{\"use strict\";Object.defineProperty(q,\"__esModule\",{value:!0});Object.defineProperty(q,\"normalizePathTrailingSlash\",{enumerable:!0,get:function(){return ki}});var hn=at(),Ei=Se(),ki=t=>{if(!t.startsWith(\"/\")||process.env.__NEXT_MANUAL_TRAILING_SLASH)return t;let{pathname:e,query:r,hash:i}=(0,Ei.parsePath)(t);return process.env.__NEXT_TRAILING_SLASH?/\\.[^/]+\\/?$/.test(e)?\"\"+(0,hn.removeTrailingSlash)(e)+r+i:e.endsWith(\"/\")?\"\"+e+r+i:e+\"/\"+r+i:\"\"+(0,hn.removeTrailingSlash)(e)+r+i};(typeof q.default==\"function\"||typeof q.default==\"object\"&&q.default!==null)&&typeof q.default.__esModule>\"u\"&&(Object.defineProperty(q.default,\"__esModule\",{value:!0}),Object.assign(q.default,q),bn.exports=q.default)});var ct=h(ut=>{\"use strict\";Object.defineProperty(ut,\"__esModule\",{value:!0});Object.defineProperty(ut,\"pathHasPrefix\",{enumerable:!0,get:function(){return wi}});var Ri=Se();function wi(t,e){if(typeof t!=\"string\")return!1;let{pathname:r}=(0,Ri.parsePath)(t);return r===e||r.startsWith(e+\"/\")}});var gn=h((F,_n)=>{\"use strict\";Object.defineProperty(F,\"__esModule\",{value:!0});Object.defineProperty(F,\"hasBasePath\",{enumerable:!0,get:function(){return Si}});var Oi=ct(),Ci=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Si(t){return(0,Oi.pathHasPrefix)(t,Ci)}(typeof F.default==\"function\"||typeof F.default==\"object\"&&F.default!==null)&&typeof F.default.__esModule>\"u\"&&(Object.defineProperty(F.default,\"__esModule\",{value:!0}),Object.assign(F.default,F),_n.exports=F.default)});var ft=h(dt=>{\"use strict\";Object.defineProperty(dt,\"__esModule\",{value:!0});Object.defineProperty(dt,\"isLocalURL\",{enumerable:!0,get:function(){return ji}});var yn=he(),Di=gn();function ji(t){if(!(0,yn.isAbsoluteUrl)(t))return!0;try{let e=(0,yn.getLocationOrigin)(),r=new URL(t,e);return r.origin===e&&(0,Di.hasBasePath)(r.pathname)}catch{return!1}}});var Nn=h(mt=>{\"use strict\";Object.defineProperty(mt,\"__esModule\",{value:!0});Object.defineProperty(mt,\"getSortedRoutes\",{enumerable:!0,get:function(){return Ii}});var pt=class t{insert(e){this._insert(e.split(\"/\").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){e===void 0&&(e=\"/\");let r=[...this.children.keys()].sort();this.slugName!==null&&r.splice(r.indexOf(\"[]\"),1),this.restSlugName!==null&&r.splice(r.indexOf(\"[...]\"),1),this.optionalRestSlugName!==null&&r.splice(r.indexOf(\"[[...]]\"),1);let i=r.map(o=>this.children.get(o)._smoosh(\"\"+e+o+\"/\")).reduce((o,s)=>[...o,...s],[]);if(this.slugName!==null&&i.push(...this.children.get(\"[]\")._smoosh(e+\"[\"+this.slugName+\"]/\")),!this.placeholder){let o=e===\"/\"?\"/\":e.slice(0,-1);if(this.optionalRestSlugName!=null)throw new Error('You cannot define a route with the same specificity as a optional catch-all route (\"'+o+'\" and \"'+o+\"[[...\"+this.optionalRestSlugName+']]\").');i.unshift(o)}return this.restSlugName!==null&&i.push(...this.children.get(\"[...]\")._smoosh(e+\"[...\"+this.restSlugName+\"]/\")),this.optionalRestSlugName!==null&&i.push(...this.children.get(\"[[...]]\")._smoosh(e+\"[[...\"+this.optionalRestSlugName+\"]]/\")),i}_insert(e,r,i){if(e.length===0){this.placeholder=!1;return}if(i)throw new Error(\"Catch-all must be the last part of the URL.\");let o=e[0];if(o.startsWith(\"[\")&&o.endsWith(\"]\")){let d=function(u,p){if(u!==null&&u!==p)throw new Error(\"You cannot use different slug names for the same dynamic path ('\"+u+\"' !== '\"+p+\"').\");r.forEach(v=>{if(v===p)throw new Error('You cannot have the same slug name \"'+p+'\" repeat within a single dynamic path');if(v.replace(/\\W/g,\"\")===o.replace(/\\W/g,\"\"))throw new Error('You cannot have the slug names \"'+v+'\" and \"'+p+'\" differ only by non-word symbols within a single dynamic path')}),r.push(p)},s=o.slice(1,-1),f=!1;if(s.startsWith(\"[\")&&s.endsWith(\"]\")&&(s=s.slice(1,-1),f=!0),s.startsWith(\"...\")&&(s=s.substring(3),i=!0),s.startsWith(\"[\")||s.endsWith(\"]\"))throw new Error(\"Segment names may not start or end with extra brackets ('\"+s+\"').\");if(s.startsWith(\".\"))throw new Error(\"Segment names may not start with erroneous periods ('\"+s+\"').\");if(i)if(f){if(this.restSlugName!=null)throw new Error('You cannot use both an required and optional catch-all route at the same level (\"[...'+this.restSlugName+']\" and \"'+e[0]+'\" ).');d(this.optionalRestSlugName,s),this.optionalRestSlugName=s,o=\"[[...]]\"}else{if(this.optionalRestSlugName!=null)throw new Error('You cannot use both an optional and required catch-all route at the same level (\"[[...'+this.optionalRestSlugName+']]\" and \"'+e[0]+'\").');d(this.restSlugName,s),this.restSlugName=s,o=\"[...]\"}else{if(f)throw new Error('Optional route parameters are not yet supported (\"'+e[0]+'\").');d(this.slugName,s),this.slugName=s,o=\"[]\"}}this.children.has(o)||this.children.set(o,new t),this.children.get(o)._insert(e.slice(1),r,i)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}};function Ii(t){let e=new pt;return t.forEach(r=>e.insert(r)),e.smoosh()}});var xn=h(ht=>{\"use strict\";Object.defineProperty(ht,\"__esModule\",{value:!0});Object.defineProperty(ht,\"isDynamicRoute\",{enumerable:!0,get:function(){return Ui}});var Ai=/\\/\\[[^/]+?\\](?=\\/|$)/;function Ui(t){return Ai.test(t)}});var vn=h(bt=>{\"use strict\";Object.defineProperty(bt,\"__esModule\",{value:!0});function Wi(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}Wi(bt,{getSortedRoutes:function(){return zi.getSortedRoutes},isDynamicRoute:function(){return Mi.isDynamicRoute}});var zi=Nn(),Mi=xn()});var Tn=h(_t=>{\"use strict\";Object.defineProperty(_t,\"__esModule\",{value:!0});Object.defineProperty(_t,\"getRouteMatcher\",{enumerable:!0,get:function(){return qi}});var Li=he();function qi(t){let{re:e,groups:r}=t;return i=>{let o=e.exec(i);if(!o)return!1;let s=d=>{try{return decodeURIComponent(d)}catch{throw new Li.DecodeError(\"failed to decode param\")}},f={};return Object.keys(r).forEach(d=>{let u=r[d],p=o[u.pos];p!==void 0&&(f[d]=~p.indexOf(\"/\")?p.split(\"/\").map(v=>s(v)):u.repeat?[s(p)]:s(p))}),f}}});var Pn=h(gt=>{\"use strict\";Object.defineProperty(gt,\"__esModule\",{value:!0});Object.defineProperty(gt,\"ensureLeadingSlash\",{enumerable:!0,get:function(){return Fi}});function Fi(t){return t.startsWith(\"/\")?t:\"/\"+t}});var En=h(yt=>{\"use strict\";Object.defineProperty(yt,\"__esModule\",{value:!0});Object.defineProperty(yt,\"isGroupSegment\",{enumerable:!0,get:function(){return Gi}});function Gi(t){return t[0]===\"(\"&&t.endsWith(\")\")}});var kn=h(Nt=>{\"use strict\";Object.defineProperty(Nt,\"__esModule\",{value:!0});function Hi(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}Hi(Nt,{normalizeAppPath:function(){return Bi},normalizeRscPath:function(){return Ki}});var Vi=Pn(),$i=En();function Bi(t){return(0,Vi.ensureLeadingSlash)(t.split(\"/\").reduce((e,r,i,o)=>!r||(0,$i.isGroupSegment)(r)||r[0]===\"@\"||(r===\"page\"||r===\"route\")&&i===o.length-1?e:e+\"/\"+r,\"\"))}function Ki(t,e){return e?t.replace(/\\.rsc($|\\?)/,\"$1\"):t}});var Rn=h(vt=>{\"use strict\";Object.defineProperty(vt,\"__esModule\",{value:!0});function Yi(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}Yi(vt,{INTERCEPTION_ROUTE_MARKERS:function(){return xt},isInterceptionRouteAppPath:function(){return Qi},extractInterceptionRouteInformation:function(){return Zi}});var Xi=kn(),xt=[\"(..)(..)\",\"(.)\",\"(..)\",\"(...)\"];function Qi(t){return t.split(\"/\").find(e=>xt.find(r=>e.startsWith(r)))!==void 0}function Zi(t){let e,r,i;for(let o of t.split(\"/\"))if(r=xt.find(s=>o.startsWith(s)),r){[e,i]=t.split(r,2);break}if(!e||!r||!i)throw new Error(`Invalid interception route: ${t}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(e=(0,Xi.normalizeAppPath)(e),r){case\"(.)\":e===\"/\"?i=`/${i}`:i=e+\"/\"+i;break;case\"(..)\":if(e===\"/\")throw new Error(`Invalid interception route: ${t}. Cannot use (..) marker at the root level, use (.) instead.`);i=e.split(\"/\").slice(0,-1).concat(i).join(\"/\");break;case\"(...)\":i=\"/\"+i;break;case\"(..)(..)\":let o=e.split(\"/\");if(o.length<=2)throw new Error(`Invalid interception route: ${t}. Cannot use (..)(..) marker at the root level or one level up.`);i=o.slice(0,-2).concat(i).join(\"/\");break;default:throw new Error(\"Invariant: unexpected marker\")}return{interceptingRoute:e,interceptedRoute:i}}});var wn=h(Tt=>{\"use strict\";Object.defineProperty(Tt,\"__esModule\",{value:!0});Object.defineProperty(Tt,\"escapeStringRegexp\",{enumerable:!0,get:function(){return to}});var Ji=/[|\\\\{}()[\\]^$+*?.-]/,eo=/[|\\\\{}()[\\]^$+*?.-]/g;function to(t){return Ji.test(t)?t.replace(eo,\"\\\\$&\"):t}});var An=h(kt=>{\"use strict\";Object.defineProperty(kt,\"__esModule\",{value:!0});function no(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}no(kt,{getRouteRegex:function(){return jn},getNamedRouteRegex:function(){return so},getNamedMiddlewareRegex:function(){return ao}});var Cn=Rn(),Pt=wn(),Sn=at(),ro=\"nxtP\",io=\"nxtI\";function Et(t){let e=t.startsWith(\"[\")&&t.endsWith(\"]\");e&&(t=t.slice(1,-1));let r=t.startsWith(\"...\");return r&&(t=t.slice(3)),{key:t,repeat:r,optional:e}}function Dn(t){let e=(0,Sn.removeTrailingSlash)(t).slice(1).split(\"/\"),r={},i=1;return{parameterizedRoute:e.map(o=>{let s=Cn.INTERCEPTION_ROUTE_MARKERS.find(d=>o.startsWith(d)),f=o.match(/\\[((?:\\[.*\\])|.+)\\]/);if(s&&f){let{key:d,optional:u,repeat:p}=Et(f[1]);return r[d]={pos:i++,repeat:p,optional:u},\"/\"+(0,Pt.escapeStringRegexp)(s)+\"([^/]+?)\"}else if(f){let{key:d,repeat:u,optional:p}=Et(f[1]);return r[d]={pos:i++,repeat:u,optional:p},u?p?\"(?:/(.+?))?\":\"/(.+?)\":\"/([^/]+?)\"}else return\"/\"+(0,Pt.escapeStringRegexp)(o)}).join(\"\"),groups:r}}function jn(t){let{parameterizedRoute:e,groups:r}=Dn(t);return{re:new RegExp(\"^\"+e+\"(?:/)?$\"),groups:r}}function oo(){let t=0;return()=>{let e=\"\",r=++t;for(;r>0;)e+=String.fromCharCode(97+(r-1)%26),r=Math.floor((r-1)/26);return e}}function On(t){let{getSafeRouteKey:e,segment:r,routeKeys:i,keyPrefix:o}=t,{key:s,optional:f,repeat:d}=Et(r),u=s.replace(/\\W/g,\"\");o&&(u=\"\"+o+u);let p=!1;return(u.length===0||u.length>30)&&(p=!0),isNaN(parseInt(u.slice(0,1)))||(p=!0),p&&(u=e()),o?i[u]=\"\"+o+s:i[u]=\"\"+s,d?f?\"(?:/(?<\"+u+\">.+?))?\":\"/(?<\"+u+\">.+?)\":\"/(?<\"+u+\">[^/]+?)\"}function In(t,e){let r=(0,Sn.removeTrailingSlash)(t).slice(1).split(\"/\"),i=oo(),o={};return{namedParameterizedRoute:r.map(s=>{let f=Cn.INTERCEPTION_ROUTE_MARKERS.some(u=>s.startsWith(u)),d=s.match(/\\[((?:\\[.*\\])|.+)\\]/);return f&&d?On({getSafeRouteKey:i,segment:d[1],routeKeys:o,keyPrefix:e?io:void 0}):d?On({getSafeRouteKey:i,segment:d[1],routeKeys:o,keyPrefix:e?ro:void 0}):\"/\"+(0,Pt.escapeStringRegexp)(s)}).join(\"\"),routeKeys:o}}function so(t,e){let r=In(t,e);return{...jn(t),namedRegex:\"^\"+r.namedParameterizedRoute+\"(?:/)?$\",routeKeys:r.routeKeys}}function ao(t,e){let{parameterizedRoute:r}=Dn(t),{catchAll:i=!0}=e;if(r===\"/\")return{namedRegex:\"^/\"+(i?\".*\":\"\")+\"$\"};let{namedParameterizedRoute:o}=In(t,!1),s=i?\"(?:(/.*)?)\":\"\";return{namedRegex:\"^\"+o+s+\"$\"}}});var Un=h(Rt=>{\"use strict\";Object.defineProperty(Rt,\"__esModule\",{value:!0});Object.defineProperty(Rt,\"interpolateAs\",{enumerable:!0,get:function(){return co}});var lo=Tn(),uo=An();function co(t,e,r){let i=\"\",o=(0,uo.getRouteRegex)(t),s=o.groups,f=(e!==t?(0,lo.getRouteMatcher)(o)(e):\"\")||r;i=t;let d=Object.keys(s);return d.every(u=>{let p=f[u]||\"\",{repeat:v,optional:S}=s[u],P=\"[\"+(v?\"...\":\"\")+u+\"]\";return S&&(P=(p?\"\":\"/\")+\"[\"+P+\"]\"),v&&!Array.isArray(p)&&(p=[p]),(S||u in f)&&(i=i.replace(P,v?p.map(U=>encodeURIComponent(U)).join(\"/\"):encodeURIComponent(p))||\"/\")})||(i=\"\"),{params:d,result:i}}});var Mn=h((G,zn)=>{\"use strict\";Object.defineProperty(G,\"__esModule\",{value:!0});Object.defineProperty(G,\"resolveHref\",{enumerable:!0,get:function(){return yo}});var fo=Ye(),Wn=Ze(),po=cn(),mo=he(),ho=be(),bo=ft(),_o=vn(),go=Un();function yo(t,e,r){let i,o=typeof e==\"string\"?e:(0,Wn.formatWithValidation)(e),s=o.match(/^[a-zA-Z]{1,}:\\/\\//),f=s?o.slice(s[0].length):o;if((f.split(\"?\")[0]||\"\").match(/(\\/\\/|\\\\)/)){console.error(\"Invalid href '\"+o+\"' passed to next/router in page: '\"+t.pathname+\"'. Repeated forward-slashes (//) or backslashes \\\\ are not valid in the href.\");let u=(0,mo.normalizeRepeatedSlashes)(f);o=(s?s[0]:\"\")+u}if(!(0,bo.isLocalURL)(o))return r?[o]:o;try{i=new URL(o.startsWith(\"#\")?t.asPath:t.pathname,\"http://n\")}catch{i=new URL(\"/\",\"http://n\")}try{let u=new URL(o,i);u.pathname=(0,ho.normalizePathTrailingSlash)(u.pathname);let p=\"\";if((0,_o.isDynamicRoute)(u.pathname)&&u.searchParams&&r){let S=(0,fo.searchParamsToUrlQuery)(u.searchParams),{result:P,params:U}=(0,go.interpolateAs)(u.pathname,u.pathname,S);P&&(p=(0,Wn.formatWithValidation)({pathname:P,hash:u.hash,query:(0,po.omit)(S,U)}))}let v=u.origin===i.origin?u.href.slice(u.origin.length):u.href;return r?[v,p||v]:v}catch{return r?[o]:o}}(typeof G.default==\"function\"||typeof G.default==\"object\"&&G.default!==null)&&typeof G.default.__esModule>\"u\"&&(Object.defineProperty(G.default,\"__esModule\",{value:!0}),Object.assign(G.default,G),zn.exports=G.default)});var Ot=h(wt=>{\"use strict\";Object.defineProperty(wt,\"__esModule\",{value:!0});Object.defineProperty(wt,\"addPathPrefix\",{enumerable:!0,get:function(){return xo}});var No=Se();function xo(t,e){if(!t.startsWith(\"/\")||!e)return t;let{pathname:r,query:i,hash:o}=(0,No.parsePath)(t);return\"\"+e+r+i+o}});var qn=h(Ct=>{\"use strict\";Object.defineProperty(Ct,\"__esModule\",{value:!0});Object.defineProperty(Ct,\"addLocale\",{enumerable:!0,get:function(){return To}});var vo=Ot(),Ln=ct();function To(t,e,r,i){if(!e||e===r)return t;let o=t.toLowerCase();return!i&&((0,Ln.pathHasPrefix)(o,\"/api\")||(0,Ln.pathHasPrefix)(o,\"/\"+e.toLowerCase()))?t:(0,vo.addPathPrefix)(t,\"/\"+e)}});var Gn=h((H,Fn)=>{\"use strict\";Object.defineProperty(H,\"__esModule\",{value:!0});Object.defineProperty(H,\"addLocale\",{enumerable:!0,get:function(){return Eo}});var Po=be(),Eo=function(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];return process.env.__NEXT_I18N_SUPPORT?(0,Po.normalizePathTrailingSlash)(qn().addLocale(t,...r)):t};(typeof H.default==\"function\"||typeof H.default==\"object\"&&H.default!==null)&&typeof H.default.__esModule>\"u\"&&(Object.defineProperty(H.default,\"__esModule\",{value:!0}),Object.assign(H.default,H),Fn.exports=H.default)});var Vn=h(St=>{\"use strict\";Object.defineProperty(St,\"__esModule\",{value:!0});Object.defineProperty(St,\"RouterContext\",{enumerable:!0,get:function(){return Hn}});var ko=Oe(),Ro=ko._(de()),Hn=Ro.default.createContext(null);Hn.displayName=\"RouterContext\"});var Xn=h(jt=>{\"use client\";\"use strict\";Object.defineProperty(jt,\"__esModule\",{value:!0});function wo(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}wo(jt,{CacheStates:function(){return Dt},AppRouterContext:function(){return $n},LayoutRouterContext:function(){return Bn},GlobalLayoutRouterContext:function(){return Kn},TemplateContext:function(){return Yn}});var Oo=Oe(),De=Oo._(de()),Dt;(function(t){t.LAZY_INITIALIZED=\"LAZYINITIALIZED\",t.DATA_FETCH=\"DATAFETCH\",t.READY=\"READY\"})(Dt||(Dt={}));var $n=De.default.createContext(null),Bn=De.default.createContext(null),Kn=De.default.createContext(null),Yn=De.default.createContext(null);$n.displayName=\"AppRouterContext\",Bn.displayName=\"LayoutRouterContext\",Kn.displayName=\"GlobalLayoutRouterContext\",Yn.displayName=\"TemplateContext\"});var Zn=h((V,Qn)=>{\"use strict\";Object.defineProperty(V,\"__esModule\",{value:!0});function Co(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}Co(V,{requestIdleCallback:function(){return So},cancelIdleCallback:function(){return Do}});var So=typeof self<\"u\"&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(t){let e=Date.now();return self.setTimeout(function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})},1)},Do=typeof self<\"u\"&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(t){return clearTimeout(t)};(typeof V.default==\"function\"||typeof V.default==\"object\"&&V.default!==null)&&typeof V.default.__esModule>\"u\"&&(Object.defineProperty(V.default,\"__esModule\",{value:!0}),Object.assign(V.default,V),Qn.exports=V.default)});var nr=h(($,tr)=>{\"use strict\";Object.defineProperty($,\"__esModule\",{value:!0});Object.defineProperty($,\"useIntersection\",{enumerable:!0,get:function(){return Ao}});var _e=de(),Jn=Zn(),er=typeof IntersectionObserver==\"function\",It=new Map,je=[];function jo(t){let e={root:t.root||null,margin:t.rootMargin||\"\"},r=je.find(f=>f.root===e.root&&f.margin===e.margin),i;if(r&&(i=It.get(r),i))return i;let o=new Map,s=new IntersectionObserver(f=>{f.forEach(d=>{let u=o.get(d.target),p=d.isIntersecting||d.intersectionRatio>0;u&&p&&u(p)})},t);return i={id:e,observer:s,elements:o},je.push(e),It.set(e,i),i}function Io(t,e,r){let{id:i,observer:o,elements:s}=jo(r);return s.set(t,e),o.observe(t),function(){if(s.delete(t),o.unobserve(t),s.size===0){o.disconnect(),It.delete(i);let d=je.findIndex(u=>u.root===i.root&&u.margin===i.margin);d>-1&&je.splice(d,1)}}}function Ao(t){let{rootRef:e,rootMargin:r,disabled:i}=t,o=i||!er,[s,f]=(0,_e.useState)(!1),d=(0,_e.useRef)(null),u=(0,_e.useCallback)(v=>{d.current=v},[]);(0,_e.useEffect)(()=>{if(er){if(o||s)return;let v=d.current;if(v&&v.tagName)return Io(v,P=>P&&f(P),{root:e?.current,rootMargin:r})}else if(!s){let v=(0,Jn.requestIdleCallback)(()=>f(!0));return()=>(0,Jn.cancelIdleCallback)(v)}},[o,r,e,s,d.current]);let p=(0,_e.useCallback)(()=>{f(!1)},[]);return[u,s,p]}(typeof $.default==\"function\"||typeof $.default==\"object\"&&$.default!==null)&&typeof $.default.__esModule>\"u\"&&(Object.defineProperty($.default,\"__esModule\",{value:!0}),Object.assign($.default,$),tr.exports=$.default)});var rr=h(At=>{\"use strict\";Object.defineProperty(At,\"__esModule\",{value:!0});Object.defineProperty(At,\"normalizeLocalePath\",{enumerable:!0,get:function(){return Uo}});function Uo(t,e){let r,i=t.split(\"/\");return(e||[]).some(o=>i[1]&&i[1].toLowerCase()===o.toLowerCase()?(r=o,i.splice(1,1),t=i.join(\"/\")||\"/\",!0):!1),{pathname:t,detectedLocale:r}}});var or=h((B,ir)=>{\"use strict\";Object.defineProperty(B,\"__esModule\",{value:!0});Object.defineProperty(B,\"normalizeLocalePath\",{enumerable:!0,get:function(){return Wo}});var Wo=(t,e)=>process.env.__NEXT_I18N_SUPPORT?rr().normalizeLocalePath(t,e):{pathname:t,detectedLocale:void 0};(typeof B.default==\"function\"||typeof B.default==\"object\"&&B.default!==null)&&typeof B.default.__esModule>\"u\"&&(Object.defineProperty(B.default,\"__esModule\",{value:!0}),Object.assign(B.default,B),ir.exports=B.default)});var sr=h(Ut=>{\"use strict\";Object.defineProperty(Ut,\"__esModule\",{value:!0});Object.defineProperty(Ut,\"detectDomainLocale\",{enumerable:!0,get:function(){return zo}});function zo(t,e,r){if(t){r&&(r=r.toLowerCase());for(let s of t){var i,o;let f=(i=s.domain)==null?void 0:i.split(\":\")[0].toLowerCase();if(e===f||r===s.defaultLocale.toLowerCase()||(o=s.locales)!=null&&o.some(d=>d.toLowerCase()===r))return s}}}});var lr=h((K,ar)=>{\"use strict\";Object.defineProperty(K,\"__esModule\",{value:!0});Object.defineProperty(K,\"detectDomainLocale\",{enumerable:!0,get:function(){return Mo}});var Mo=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];if(process.env.__NEXT_I18N_SUPPORT)return sr().detectDomainLocale(...e)};(typeof K.default==\"function\"||typeof K.default==\"object\"&&K.default!==null)&&typeof K.default.__esModule>\"u\"&&(Object.defineProperty(K.default,\"__esModule\",{value:!0}),Object.assign(K.default,K),ar.exports=K.default)});var cr=h((Y,ur)=>{\"use strict\";Object.defineProperty(Y,\"__esModule\",{value:!0});Object.defineProperty(Y,\"getDomainLocale\",{enumerable:!0,get:function(){return Fo}});var Lo=be(),qo=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Fo(t,e,r,i){if(process.env.__NEXT_I18N_SUPPORT){let o=or().normalizeLocalePath,s=lr().detectDomainLocale,f=e||o(t,r).detectedLocale,d=s(i,void 0,f);if(d){let u=\"http\"+(d.http?\"\":\"s\")+\"://\",p=f===d.defaultLocale?\"\":\"/\"+f;return\"\"+u+d.domain+(0,Lo.normalizePathTrailingSlash)(\"\"+qo+p+t)}return!1}else return!1}(typeof Y.default==\"function\"||typeof Y.default==\"object\"&&Y.default!==null)&&typeof Y.default.__esModule>\"u\"&&(Object.defineProperty(Y.default,\"__esModule\",{value:!0}),Object.assign(Y.default,Y),ur.exports=Y.default)});var fr=h((X,dr)=>{\"use strict\";Object.defineProperty(X,\"__esModule\",{value:!0});Object.defineProperty(X,\"addBasePath\",{enumerable:!0,get:function(){return $o}});var Go=Ot(),Ho=be(),Vo=process.env.__NEXT_ROUTER_BASEPATH||\"\";function $o(t,e){return(0,Ho.normalizePathTrailingSlash)(process.env.__NEXT_MANUAL_CLIENT_BASE_PATH&&!e?t:(0,Go.addPathPrefix)(t,Vo))}(typeof X.default==\"function\"||typeof X.default==\"object\"&&X.default!==null)&&typeof X.default.__esModule>\"u\"&&(Object.defineProperty(X.default,\"__esModule\",{value:!0}),Object.assign(X.default,X),dr.exports=X.default)});var mr=h((Q,pr)=>{\"use strict\";Object.defineProperty(Q,\"__esModule\",{value:!0});function Bo(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}Bo(Q,{PrefetchKind:function(){return Wt},ACTION_REFRESH:function(){return Ko},ACTION_NAVIGATE:function(){return Yo},ACTION_RESTORE:function(){return Xo},ACTION_SERVER_PATCH:function(){return Qo},ACTION_PREFETCH:function(){return Zo},ACTION_FAST_REFRESH:function(){return Jo},ACTION_SERVER_ACTION:function(){return es}});var Ko=\"refresh\",Yo=\"navigate\",Xo=\"restore\",Qo=\"server-patch\",Zo=\"prefetch\",Jo=\"fast-refresh\",es=\"server-action\",Wt;(function(t){t.AUTO=\"auto\",t.FULL=\"full\",t.TEMPORARY=\"temporary\"})(Wt||(Wt={}));(typeof Q.default==\"function\"||typeof Q.default==\"object\"&&Q.default!==null)&&typeof Q.default.__esModule>\"u\"&&(Object.defineProperty(Q.default,\"__esModule\",{value:!0}),Object.assign(Q.default,Q),pr.exports=Q.default)});var xr=h((Z,Nr)=>{\"use client\";\"use strict\";Object.defineProperty(Z,\"__esModule\",{value:!0});Object.defineProperty(Z,\"default\",{enumerable:!0,get:function(){return ps}});var ts=Oe(),I=ts._(de()),hr=Mn(),yr=ft(),ns=Ze(),rs=he(),is=Gn(),os=Vn(),ss=Xn(),as=nr(),ls=cr(),us=fr(),br=mr(),_r=new Set;function zt(t,e,r,i,o,s){if(typeof window>\"u\"||!s&&!(0,yr.isLocalURL)(e))return;if(!i.bypassPrefetchedCheck){let d=typeof i.locale<\"u\"?i.locale:\"locale\"in t?t.locale:void 0,u=e+\"%\"+r+\"%\"+d;if(_r.has(u))return;_r.add(u)}let f=s?t.prefetch(e,o):t.prefetch(e,r,i);Promise.resolve(f).catch(d=>{throw d})}function cs(t){let r=t.currentTarget.getAttribute(\"target\");return r&&r!==\"_self\"||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&t.nativeEvent.which===2}function ds(t,e,r,i,o,s,f,d,u,p){let{nodeName:v}=t.currentTarget;if(v.toUpperCase()===\"A\"&&(cs(t)||!u&&!(0,yr.isLocalURL)(r)))return;t.preventDefault();let P=()=>{let U=f??!0;\"beforePopState\"in e?e[o?\"replace\":\"push\"](r,i,{shallow:s,locale:d,scroll:U}):e[o?\"replace\":\"push\"](i||r,{forceOptimisticNavigation:!p,scroll:U})};u?I.default.startTransition(P):P()}function gr(t){return typeof t==\"string\"?t:(0,ns.formatUrl)(t)}var fs=I.default.forwardRef(function(e,r){let i,{href:o,as:s,children:f,prefetch:d=null,passHref:u,replace:p,shallow:v,scroll:S,locale:P,onClick:U,onMouseEnter:ae,onTouchStart:ge,legacyBehavior:A=!1,...te}=e;i=f,A&&(typeof i==\"string\"||typeof i==\"number\")&&(i=I.default.createElement(\"a\",null,i));let _=I.default.useContext(os.RouterContext),Ae=I.default.useContext(ss.AppRouterContext),z=_??Ae,M=!_,ie=d!==!1,le=d===null?br.PrefetchKind.AUTO:br.PrefetchKind.FULL;{let b=function(g){return new Error(\"Failed prop type: The prop `\"+g.key+\"` expects a \"+g.expected+\" in `<Link>`, but got `\"+g.actual+\"` instead.\"+(typeof window<\"u\"?`\nOpen your browser's console to view the Component stack trace.`:\"\"))};Object.keys({href:!0}).forEach(g=>{if(g===\"href\"){if(e[g]==null||typeof e[g]!=\"string\"&&typeof e[g]!=\"object\")throw b({key:g,expected:\"`string` or `object`\",actual:e[g]===null?\"null\":typeof e[g]})}else{let D=g}}),Object.keys({as:!0,replace:!0,scroll:!0,shallow:!0,passHref:!0,prefetch:!0,locale:!0,onClick:!0,onMouseEnter:!0,onTouchStart:!0,legacyBehavior:!0}).forEach(g=>{let D=typeof e[g];if(g===\"as\"){if(e[g]&&D!==\"string\"&&D!==\"object\")throw b({key:g,expected:\"`string` or `object`\",actual:D})}else if(g===\"locale\"){if(e[g]&&D!==\"string\")throw b({key:g,expected:\"`string`\",actual:D})}else if(g===\"onClick\"||g===\"onMouseEnter\"||g===\"onTouchStart\"){if(e[g]&&D!==\"function\")throw b({key:g,expected:\"`function`\",actual:D})}else if(g===\"replace\"||g===\"scroll\"||g===\"shallow\"||g===\"passHref\"||g===\"prefetch\"||g===\"legacyBehavior\"){if(e[g]!=null&&D!==\"boolean\")throw b({key:g,expected:\"`boolean`\",actual:D})}else{let Te=g}});let ve=I.default.useRef(!1);e.prefetch&&!ve.current&&!M&&(ve.current=!0,console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\"))}if(M&&!s){let b;if(typeof o==\"string\"?b=o:typeof o==\"object\"&&typeof o.pathname==\"string\"&&(b=o.pathname),b&&b.split(\"/\").some(ee=>ee.startsWith(\"[\")&&ee.endsWith(\"]\")))throw new Error(\"Dynamic href `\"+b+\"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\")}let{href:L,as:O}=I.default.useMemo(()=>{if(!_){let ee=gr(o);return{href:ee,as:s?gr(s):ee}}let[b,re]=(0,hr.resolveHref)(_,o,!0);return{href:b,as:s?(0,hr.resolveHref)(_,s):re||b}},[_,o,s]),ye=I.default.useRef(L),Ne=I.default.useRef(O),R;if(A){U&&console.warn('\"onClick\" was passed to <Link> with `href` of `'+o+'` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link'),ae&&console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `'+o+'` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');try{R=I.default.Children.only(i)}catch{throw i?new Error(\"Multiple children were passed to <Link> with `href` of `\"+o+\"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\"+(typeof window<\"u\"?` \nOpen your browser's console to view the Component stack trace.`:\"\")):new Error(\"No children were passed to <Link> with `href` of `\"+o+\"` but one child is required https://nextjs.org/docs/messages/link-no-children\")}}else if(i?.type===\"a\")throw new Error(`Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor`);let w=A?R&&typeof R==\"object\"&&R.ref:r,[J,ne,fe]=(0,as.useIntersection)({rootMargin:\"200px\"}),xe=I.default.useCallback(b=>{(Ne.current!==O||ye.current!==L)&&(fe(),Ne.current=O,ye.current=L),J(b),w&&(typeof w==\"function\"?w(b):typeof w==\"object\"&&(w.current=b))},[O,w,L,fe,J]);I.default.useEffect(()=>{},[O,L,ne,P,ie,_?.locale,z,M,le]);let oe={ref:xe,onClick(b){if(!b)throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');!A&&typeof U==\"function\"&&U(b),A&&R.props&&typeof R.props.onClick==\"function\"&&R.props.onClick(b),z&&(b.defaultPrevented||ds(b,z,L,O,p,v,S,P,M,ie))},onMouseEnter(b){!A&&typeof ae==\"function\"&&ae(b),A&&R.props&&typeof R.props.onMouseEnter==\"function\"&&R.props.onMouseEnter(b),z&&((!ie||!0)&&M||zt(z,L,O,{locale:P,priority:!0,bypassPrefetchedCheck:!0},{kind:le},M))},onTouchStart(b){!A&&typeof ge==\"function\"&&ge(b),A&&R.props&&typeof R.props.onTouchStart==\"function\"&&R.props.onTouchStart(b),z&&(!ie&&M||zt(z,L,O,{locale:P,priority:!0,bypassPrefetchedCheck:!0},{kind:le},M))}};if((0,rs.isAbsoluteUrl)(O))oe.href=O;else if(!A||u||R.type===\"a\"&&!(\"href\"in R.props)){let b=typeof P<\"u\"?P:_?.locale,re=_?.isLocaleDomain&&(0,ls.getDomainLocale)(O,b,_?.locales,_?.domainLocales);oe.href=re||(0,us.addBasePath)((0,is.addLocale)(O,b,_?.defaultLocale))}return A?I.default.cloneElement(R,oe):I.default.createElement(\"a\",{...te,...oe},i)}),ps=fs;(typeof Z.default==\"function\"||typeof Z.default==\"object\"&&Z.default!==null)&&typeof Z.default.__esModule>\"u\"&&(Object.defineProperty(Z.default,\"__esModule\",{value:!0}),Object.assign(Z.default,Z),Nr.exports=Z.default)});var Tr=h((Qs,vr)=>{vr.exports=xr()});var gs={};ei(gs,{default:()=>_s,faqData:()=>hs,frontmatter:()=>ms});var a=Jt(rn());function $e({children:t,quizSlug:e}){return React.createElement(\"article\",{className:\"max-w-4xl mx-auto text-gray-900\"},React.createElement(\"div\",{className:\"prose prose-lg max-w-none text-gray-900 [&>*]:text-gray-900\"},t))}var Pr=Jt(Tr());function Ie({task:t,href:e}){let r=e||`/quiz/${t}`;return React.createElement(\"div\",{className:\"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\"},React.createElement(\"div\",{className:\"text-center\"},React.createElement(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-2\"},\"Not sure which AI fits your workflow?\"),React.createElement(\"p\",{className:\"text-gray-600 mb-4\"},\"Take our 30-second quiz to get a personalized recommendation\"),React.createElement(Pr.default,{href:r,className:\"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\"},\"Take the \",t.charAt(0).toUpperCase()+t.slice(1),\" Quiz \\u2192\")))}var ms={title:\"Best AI for Coding & Debugging (2025) \\u2014 GPT-4o vs Claude 3.5, GitHub Copilot & more\",description:\"Developer-focused comparison of GPT-4o, Claude 3.5 Sonnet, GitHub Copilot, and Replit Ghostwriter. Find your perfect AI coding assistant.\",slug:\"best-ai-for-coding-2025-gpt-4o-vs-claude-github-copilot-more\",template:\"pillar\",cluster:\"code\",priority:\"High\",lastUpdated:\"2025-07-22\"},hs=[{q:\"What's the cheapest AI coder?\",a:\"For free options, GitHub Copilot offers free access for students and open-source contributors. For paid plans, most AI coding assistants are around $20/month, with GitHub Copilot Business at $19/month being slightly cheaper.\"},{q:\"Can AI write a full application?\",a:\"While AI can generate significant portions of an application, including boilerplate, functions, and UI components, it cannot yet write a complete, production-ready application from a single prompt without human supervision. It excels as a 'co-pilot' for assistance.\"},{q:\"Is GPT-4o good for debugging complex code?\",a:\"Yes, GPT-4o is excellent for debugging complex code due to its strong logical reasoning. However, for extremely large codebases, Claude 3.5 Sonnet's larger context window may have an advantage for understanding file relationships.\"},{q:\"Does GitHub Copilot steal your code?\",a:\"No, GitHub Copilot does not 'steal' your code. For enterprise users, GitHub has a strict policy that private code is not used to train public models. Enterprise licenses include IP indemnification for legal protection.\"},{q:\"Which AI is best for Python development?\",a:\"All major AI coding assistants handle Python well. GPT-4o excels at algorithmic problems, Claude 3.5 is great for large Python projects, and GitHub Copilot offers the best IDE integration for Python development.\"},{q:\"Can AI help with code reviews?\",a:\"Yes, AI can assist with code reviews by identifying potential bugs, suggesting improvements, and checking for best practices. GitHub Copilot integrates directly with PR workflows, while Claude 3.5's large context window is excellent for reviewing entire files.\"}];function Er(t){let e=Object.assign({h2:\"h2\",p:\"p\",hr:\"hr\",strong:\"strong\",h3:\"h3\",pre:\"pre\",code:\"code\",ul:\"ul\",li:\"li\",em:\"em\"},t.components);return(0,a.jsxDEV)($e,{quizSlug:\"coding\",children:[(0,a.jsxDEV)(Ie,{task:\"coding\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:16,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\"The AI Coding Landscape in 2025\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:18,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:'The conversation around AI coding assistants has moved far beyond simple autocomplete. In 2025, these tools are powerful collaborators capable of architecting systems, debugging multi-file repositories, and accelerating development cycles. But the fragmented market means the \"best\" AI is no longer a simple choice. The right tool depends entirely on your specific needs: low latency to maintain flow state, a massive context window for complex codebases, a deep plug-in ecosystem for your existing workflow, and robust licensing for enterprise security.'},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:20,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\"This guide provides a developer-focused comparison of the top contenders\\u2014GPT-4o, Claude 3.5 Sonnet, GitHub Copilot, and Replit Ghostwriter\\u2014to help you select the right AI co-pilot for your next project.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:22,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:24,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\"The AI Coder's Scorecard: Specs at a Glance\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:26,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\"For developers, specs matter. This chart breaks down the key models by what you care about most: cost, context, and core strengths.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:28,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[`| Model | Pricing (per user/month) | Context Window | Key Strength / Ecosystem |\n|-------|--------------------------|----------------|--------------------------|\n| `,(0,a.jsxDEV)(e.strong,{children:\"GPT-4o\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:32,columnNumber:3},this),` | ~$20 (API is usage-based) | 128k tokens | Versatility; a powerful \"second brain\" for logic and algorithms. |\n| `,(0,a.jsxDEV)(e.strong,{children:\"Claude 3.5 Sonnet\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:33,columnNumber:3},this),` | ~$20 (API is usage-based) | 200k tokens | Massive context for codebase analysis and complex refactoring. |\n| `,(0,a.jsxDEV)(e.strong,{children:\"GitHub Copilot\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:34,columnNumber:3},this),` | $19 (Business) / $39 (Enterprise) | Varies (uses GPT-4) | Deep integration with GitHub, VS Code, and the PR lifecycle. |\n| `,(0,a.jsxDEV)(e.strong,{children:\"Replit Ghostwriter\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:35,columnNumber:3},this),\" | $20 (Pro) / $50 (Teams) | Varies | Native to the Replit cloud IDE for seamless prototyping. |\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:30,columnNumber:1},this),(0,a.jsxDEV)(\"div\",{style:{textAlign:\"right\",fontSize:\"0.9rem\"},children:(0,a.jsxDEV)(\"a\",{href:\"/export/coding-scorecard.csv\",children:\"Export to Sheets \\u2192\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:37,columnNumber:52},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:37,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:39,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\"The Code Challenge: Simple Bugs vs. High-Context Flaws\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:41,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\"Not all bugs are created equal. Some are simple typos, while others are subtle logical flaws that hide deep within a large codebase. We tested the leading models with two distinct challenges to see where they shine and where they falter.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:43,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:\"Snippet 1: The Flawless Fix\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:45,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\"This simple Python function is meant to calculate the total price of items in a cart but has a common off-by-one error.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:47,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.strong,{children:\"Buggy Code:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:49,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:49,columnNumber:1},this),(0,a.jsxDEV)(e.pre,{children:(0,a.jsxDEV)(e.code,{className:\"language-python\",children:`def calculate_cart_total(prices):\n  total = 0\n  # Bug: range stops before the last index\n  for i in range(len(prices) - 1):\n    total += prices[i]\n  return total\n\ncart = [10, 25, 15, 5]\nprint(f\"Total: \\${calculate_cart_total(cart)}\") \n# Expected output: $55\n# Actual output: $50\n`},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:51,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:51,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\"Result:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:65,columnNumber:1},this),\" Every model tested\\u2014GPT-4o, Claude, Copilot, and Ghostwriter\\u2014fixed this instantly. They correctly identified that the loop failed to include the last item and adjusted \",(0,a.jsxDEV)(e.code,{children:\"range(len(prices) - 1)\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:65,columnNumber:180},this),\" to \",(0,a.jsxDEV)(e.code,{children:\"range(len(prices))\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:65,columnNumber:208},this),\". This is the table-stakes capability you should expect from any modern AI code generator.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:65,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:\"Snippet 2: The High-Context Challenge\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:67,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[\"This is where premium models prove their worth. The bug here is subtle. A utility function \",(0,a.jsxDEV)(e.code,{children:\"process_data\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:69,columnNumber:92},this),\" incorrectly uses a global \",(0,a.jsxDEV)(e.code,{children:\"TRANSACTION_FEE\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:69,columnNumber:133},this),\" variable, but this is only apparent when you see how \",(0,a.jsxDEV)(e.code,{children:\"process_data\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:69,columnNumber:204},this),\" is called by another function that has already applied a separate, regional tax. Only an AI that can hold the entire call stack in its context can spot the double charge.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:69,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.strong,{children:\"Buggy Code (in a large file):\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:71,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:71,columnNumber:1},this),(0,a.jsxDEV)(e.pre,{children:(0,a.jsxDEV)(e.code,{className:\"language-javascript\",children:`// Defined 500 lines earlier...\nconst TRANSACTION_FEE = 0.02; // 2% processing fee\n\nfunction process_data(items) {\n  let subtotal = items.reduce((acc, item) => acc + item.price, 0);\n  // Bug: This fee is applied redundantly, as the calling function handles taxes.\n  return subtotal * (1 + TRANSACTION_FEE); \n}\n\n// ... much later in the file ...\n\nfunction checkout_for_region(cart, region_config) {\n  let regional_total = cart.reduce((acc, item) => acc + item.price, 0);\n  \n  // Apply regional tax correctly\n  regional_total *= (1 + region_config.tax_rate);\n\n  // Send to processing, unaware that it adds another fee\n  const final_price = process_data(cart); // Should pass regional_total\n  \n  console.log(\\`Final price is: \\${final_price.toFixed(2)}\\`);\n}\n`},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:73,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:73,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.strong,{children:\"Result:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:98,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:98,columnNumber:1},this),(0,a.jsxDEV)(e.ul,{children:[`\n`,(0,a.jsxDEV)(e.li,{children:[`\n`,(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\"Lower-Context Models\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:100,columnNumber:3},this),\" typically suggest fixing \",(0,a.jsxDEV)(e.code,{children:\"process_data\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:100,columnNumber:53},this),\" in isolation, perhaps by adding a parameter to toggle the fee. They miss the reason it's wrong\\u2014the redundant call inside \",(0,a.jsxDEV)(e.code,{children:\"checkout_for_region\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:100,columnNumber:189},this),\".\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:100,columnNumber:3},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:100,columnNumber:1},this),`\n`,(0,a.jsxDEV)(e.li,{children:[`\n`,(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\"High-Context Models\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:102,columnNumber:3},this),\" (Claude 3.5 Sonnet & GPT-4o) excelled. They identified the core issue: \",(0,a.jsxDEV)(e.code,{children:\"checkout_for_region\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:102,columnNumber:98},this),\" performs its own calculation and then calls \",(0,a.jsxDEV)(e.code,{children:\"process_data\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:102,columnNumber:164},this),\" with the original cart, causing a redundant calculation and an extra fee. Claude, in particular, suggested refactoring \",(0,a.jsxDEV)(e.code,{children:\"checkout_for_region\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:102,columnNumber:298},this),\" to pass the \",(0,a.jsxDEV)(e.code,{children:\"regional_total\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:102,columnNumber:332},this),\" into \",(0,a.jsxDEV)(e.code,{children:\"process_data\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:102,columnNumber:354},this),\" and removing the fee logic from \",(0,a.jsxDEV)(e.code,{children:\"process_data\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:102,columnNumber:401},this),\" entirely, demonstrating a deep understanding of the entire file's logic.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:102,columnNumber:3},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:102,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:100,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:104,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\"The Enterprise Developer's Checklist\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:106,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\"For teams, choosing an AI coding assistant involves more than just performance\\u2014it's about security, licensing, and integration. Before committing, run your choice through this permissions checklist.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:108,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[\"\\u2610 \",(0,a.jsxDEV)(e.strong,{children:\"Data Privacy & Training\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:110,columnNumber:3},this),\": Does the provider offer a zero-retention policy, guaranteeing your proprietary code is never used for training their models? (Look for Enterprise or Business tiers).\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:110,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[\"\\u2610 \",(0,a.jsxDEV)(e.strong,{children:\"Licensing & Indemnification\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:112,columnNumber:3},this),\": Are the terms clear about the ownership of AI-generated code? Does the provider (like GitHub Copilot) offer intellectual property indemnification to protect your company from potential lawsuits?\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:112,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[\"\\u2610 \",(0,a.jsxDEV)(e.strong,{children:\"Seat Management & SSO\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:114,columnNumber:3},this),\": Can you manage user licenses from a central dashboard and integrate with your existing Single Sign-On (SSO) solution for secure access?\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:114,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[\"\\u2610 \",(0,a.jsxDEV)(e.strong,{children:\"Security Compliance\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:116,columnNumber:3},this),\": Is the tool compliant with industry standards like SOC 2 Type 2? This is non-negotiable for most enterprise environments.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:116,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[\"\\u2610 \",(0,a.jsxDEV)(e.strong,{children:\"IDE & Toolchain Integration\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:118,columnNumber:3},this),\": Does it offer first-party extensions for your team's preferred IDEs (VS Code, JetBrains) and version control systems? Seamless integration is key to adoption.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:118,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:120,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\"Deep-dive profiles\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:122,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\"GPT-4o \\u2014 \",(0,a.jsxDEV)(e.em,{children:\"the versatile problem-solver\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:124,columnNumber:14},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:124,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\"Strengths.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:126,columnNumber:1},this),` Excellent logical reasoning; handles multiple programming languages; strong algorithmic thinking.\n`,(0,a.jsxDEV)(e.strong,{children:\"Weaknesses.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:127,columnNumber:1},this),` Smaller context window than Claude; can be verbose in explanations.\n`,(0,a.jsxDEV)(e.em,{children:\"Perfect for: General development, algorithm design, multi-language projects.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:128,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:126,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\"Claude 3.5 Sonnet \\u2014 \",(0,a.jsxDEV)(e.em,{children:\"the codebase analyst\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:130,columnNumber:25},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:130,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\"Strengths.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:132,columnNumber:1},this),` Massive 200k token context; excellent at understanding large file relationships; thoughtful refactoring suggestions.\n`,(0,a.jsxDEV)(e.strong,{children:\"Weaknesses.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:133,columnNumber:1},this),` No native IDE integration yet; API-only access.\n`,(0,a.jsxDEV)(e.em,{children:\"Perfect for: Large codebase analysis, complex refactoring, architectural decisions.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:134,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:132,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\"GitHub Copilot \\u2014 \",(0,a.jsxDEV)(e.em,{children:\"the workflow integrator\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:136,columnNumber:22},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:136,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\"Strengths.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:138,columnNumber:1},this),` Seamless VS Code integration; understands Git context; PR and issue integration.\n`,(0,a.jsxDEV)(e.strong,{children:\"Weaknesses.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:139,columnNumber:1},this),` Limited to GitHub ecosystem; enterprise pricing can be steep.\n`,(0,a.jsxDEV)(e.em,{children:\"Perfect for: GitHub-based teams, VS Code users, integrated development workflows.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:140,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:138,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\"Replit Ghostwriter \\u2014 \",(0,a.jsxDEV)(e.em,{children:\"the rapid prototyper\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:142,columnNumber:26},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:142,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\"Strengths.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:144,columnNumber:1},this),` Instant deployment; browser-based development; great for learning and experimentation.\n`,(0,a.jsxDEV)(e.strong,{children:\"Weaknesses.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:145,columnNumber:1},this),` Limited to Replit environment; less suitable for complex enterprise projects.\n`,(0,a.jsxDEV)(e.em,{children:\"Perfect for: Rapid prototyping, educational projects, web-based development.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:146,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:144,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:148,columnNumber:1},this),(0,a.jsxDEV)(Ie,{task:\"coding\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:150,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:152,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\",lineNumber:14,columnNumber:1},this)}function bs(t={}){let{wrapper:e}=t.components||{};return e?(0,a.jsxDEV)(e,Object.assign({},t,{children:(0,a.jsxDEV)(Er,t,void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\"},this):Er(t)}var _s=bs;return ti(gs);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "pillars/best-ai-for-coding.mdx", "_raw": {"sourceFilePath": "pillars/best-ai-for-coding.mdx", "sourceFileName": "best-ai-for-coding.mdx", "sourceFileDir": "pillars", "contentType": "mdx", "flattenedPath": "pillars/best-ai-for-coding"}, "type": "<PERSON><PERSON>", "url": "/best-ai-for-coding-2025-gpt-4o-vs-claude-github-copilot-more", "slugFromPath": "pillars/best-ai-for-coding"}, "documentHash": "1753147055343", "hasWarnings": false, "documentTypeName": "<PERSON><PERSON>"}, "pillars/best-ai-for-writing.mdx": {"document": {"title": "Best AI for Writing (2025) — <PERSON> 3.5 vs GPT-4o, Gemini & more", "description": "Comprehensive comparison of Claude 3.5, GPT-4o, Gemini, Perplexity & Grok for bloggers, students, and marketers. Find your perfect AI writing assistant.", "slug": "best-ai-for-writing-2025-claude-3-5-vs-gpt-4o-gemini-more", "cluster": "text", "template": "pillar", "priority": "Low", "lastUpdated": "2025-07-22", "body": {"raw": "\nimport PillarLayout from \"@/templates/PillarLayout\"\nimport Quiz<PERSON>ta from \"@/components/QuizCta\"\n\n<PillarLayout quizSlug=\"writing\">\n\n<QuizCta task=\"writing\" />\n\n## Who are you writing for ?\n\n**The Blogger**\n\n- **Pain** – needs original long-form content that won't feel robotic or earn an SEO penalty.\n- **Ideal output** – an AI blog generator that keeps a consistent tone.\n- **Killer feature** – a huge context window to track details across thousands of words.\n\n**The Student**\n\n- **Pain** – must research, structure, and cite accurately while avoiding plagiarism.\n- **Ideal output** – an AI essay writer that returns verifiable facts with citations.\n- **Killer feature** – can ingest PDFs and analyse them directly.\n\n**The Marketer**\n\n- **Pain** – high-volume, mixed-format content plus brand-voice consistency.\n- **Ideal output** – a tool that plugs into Google Workspace and accelerates campaigns.\n- **Killer feature** – analyses spreadsheet data and builds project plans.\n\n---\n\n## A market of specialists, not one \"best\" model\n\nPerplexity is an **answer engine**, <PERSON> a **creative prose specialist**, and Gemini a **productivity layer** for Docs, Sheets, and Gmail. The takeaway: _choose by task_, not by raw IQ.\n\n> ### ⚠ Premium trap\n> The $20 Pro tiers are no longer the cutting edge. OpenAI, Anthropic, Perplexity, and xAI now sell $100–$300 \"Max / Heavy\" plans aimed at enterprises. For most writers the $20 tier remains the ROI sweet-spot, but know you're not buying the absolute top model.\n\n---\n\n## 2025 AI-writer scorecard\n\n| Model | Best for (archetype) | Stand-out feature | Context window | Free tier | Pro price | Key limitation |\n|-------|----------------------|-------------------|---------------|-----------|-----------|----------------|\n| **Claude 3.5 Sonnet** | Creative writing (Poet) | \"Artifacts\" live editor | 200 k tokens | Yes (daily cap) | $20 | No native real-time web search |\n| **GPT-4o** | Generalist (Polymath) | Multimodal + Custom GPTs | 128 k tokens | Yes (cap) | $20 | Output can feel robotic |\n| **Gemini Advanced** | Productivity (Producer) | Deep Workspace integration | 1 M + tokens | Yes (std) | $19.99 | Creative flair weaker than Claude |\n| **Perplexity Pro** | Research (Professor) | Clickable citations, Deep Research | — | Yes (cap) | $20 | Not a creative writer |\n| **Grok** | Real-time insights (Provocateur) | Live X / Twitter data | — | Yes (cap) | $30 | Pricey; edgy tone not for all |\n\n<div style={{textAlign:'right',fontSize:'0.9rem'}}><a href=\"/export/scorecard.csv\">Export to Sheets →</a></div>\n\n---\n\n## Speed test ⚡\n\n*[Speed comparison GIF placeholder - GPT-4o vs Claude 3.5 vs Gemini]*\n\nGPT-4o starts generating sooner and streams tokens faster, a win for brainstorms and quick Q & A. Claude is ~2× faster than its predecessor Opus but still trails GPT-4o on sheer responsiveness.\n\n---\n\n## Deep-dive profiles\n\n### Claude 3.5 Sonnet — _the creative wordsmith_\n\n**Strengths.** Thoughtful, expressive prose; 200 k-token context; \"Artifacts\" side-panel for iterative editing.\n**Weaknesses.** No built-in web browsing; free tier message cap.\n_Read the full [Claude 3.5 blogging review](/claude-3-5-for-blogging-review)._\n\n---\n\n### GPT-4o — _the versatile all-rounder_\n\nHandles writing, code, data and images in one chat. Custom GPTs unlock niche workflows.\nCriticisms: increasing verbosity, smaller window than Claude, privacy concerns.\n\n---\n\n### Gemini Advanced — _the integrated productivity engine_\n\nNative in Docs, Sheets, Gmail. Perfect for campaign tables, email summarising, Drive file look-ups.\nDeep dive: [Gemini for marketers](/gemini-advanced-for-marketers-guide).\n\n---\n\n### Perplexity Pro — _the research powerhouse_\n\nDelivers answers with numbered citations; \"Deep Research\" builds exhaustive reports.\nGuide: [How to use Perplexity for academic research](/how-to-use-perplexity-for-academic-research).\n\n---\n\n### Grok — _the real-time provocateur_\n\nLive social-media pulse plus a snarky attitude. Great for trend analysts, overkill for everyday writing.\n\n---\n\n<QuizCta task=\"writing\" />\n\n---\n\nexport const faqData = [\n  { q: \"What is the best free AI for writing?\", a: \"Perplexity offers the strongest free tier for fact-based writing, while Claude's free tier is best for creative prose.\" },\n  { q: \"Can Google penalise AI-generated content?\", a: \"Google ranks helpful content regardless of how it's produced; thin or spammy AI text can be penalised.\" },\n  { q: \"What's a context window and why does it matter?\", a: \"It's the amount of text an AI can 'remember'. Bigger windows (e.g., Claude's 200 k tokens) keep long documents coherent.\" },\n  { q: \"Which AI is best for creative writing?\", a: \"Claude 3.5 Sonnet consistently produces the most human-like, nuanced prose.\" },\n  { q: \"Which AI provides reliable citations?\", a: \"Perplexity Pro surfaces sources and clickable references by default.\" },\n  { q: \"Is GPT-4o still king in 2025?\", a: \"It's the best all-rounder, but Claude wins on style and Perplexity on accuracy.\" }\n]\n\n</PillarLayout>\n", "code": "var Component=(()=>{var Bn=Object.create;var we=Object.defineProperty;var Xn=Object.getOwnPropertyDescriptor;var Qn=Object.getOwnPropertyNames;var Zn=Object.getPrototypeOf,Jn=Object.prototype.hasOwnProperty;var h=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),ei=(t,e)=>{for(var n in e)we(t,n,{get:e[n],enumerable:!0})},Zt=(t,e,n,i)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let o of Qn(e))!Jn.call(t,o)&&o!==n&&we(t,o,{get:()=>e[o],enumerable:!(i=Xn(e,o))||i.enumerable});return t};var Jt=(t,e,n)=>(n=t!=null?Bn(Zn(t)):{},Zt(e||!t||!t.__esModule?we(n,\"default\",{value:t,enumerable:!0}):n,t)),ti=t=>Zt(we({},\"__esModule\",{value:!0}),t);var de=h((Ns,er)=>{er.exports=React});var tr=h($e=>{\"use strict\";(function(){\"use strict\";var t=de(),e=Symbol.for(\"react.element\"),n=Symbol.for(\"react.portal\"),i=Symbol.for(\"react.fragment\"),o=Symbol.for(\"react.strict_mode\"),s=Symbol.for(\"react.profiler\"),f=Symbol.for(\"react.provider\"),d=Symbol.for(\"react.context\"),u=Symbol.for(\"react.forward_ref\"),p=Symbol.for(\"react.suspense\"),x=Symbol.for(\"react.suspense_list\"),S=Symbol.for(\"react.memo\"),T=Symbol.for(\"react.lazy\"),U=Symbol.for(\"react.offscreen\"),le=Symbol.iterator,ge=\"@@iterator\";function I(r){if(r===null||typeof r!=\"object\")return null;var l=le&&r[le]||r[ge];return typeof l==\"function\"?l:null}var te=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function _(r){{for(var l=arguments.length,c=new Array(l>1?l-1:0),m=1;m<l;m++)c[m-1]=arguments[m];Ie(\"error\",r,c)}}function Ie(r,l,c){{var m=te.ReactDebugCurrentFrame,v=m.getStackAddendum();v!==\"\"&&(l+=\"%s\",c=c.concat([v]));var P=c.map(function(N){return String(N)});P.unshift(\"Warning: \"+l),Function.prototype.apply.call(console[r],console,P)}}var z=!1,M=!1,ie=!1,ae=!1,L=!1,O;O=Symbol.for(\"react.module.reference\");function ye(r){return!!(typeof r==\"string\"||typeof r==\"function\"||r===i||r===s||L||r===o||r===p||r===x||ae||r===U||z||M||ie||typeof r==\"object\"&&r!==null&&(r.$$typeof===T||r.$$typeof===S||r.$$typeof===f||r.$$typeof===d||r.$$typeof===u||r.$$typeof===O||r.getModuleId!==void 0))}function Ne(r,l,c){var m=r.displayName;if(m)return m;var v=l.displayName||l.name||\"\";return v!==\"\"?c+\"(\"+v+\")\":c}function E(r){return r.displayName||\"Context\"}function w(r){if(r==null)return null;if(typeof r.tag==\"number\"&&_(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof r==\"function\")return r.displayName||r.name||null;if(typeof r==\"string\")return r;switch(r){case i:return\"Fragment\";case n:return\"Portal\";case s:return\"Profiler\";case o:return\"StrictMode\";case p:return\"Suspense\";case x:return\"SuspenseList\"}if(typeof r==\"object\")switch(r.$$typeof){case d:var l=r;return E(l)+\".Consumer\";case f:var c=r;return E(c._context)+\".Provider\";case u:return Ne(r,r.render,\"ForwardRef\");case S:var m=r.displayName||null;return m!==null?m:w(r.type)||\"Memo\";case T:{var v=r,P=v._payload,N=v._init;try{return w(N(P))}catch{return null}}}return null}var J=Object.assign,re=0,fe,ve,oe,b,ne,ee,Ue;function We(){}We.__reactDisabledLog=!0;function xe(){{if(re===0){fe=console.log,ve=console.info,oe=console.warn,b=console.error,ne=console.group,ee=console.groupCollapsed,Ue=console.groupEnd;var r={configurable:!0,enumerable:!0,value:We,writable:!0};Object.defineProperties(console,{info:r,log:r,warn:r,error:r,group:r,groupCollapsed:r,groupEnd:r})}re++}}function g(){{if(re--,re===0){var r={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:J({},r,{value:fe}),info:J({},r,{value:ve}),warn:J({},r,{value:oe}),error:J({},r,{value:b}),group:J({},r,{value:ne}),groupCollapsed:J({},r,{value:ee}),groupEnd:J({},r,{value:Ue})})}re<0&&_(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var D=te.ReactCurrentDispatcher,Pe;function Te(r,l,c){{if(Pe===void 0)try{throw Error()}catch(v){var m=v.stack.trim().match(/\\n( *(at )?)/);Pe=m&&m[1]||\"\"}return`\n`+Pe+r}}var ze=!1,Re;{var kn=typeof WeakMap==\"function\"?WeakMap:Map;Re=new kn}function Mt(r,l){if(!r||ze)return\"\";{var c=Re.get(r);if(c!==void 0)return c}var m;ze=!0;var v=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var P;P=D.current,D.current=null,xe();try{if(l){var N=function(){throw Error()};if(Object.defineProperty(N.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(N,[])}catch(j){m=j}Reflect.construct(r,[],N)}else{try{N.call()}catch(j){m=j}r.call(N.prototype)}}else{try{throw Error()}catch(j){m=j}r()}}catch(j){if(j&&m&&typeof j.stack==\"string\"){for(var y=j.stack.split(`\n`),C=m.stack.split(`\n`),R=y.length-1,k=C.length-1;R>=1&&k>=0&&y[R]!==C[k];)k--;for(;R>=1&&k>=0;R--,k--)if(y[R]!==C[k]){if(R!==1||k!==1)do if(R--,k--,k<0||y[R]!==C[k]){var W=`\n`+y[R].replace(\" at new \",\" at \");return r.displayName&&W.includes(\"<anonymous>\")&&(W=W.replace(\"<anonymous>\",r.displayName)),typeof r==\"function\"&&Re.set(r,W),W}while(R>=1&&k>=0);break}}}finally{ze=!1,D.current=P,g(),Error.prepareStackTrace=v}var ce=r?r.displayName||r.name:\"\",se=ce?Te(ce):\"\";return typeof r==\"function\"&&Re.set(r,se),se}function En(r,l,c){return Mt(r,!1)}function wn(r){var l=r.prototype;return!!(l&&l.isReactComponent)}function ke(r,l,c){if(r==null)return\"\";if(typeof r==\"function\")return Mt(r,wn(r));if(typeof r==\"string\")return Te(r);switch(r){case p:return Te(\"Suspense\");case x:return Te(\"SuspenseList\")}if(typeof r==\"object\")switch(r.$$typeof){case u:return En(r.render);case S:return ke(r.type,l,c);case T:{var m=r,v=m._payload,P=m._init;try{return ke(P(v),l,c)}catch{}}}return\"\"}var pe=Object.prototype.hasOwnProperty,Lt={},qt=te.ReactDebugCurrentFrame;function Ee(r){if(r){var l=r._owner,c=ke(r.type,r._source,l?l.type:null);qt.setExtraStackFrame(c)}else qt.setExtraStackFrame(null)}function On(r,l,c,m,v){{var P=Function.call.bind(pe);for(var N in r)if(P(r,N)){var y=void 0;try{if(typeof r[N]!=\"function\"){var C=Error((m||\"React class\")+\": \"+c+\" type `\"+N+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof r[N]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw C.name=\"Invariant Violation\",C}y=r[N](l,N,m,c,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(R){y=R}y&&!(y instanceof Error)&&(Ee(v),_(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",m||\"React class\",c,N,typeof y),Ee(null)),y instanceof Error&&!(y.message in Lt)&&(Lt[y.message]=!0,Ee(v),_(\"Failed %s type: %s\",c,y.message),Ee(null))}}}var Cn=Array.isArray;function Me(r){return Cn(r)}function Sn(r){{var l=typeof Symbol==\"function\"&&Symbol.toStringTag,c=l&&r[Symbol.toStringTag]||r.constructor.name||\"Object\";return c}}function Dn(r){try{return Ft(r),!1}catch{return!0}}function Ft(r){return\"\"+r}function Gt(r){if(Dn(r))return _(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",Sn(r)),Ft(r)}var me=te.ReactCurrentOwner,jn={key:!0,ref:!0,__self:!0,__source:!0},Vt,$t,Le;Le={};function An(r){if(pe.call(r,\"ref\")){var l=Object.getOwnPropertyDescriptor(r,\"ref\").get;if(l&&l.isReactWarning)return!1}return r.ref!==void 0}function In(r){if(pe.call(r,\"key\")){var l=Object.getOwnPropertyDescriptor(r,\"key\").get;if(l&&l.isReactWarning)return!1}return r.key!==void 0}function Un(r,l){if(typeof r.ref==\"string\"&&me.current&&l&&me.current.stateNode!==l){var c=w(me.current.type);Le[c]||(_('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',w(me.current.type),r.ref),Le[c]=!0)}}function Wn(r,l){{var c=function(){Vt||(Vt=!0,_(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",l))};c.isReactWarning=!0,Object.defineProperty(r,\"key\",{get:c,configurable:!0})}}function zn(r,l){{var c=function(){$t||($t=!0,_(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",l))};c.isReactWarning=!0,Object.defineProperty(r,\"ref\",{get:c,configurable:!0})}}var Mn=function(r,l,c,m,v,P,N){var y={$$typeof:e,type:r,key:l,ref:c,props:N,_owner:P};return y._store={},Object.defineProperty(y._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(y,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:m}),Object.defineProperty(y,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:v}),Object.freeze&&(Object.freeze(y.props),Object.freeze(y)),y};function Ln(r,l,c,m,v){{var P,N={},y=null,C=null;c!==void 0&&(Gt(c),y=\"\"+c),In(l)&&(Gt(l.key),y=\"\"+l.key),An(l)&&(C=l.ref,Un(l,v));for(P in l)pe.call(l,P)&&!jn.hasOwnProperty(P)&&(N[P]=l[P]);if(r&&r.defaultProps){var R=r.defaultProps;for(P in R)N[P]===void 0&&(N[P]=R[P])}if(y||C){var k=typeof r==\"function\"?r.displayName||r.name||\"Unknown\":r;y&&Wn(N,k),C&&zn(N,k)}return Mn(r,y,C,v,m,me.current,N)}}var qe=te.ReactCurrentOwner,Ht=te.ReactDebugCurrentFrame;function ue(r){if(r){var l=r._owner,c=ke(r.type,r._source,l?l.type:null);Ht.setExtraStackFrame(c)}else Ht.setExtraStackFrame(null)}var Fe;Fe=!1;function Ge(r){return typeof r==\"object\"&&r!==null&&r.$$typeof===e}function Kt(){{if(qe.current){var r=w(qe.current.type);if(r)return`\n\nCheck the render method of \\``+r+\"`.\"}return\"\"}}function qn(r){{if(r!==void 0){var l=r.fileName.replace(/^.*[\\\\\\/]/,\"\"),c=r.lineNumber;return`\n\nCheck your code at `+l+\":\"+c+\".\"}return\"\"}}var Yt={};function Fn(r){{var l=Kt();if(!l){var c=typeof r==\"string\"?r:r.displayName||r.name;c&&(l=`\n\nCheck the top-level render call using <`+c+\">.\")}return l}}function Bt(r,l){{if(!r._store||r._store.validated||r.key!=null)return;r._store.validated=!0;var c=Fn(l);if(Yt[c])return;Yt[c]=!0;var m=\"\";r&&r._owner&&r._owner!==qe.current&&(m=\" It was passed a child from \"+w(r._owner.type)+\".\"),ue(r),_('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',c,m),ue(null)}}function Xt(r,l){{if(typeof r!=\"object\")return;if(Me(r))for(var c=0;c<r.length;c++){var m=r[c];Ge(m)&&Bt(m,l)}else if(Ge(r))r._store&&(r._store.validated=!0);else if(r){var v=I(r);if(typeof v==\"function\"&&v!==r.entries)for(var P=v.call(r),N;!(N=P.next()).done;)Ge(N.value)&&Bt(N.value,l)}}}function Gn(r){{var l=r.type;if(l==null||typeof l==\"string\")return;var c;if(typeof l==\"function\")c=l.propTypes;else if(typeof l==\"object\"&&(l.$$typeof===u||l.$$typeof===S))c=l.propTypes;else return;if(c){var m=w(l);On(c,r.props,\"prop\",m,r)}else if(l.PropTypes!==void 0&&!Fe){Fe=!0;var v=w(l);_(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",v||\"Unknown\")}typeof l.getDefaultProps==\"function\"&&!l.getDefaultProps.isReactClassApproved&&_(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function Vn(r){{for(var l=Object.keys(r.props),c=0;c<l.length;c++){var m=l[c];if(m!==\"children\"&&m!==\"key\"){ue(r),_(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",m),ue(null);break}}r.ref!==null&&(ue(r),_(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),ue(null))}}var Qt={};function $n(r,l,c,m,v,P){{var N=ye(r);if(!N){var y=\"\";(r===void 0||typeof r==\"object\"&&r!==null&&Object.keys(r).length===0)&&(y+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var C=qn(v);C?y+=C:y+=Kt();var R;r===null?R=\"null\":Me(r)?R=\"array\":r!==void 0&&r.$$typeof===e?(R=\"<\"+(w(r.type)||\"Unknown\")+\" />\",y=\" Did you accidentally export a JSX literal instead of a component?\"):R=typeof r,_(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",R,y)}var k=Ln(r,l,c,v,P);if(k==null)return k;if(N){var W=l.children;if(W!==void 0)if(m)if(Me(W)){for(var ce=0;ce<W.length;ce++)Xt(W[ce],r);Object.freeze&&Object.freeze(W)}else _(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else Xt(W,r)}if(pe.call(l,\"key\")){var se=w(r),j=Object.keys(l).filter(function(Yn){return Yn!==\"key\"}),Ve=j.length>0?\"{key: someKey, \"+j.join(\": ..., \")+\": ...}\":\"{key: someKey}\";if(!Qt[se+Ve]){var Kn=j.length>0?\"{\"+j.join(\": ..., \")+\": ...}\":\"{}\";_(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,Ve,se,Kn,se),Qt[se+Ve]=!0}}return r===i?Vn(k):Gn(k),k}}var Hn=$n;$e.Fragment=i,$e.jsxDEV=Hn})()});var nr=h((xs,rr)=>{\"use strict\";rr.exports=tr()});var Oe=h(Ke=>{\"use strict\";Ke._=Ke._interop_require_default=ri;function ri(t){return t&&t.__esModule?t:{default:t}}});var Be=h(Ye=>{\"use strict\";Object.defineProperty(Ye,\"__esModule\",{value:!0});function ni(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}ni(Ye,{searchParamsToUrlQuery:function(){return ii},urlQueryToSearchParams:function(){return oi},assign:function(){return si}});function ii(t){let e={};return t.forEach((n,i)=>{typeof e[i]>\"u\"?e[i]=n:Array.isArray(e[i])?e[i].push(n):e[i]=[e[i],n]}),e}function ir(t){return typeof t==\"string\"||typeof t==\"number\"&&!isNaN(t)||typeof t==\"boolean\"?String(t):\"\"}function oi(t){let e=new URLSearchParams;return Object.entries(t).forEach(n=>{let[i,o]=n;Array.isArray(o)?o.forEach(s=>e.append(i,ir(s))):e.set(i,ir(o))}),e}function si(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return n.forEach(o=>{Array.from(o.keys()).forEach(s=>t.delete(s)),o.forEach((s,f)=>t.append(f,s))}),t}});var sr=h(Xe=>{\"use strict\";function or(t){if(typeof WeakMap!=\"function\")return null;var e=new WeakMap,n=new WeakMap;return(or=function(i){return i?n:e})(t)}Xe._=Xe._interop_require_wildcard=li;function li(t,e){if(!e&&t&&t.__esModule)return t;if(t===null||typeof t!=\"object\"&&typeof t!=\"function\")return{default:t};var n=or(e);if(n&&n.has(t))return n.get(t);var i={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if(s!==\"default\"&&Object.prototype.hasOwnProperty.call(t,s)){var f=o?Object.getOwnPropertyDescriptor(t,s):null;f&&(f.get||f.set)?Object.defineProperty(i,s,f):i[s]=t[s]}return i.default=t,n&&n.set(t,i),i}});var Ze=h(Qe=>{\"use strict\";Object.defineProperty(Qe,\"__esModule\",{value:!0});function ai(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}ai(Qe,{formatUrl:function(){return lr},urlObjectKeys:function(){return ar},formatWithValidation:function(){return fi}});var ui=sr(),ci=ui._(Be()),di=/https?|ftp|gopher|file/;function lr(t){let{auth:e,hostname:n}=t,i=t.protocol||\"\",o=t.pathname||\"\",s=t.hash||\"\",f=t.query||\"\",d=!1;e=e?encodeURIComponent(e).replace(/%3A/i,\":\")+\"@\":\"\",t.host?d=e+t.host:n&&(d=e+(~n.indexOf(\":\")?\"[\"+n+\"]\":n),t.port&&(d+=\":\"+t.port)),f&&typeof f==\"object\"&&(f=String(ci.urlQueryToSearchParams(f)));let u=t.search||f&&\"?\"+f||\"\";return i&&!i.endsWith(\":\")&&(i+=\":\"),t.slashes||(!i||di.test(i))&&d!==!1?(d=\"//\"+(d||\"\"),o&&o[0]!==\"/\"&&(o=\"/\"+o)):d||(d=\"\"),s&&s[0]!==\"#\"&&(s=\"#\"+s),u&&u[0]!==\"?\"&&(u=\"?\"+u),o=o.replace(/[?#]/g,encodeURIComponent),u=u.replace(\"#\",\"%23\"),\"\"+i+d+o+u+s}var ar=[\"auth\",\"hash\",\"host\",\"hostname\",\"href\",\"path\",\"pathname\",\"port\",\"protocol\",\"query\",\"search\",\"slashes\"];function fi(t){return t!==null&&typeof t==\"object\"&&Object.keys(t).forEach(e=>{ar.includes(e)||console.warn(\"Unknown key passed via urlObject into url.format: \"+e)}),lr(t)}});var ur=h(Je=>{\"use strict\";Object.defineProperty(Je,\"__esModule\",{value:!0});Object.defineProperty(Je,\"omit\",{enumerable:!0,get:function(){return pi}});function pi(t,e){let n={};return Object.keys(t).forEach(i=>{e.includes(i)||(n[i]=t[i])}),n}});var he=h(ot=>{\"use strict\";Object.defineProperty(ot,\"__esModule\",{value:!0});function mi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}mi(ot,{WEB_VITALS:function(){return hi},execOnce:function(){return bi},isAbsoluteUrl:function(){return gi},getLocationOrigin:function(){return cr},getURL:function(){return yi},getDisplayName:function(){return Ce},isResSent:function(){return dr},normalizeRepeatedSlashes:function(){return Ni},loadGetInitialProps:function(){return fr},SP:function(){return pr},ST:function(){return vi},DecodeError:function(){return et},NormalizeError:function(){return tt},PageNotFoundError:function(){return rt},MissingStaticPage:function(){return nt},MiddlewareNotFoundError:function(){return it},stringifyError:function(){return xi}});var hi=[\"CLS\",\"FCP\",\"FID\",\"INP\",\"LCP\",\"TTFB\"];function bi(t){let e=!1,n;return function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return e||(e=!0,n=t(...o)),n}}var _i=/^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/,gi=t=>_i.test(t);function cr(){let{protocol:t,hostname:e,port:n}=window.location;return t+\"//\"+e+(n?\":\"+n:\"\")}function yi(){let{href:t}=window.location,e=cr();return t.substring(e.length)}function Ce(t){return typeof t==\"string\"?t:t.displayName||t.name||\"Unknown\"}function dr(t){return t.finished||t.headersSent}function Ni(t){let e=t.split(\"?\");return e[0].replace(/\\\\/g,\"/\").replace(/\\/\\/+/g,\"/\")+(e[1]?\"?\"+e.slice(1).join(\"?\"):\"\")}async function fr(t,e){var n;if((n=t.prototype)!=null&&n.getInitialProps){let s='\"'+Ce(t)+'.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';throw new Error(s)}let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await fr(e.Component,e.ctx)}:{};let o=await t.getInitialProps(e);if(i&&dr(i))return o;if(!o){let s='\"'+Ce(t)+'.getInitialProps()\" should resolve to an object. But found \"'+o+'\" instead.';throw new Error(s)}return Object.keys(o).length===0&&!e.ctx&&console.warn(\"\"+Ce(t)+\" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\"),o}var pr=typeof performance<\"u\",vi=pr&&[\"mark\",\"measure\",\"getEntriesByName\"].every(t=>typeof performance[t]==\"function\"),et=class extends Error{},tt=class extends Error{},rt=class extends Error{constructor(e){super(),this.code=\"ENOENT\",this.name=\"PageNotFoundError\",this.message=\"Cannot find module for page: \"+e}},nt=class extends Error{constructor(e,n){super(),this.message=\"Failed to load static file for page: \"+e+\" \"+n}},it=class extends Error{constructor(){super(),this.code=\"ENOENT\",this.message=\"Cannot find the middleware module\"}};function xi(t){return JSON.stringify({message:t.message,stack:t.stack})}});var lt=h(st=>{\"use strict\";Object.defineProperty(st,\"__esModule\",{value:!0});Object.defineProperty(st,\"removeTrailingSlash\",{enumerable:!0,get:function(){return Pi}});function Pi(t){return t.replace(/\\/$/,\"\")||\"/\"}});var Se=h(at=>{\"use strict\";Object.defineProperty(at,\"__esModule\",{value:!0});Object.defineProperty(at,\"parsePath\",{enumerable:!0,get:function(){return Ti}});function Ti(t){let e=t.indexOf(\"#\"),n=t.indexOf(\"?\"),i=n>-1&&(e<0||n<e);return i||e>-1?{pathname:t.substring(0,i?n:e),query:i?t.substring(n,e>-1?e:void 0):\"\",hash:e>-1?t.slice(e):\"\"}:{pathname:t,query:\"\",hash:\"\"}}});var be=h((q,hr)=>{\"use strict\";Object.defineProperty(q,\"__esModule\",{value:!0});Object.defineProperty(q,\"normalizePathTrailingSlash\",{enumerable:!0,get:function(){return ki}});var mr=lt(),Ri=Se(),ki=t=>{if(!t.startsWith(\"/\")||process.env.__NEXT_MANUAL_TRAILING_SLASH)return t;let{pathname:e,query:n,hash:i}=(0,Ri.parsePath)(t);return process.env.__NEXT_TRAILING_SLASH?/\\.[^/]+\\/?$/.test(e)?\"\"+(0,mr.removeTrailingSlash)(e)+n+i:e.endsWith(\"/\")?\"\"+e+n+i:e+\"/\"+n+i:\"\"+(0,mr.removeTrailingSlash)(e)+n+i};(typeof q.default==\"function\"||typeof q.default==\"object\"&&q.default!==null)&&typeof q.default.__esModule>\"u\"&&(Object.defineProperty(q.default,\"__esModule\",{value:!0}),Object.assign(q.default,q),hr.exports=q.default)});var ct=h(ut=>{\"use strict\";Object.defineProperty(ut,\"__esModule\",{value:!0});Object.defineProperty(ut,\"pathHasPrefix\",{enumerable:!0,get:function(){return wi}});var Ei=Se();function wi(t,e){if(typeof t!=\"string\")return!1;let{pathname:n}=(0,Ei.parsePath)(t);return n===e||n.startsWith(e+\"/\")}});var _r=h((F,br)=>{\"use strict\";Object.defineProperty(F,\"__esModule\",{value:!0});Object.defineProperty(F,\"hasBasePath\",{enumerable:!0,get:function(){return Si}});var Oi=ct(),Ci=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Si(t){return(0,Oi.pathHasPrefix)(t,Ci)}(typeof F.default==\"function\"||typeof F.default==\"object\"&&F.default!==null)&&typeof F.default.__esModule>\"u\"&&(Object.defineProperty(F.default,\"__esModule\",{value:!0}),Object.assign(F.default,F),br.exports=F.default)});var ft=h(dt=>{\"use strict\";Object.defineProperty(dt,\"__esModule\",{value:!0});Object.defineProperty(dt,\"isLocalURL\",{enumerable:!0,get:function(){return ji}});var gr=he(),Di=_r();function ji(t){if(!(0,gr.isAbsoluteUrl)(t))return!0;try{let e=(0,gr.getLocationOrigin)(),n=new URL(t,e);return n.origin===e&&(0,Di.hasBasePath)(n.pathname)}catch{return!1}}});var yr=h(mt=>{\"use strict\";Object.defineProperty(mt,\"__esModule\",{value:!0});Object.defineProperty(mt,\"getSortedRoutes\",{enumerable:!0,get:function(){return Ai}});var pt=class t{insert(e){this._insert(e.split(\"/\").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){e===void 0&&(e=\"/\");let n=[...this.children.keys()].sort();this.slugName!==null&&n.splice(n.indexOf(\"[]\"),1),this.restSlugName!==null&&n.splice(n.indexOf(\"[...]\"),1),this.optionalRestSlugName!==null&&n.splice(n.indexOf(\"[[...]]\"),1);let i=n.map(o=>this.children.get(o)._smoosh(\"\"+e+o+\"/\")).reduce((o,s)=>[...o,...s],[]);if(this.slugName!==null&&i.push(...this.children.get(\"[]\")._smoosh(e+\"[\"+this.slugName+\"]/\")),!this.placeholder){let o=e===\"/\"?\"/\":e.slice(0,-1);if(this.optionalRestSlugName!=null)throw new Error('You cannot define a route with the same specificity as a optional catch-all route (\"'+o+'\" and \"'+o+\"[[...\"+this.optionalRestSlugName+']]\").');i.unshift(o)}return this.restSlugName!==null&&i.push(...this.children.get(\"[...]\")._smoosh(e+\"[...\"+this.restSlugName+\"]/\")),this.optionalRestSlugName!==null&&i.push(...this.children.get(\"[[...]]\")._smoosh(e+\"[[...\"+this.optionalRestSlugName+\"]]/\")),i}_insert(e,n,i){if(e.length===0){this.placeholder=!1;return}if(i)throw new Error(\"Catch-all must be the last part of the URL.\");let o=e[0];if(o.startsWith(\"[\")&&o.endsWith(\"]\")){let d=function(u,p){if(u!==null&&u!==p)throw new Error(\"You cannot use different slug names for the same dynamic path ('\"+u+\"' !== '\"+p+\"').\");n.forEach(x=>{if(x===p)throw new Error('You cannot have the same slug name \"'+p+'\" repeat within a single dynamic path');if(x.replace(/\\W/g,\"\")===o.replace(/\\W/g,\"\"))throw new Error('You cannot have the slug names \"'+x+'\" and \"'+p+'\" differ only by non-word symbols within a single dynamic path')}),n.push(p)},s=o.slice(1,-1),f=!1;if(s.startsWith(\"[\")&&s.endsWith(\"]\")&&(s=s.slice(1,-1),f=!0),s.startsWith(\"...\")&&(s=s.substring(3),i=!0),s.startsWith(\"[\")||s.endsWith(\"]\"))throw new Error(\"Segment names may not start or end with extra brackets ('\"+s+\"').\");if(s.startsWith(\".\"))throw new Error(\"Segment names may not start with erroneous periods ('\"+s+\"').\");if(i)if(f){if(this.restSlugName!=null)throw new Error('You cannot use both an required and optional catch-all route at the same level (\"[...'+this.restSlugName+']\" and \"'+e[0]+'\" ).');d(this.optionalRestSlugName,s),this.optionalRestSlugName=s,o=\"[[...]]\"}else{if(this.optionalRestSlugName!=null)throw new Error('You cannot use both an optional and required catch-all route at the same level (\"[[...'+this.optionalRestSlugName+']]\" and \"'+e[0]+'\").');d(this.restSlugName,s),this.restSlugName=s,o=\"[...]\"}else{if(f)throw new Error('Optional route parameters are not yet supported (\"'+e[0]+'\").');d(this.slugName,s),this.slugName=s,o=\"[]\"}}this.children.has(o)||this.children.set(o,new t),this.children.get(o)._insert(e.slice(1),n,i)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}};function Ai(t){let e=new pt;return t.forEach(n=>e.insert(n)),e.smoosh()}});var Nr=h(ht=>{\"use strict\";Object.defineProperty(ht,\"__esModule\",{value:!0});Object.defineProperty(ht,\"isDynamicRoute\",{enumerable:!0,get:function(){return Ui}});var Ii=/\\/\\[[^/]+?\\](?=\\/|$)/;function Ui(t){return Ii.test(t)}});var vr=h(bt=>{\"use strict\";Object.defineProperty(bt,\"__esModule\",{value:!0});function Wi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Wi(bt,{getSortedRoutes:function(){return zi.getSortedRoutes},isDynamicRoute:function(){return Mi.isDynamicRoute}});var zi=yr(),Mi=Nr()});var xr=h(_t=>{\"use strict\";Object.defineProperty(_t,\"__esModule\",{value:!0});Object.defineProperty(_t,\"getRouteMatcher\",{enumerable:!0,get:function(){return qi}});var Li=he();function qi(t){let{re:e,groups:n}=t;return i=>{let o=e.exec(i);if(!o)return!1;let s=d=>{try{return decodeURIComponent(d)}catch{throw new Li.DecodeError(\"failed to decode param\")}},f={};return Object.keys(n).forEach(d=>{let u=n[d],p=o[u.pos];p!==void 0&&(f[d]=~p.indexOf(\"/\")?p.split(\"/\").map(x=>s(x)):u.repeat?[s(p)]:s(p))}),f}}});var Pr=h(gt=>{\"use strict\";Object.defineProperty(gt,\"__esModule\",{value:!0});Object.defineProperty(gt,\"ensureLeadingSlash\",{enumerable:!0,get:function(){return Fi}});function Fi(t){return t.startsWith(\"/\")?t:\"/\"+t}});var Tr=h(yt=>{\"use strict\";Object.defineProperty(yt,\"__esModule\",{value:!0});Object.defineProperty(yt,\"isGroupSegment\",{enumerable:!0,get:function(){return Gi}});function Gi(t){return t[0]===\"(\"&&t.endsWith(\")\")}});var Rr=h(Nt=>{\"use strict\";Object.defineProperty(Nt,\"__esModule\",{value:!0});function Vi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Vi(Nt,{normalizeAppPath:function(){return Ki},normalizeRscPath:function(){return Yi}});var $i=Pr(),Hi=Tr();function Ki(t){return(0,$i.ensureLeadingSlash)(t.split(\"/\").reduce((e,n,i,o)=>!n||(0,Hi.isGroupSegment)(n)||n[0]===\"@\"||(n===\"page\"||n===\"route\")&&i===o.length-1?e:e+\"/\"+n,\"\"))}function Yi(t,e){return e?t.replace(/\\.rsc($|\\?)/,\"$1\"):t}});var kr=h(xt=>{\"use strict\";Object.defineProperty(xt,\"__esModule\",{value:!0});function Bi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Bi(xt,{INTERCEPTION_ROUTE_MARKERS:function(){return vt},isInterceptionRouteAppPath:function(){return Qi},extractInterceptionRouteInformation:function(){return Zi}});var Xi=Rr(),vt=[\"(..)(..)\",\"(.)\",\"(..)\",\"(...)\"];function Qi(t){return t.split(\"/\").find(e=>vt.find(n=>e.startsWith(n)))!==void 0}function Zi(t){let e,n,i;for(let o of t.split(\"/\"))if(n=vt.find(s=>o.startsWith(s)),n){[e,i]=t.split(n,2);break}if(!e||!n||!i)throw new Error(`Invalid interception route: ${t}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(e=(0,Xi.normalizeAppPath)(e),n){case\"(.)\":e===\"/\"?i=`/${i}`:i=e+\"/\"+i;break;case\"(..)\":if(e===\"/\")throw new Error(`Invalid interception route: ${t}. Cannot use (..) marker at the root level, use (.) instead.`);i=e.split(\"/\").slice(0,-1).concat(i).join(\"/\");break;case\"(...)\":i=\"/\"+i;break;case\"(..)(..)\":let o=e.split(\"/\");if(o.length<=2)throw new Error(`Invalid interception route: ${t}. Cannot use (..)(..) marker at the root level or one level up.`);i=o.slice(0,-2).concat(i).join(\"/\");break;default:throw new Error(\"Invariant: unexpected marker\")}return{interceptingRoute:e,interceptedRoute:i}}});var Er=h(Pt=>{\"use strict\";Object.defineProperty(Pt,\"__esModule\",{value:!0});Object.defineProperty(Pt,\"escapeStringRegexp\",{enumerable:!0,get:function(){return to}});var Ji=/[|\\\\{}()[\\]^$+*?.-]/,eo=/[|\\\\{}()[\\]^$+*?.-]/g;function to(t){return Ji.test(t)?t.replace(eo,\"\\\\$&\"):t}});var Ar=h(kt=>{\"use strict\";Object.defineProperty(kt,\"__esModule\",{value:!0});function ro(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}ro(kt,{getRouteRegex:function(){return Dr},getNamedRouteRegex:function(){return so},getNamedMiddlewareRegex:function(){return lo}});var Or=kr(),Tt=Er(),Cr=lt(),no=\"nxtP\",io=\"nxtI\";function Rt(t){let e=t.startsWith(\"[\")&&t.endsWith(\"]\");e&&(t=t.slice(1,-1));let n=t.startsWith(\"...\");return n&&(t=t.slice(3)),{key:t,repeat:n,optional:e}}function Sr(t){let e=(0,Cr.removeTrailingSlash)(t).slice(1).split(\"/\"),n={},i=1;return{parameterizedRoute:e.map(o=>{let s=Or.INTERCEPTION_ROUTE_MARKERS.find(d=>o.startsWith(d)),f=o.match(/\\[((?:\\[.*\\])|.+)\\]/);if(s&&f){let{key:d,optional:u,repeat:p}=Rt(f[1]);return n[d]={pos:i++,repeat:p,optional:u},\"/\"+(0,Tt.escapeStringRegexp)(s)+\"([^/]+?)\"}else if(f){let{key:d,repeat:u,optional:p}=Rt(f[1]);return n[d]={pos:i++,repeat:u,optional:p},u?p?\"(?:/(.+?))?\":\"/(.+?)\":\"/([^/]+?)\"}else return\"/\"+(0,Tt.escapeStringRegexp)(o)}).join(\"\"),groups:n}}function Dr(t){let{parameterizedRoute:e,groups:n}=Sr(t);return{re:new RegExp(\"^\"+e+\"(?:/)?$\"),groups:n}}function oo(){let t=0;return()=>{let e=\"\",n=++t;for(;n>0;)e+=String.fromCharCode(97+(n-1)%26),n=Math.floor((n-1)/26);return e}}function wr(t){let{getSafeRouteKey:e,segment:n,routeKeys:i,keyPrefix:o}=t,{key:s,optional:f,repeat:d}=Rt(n),u=s.replace(/\\W/g,\"\");o&&(u=\"\"+o+u);let p=!1;return(u.length===0||u.length>30)&&(p=!0),isNaN(parseInt(u.slice(0,1)))||(p=!0),p&&(u=e()),o?i[u]=\"\"+o+s:i[u]=\"\"+s,d?f?\"(?:/(?<\"+u+\">.+?))?\":\"/(?<\"+u+\">.+?)\":\"/(?<\"+u+\">[^/]+?)\"}function jr(t,e){let n=(0,Cr.removeTrailingSlash)(t).slice(1).split(\"/\"),i=oo(),o={};return{namedParameterizedRoute:n.map(s=>{let f=Or.INTERCEPTION_ROUTE_MARKERS.some(u=>s.startsWith(u)),d=s.match(/\\[((?:\\[.*\\])|.+)\\]/);return f&&d?wr({getSafeRouteKey:i,segment:d[1],routeKeys:o,keyPrefix:e?io:void 0}):d?wr({getSafeRouteKey:i,segment:d[1],routeKeys:o,keyPrefix:e?no:void 0}):\"/\"+(0,Tt.escapeStringRegexp)(s)}).join(\"\"),routeKeys:o}}function so(t,e){let n=jr(t,e);return{...Dr(t),namedRegex:\"^\"+n.namedParameterizedRoute+\"(?:/)?$\",routeKeys:n.routeKeys}}function lo(t,e){let{parameterizedRoute:n}=Sr(t),{catchAll:i=!0}=e;if(n===\"/\")return{namedRegex:\"^/\"+(i?\".*\":\"\")+\"$\"};let{namedParameterizedRoute:o}=jr(t,!1),s=i?\"(?:(/.*)?)\":\"\";return{namedRegex:\"^\"+o+s+\"$\"}}});var Ir=h(Et=>{\"use strict\";Object.defineProperty(Et,\"__esModule\",{value:!0});Object.defineProperty(Et,\"interpolateAs\",{enumerable:!0,get:function(){return co}});var ao=xr(),uo=Ar();function co(t,e,n){let i=\"\",o=(0,uo.getRouteRegex)(t),s=o.groups,f=(e!==t?(0,ao.getRouteMatcher)(o)(e):\"\")||n;i=t;let d=Object.keys(s);return d.every(u=>{let p=f[u]||\"\",{repeat:x,optional:S}=s[u],T=\"[\"+(x?\"...\":\"\")+u+\"]\";return S&&(T=(p?\"\":\"/\")+\"[\"+T+\"]\"),x&&!Array.isArray(p)&&(p=[p]),(S||u in f)&&(i=i.replace(T,x?p.map(U=>encodeURIComponent(U)).join(\"/\"):encodeURIComponent(p))||\"/\")})||(i=\"\"),{params:d,result:i}}});var zr=h((G,Wr)=>{\"use strict\";Object.defineProperty(G,\"__esModule\",{value:!0});Object.defineProperty(G,\"resolveHref\",{enumerable:!0,get:function(){return yo}});var fo=Be(),Ur=Ze(),po=ur(),mo=he(),ho=be(),bo=ft(),_o=vr(),go=Ir();function yo(t,e,n){let i,o=typeof e==\"string\"?e:(0,Ur.formatWithValidation)(e),s=o.match(/^[a-zA-Z]{1,}:\\/\\//),f=s?o.slice(s[0].length):o;if((f.split(\"?\")[0]||\"\").match(/(\\/\\/|\\\\)/)){console.error(\"Invalid href '\"+o+\"' passed to next/router in page: '\"+t.pathname+\"'. Repeated forward-slashes (//) or backslashes \\\\ are not valid in the href.\");let u=(0,mo.normalizeRepeatedSlashes)(f);o=(s?s[0]:\"\")+u}if(!(0,bo.isLocalURL)(o))return n?[o]:o;try{i=new URL(o.startsWith(\"#\")?t.asPath:t.pathname,\"http://n\")}catch{i=new URL(\"/\",\"http://n\")}try{let u=new URL(o,i);u.pathname=(0,ho.normalizePathTrailingSlash)(u.pathname);let p=\"\";if((0,_o.isDynamicRoute)(u.pathname)&&u.searchParams&&n){let S=(0,fo.searchParamsToUrlQuery)(u.searchParams),{result:T,params:U}=(0,go.interpolateAs)(u.pathname,u.pathname,S);T&&(p=(0,Ur.formatWithValidation)({pathname:T,hash:u.hash,query:(0,po.omit)(S,U)}))}let x=u.origin===i.origin?u.href.slice(u.origin.length):u.href;return n?[x,p||x]:x}catch{return n?[o]:o}}(typeof G.default==\"function\"||typeof G.default==\"object\"&&G.default!==null)&&typeof G.default.__esModule>\"u\"&&(Object.defineProperty(G.default,\"__esModule\",{value:!0}),Object.assign(G.default,G),Wr.exports=G.default)});var Ot=h(wt=>{\"use strict\";Object.defineProperty(wt,\"__esModule\",{value:!0});Object.defineProperty(wt,\"addPathPrefix\",{enumerable:!0,get:function(){return vo}});var No=Se();function vo(t,e){if(!t.startsWith(\"/\")||!e)return t;let{pathname:n,query:i,hash:o}=(0,No.parsePath)(t);return\"\"+e+n+i+o}});var Lr=h(Ct=>{\"use strict\";Object.defineProperty(Ct,\"__esModule\",{value:!0});Object.defineProperty(Ct,\"addLocale\",{enumerable:!0,get:function(){return Po}});var xo=Ot(),Mr=ct();function Po(t,e,n,i){if(!e||e===n)return t;let o=t.toLowerCase();return!i&&((0,Mr.pathHasPrefix)(o,\"/api\")||(0,Mr.pathHasPrefix)(o,\"/\"+e.toLowerCase()))?t:(0,xo.addPathPrefix)(t,\"/\"+e)}});var Fr=h((V,qr)=>{\"use strict\";Object.defineProperty(V,\"__esModule\",{value:!0});Object.defineProperty(V,\"addLocale\",{enumerable:!0,get:function(){return Ro}});var To=be(),Ro=function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return process.env.__NEXT_I18N_SUPPORT?(0,To.normalizePathTrailingSlash)(Lr().addLocale(t,...n)):t};(typeof V.default==\"function\"||typeof V.default==\"object\"&&V.default!==null)&&typeof V.default.__esModule>\"u\"&&(Object.defineProperty(V.default,\"__esModule\",{value:!0}),Object.assign(V.default,V),qr.exports=V.default)});var Vr=h(St=>{\"use strict\";Object.defineProperty(St,\"__esModule\",{value:!0});Object.defineProperty(St,\"RouterContext\",{enumerable:!0,get:function(){return Gr}});var ko=Oe(),Eo=ko._(de()),Gr=Eo.default.createContext(null);Gr.displayName=\"RouterContext\"});var Br=h(jt=>{\"use client\";\"use strict\";Object.defineProperty(jt,\"__esModule\",{value:!0});function wo(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}wo(jt,{CacheStates:function(){return Dt},AppRouterContext:function(){return $r},LayoutRouterContext:function(){return Hr},GlobalLayoutRouterContext:function(){return Kr},TemplateContext:function(){return Yr}});var Oo=Oe(),De=Oo._(de()),Dt;(function(t){t.LAZY_INITIALIZED=\"LAZYINITIALIZED\",t.DATA_FETCH=\"DATAFETCH\",t.READY=\"READY\"})(Dt||(Dt={}));var $r=De.default.createContext(null),Hr=De.default.createContext(null),Kr=De.default.createContext(null),Yr=De.default.createContext(null);$r.displayName=\"AppRouterContext\",Hr.displayName=\"LayoutRouterContext\",Kr.displayName=\"GlobalLayoutRouterContext\",Yr.displayName=\"TemplateContext\"});var Qr=h(($,Xr)=>{\"use strict\";Object.defineProperty($,\"__esModule\",{value:!0});function Co(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Co($,{requestIdleCallback:function(){return So},cancelIdleCallback:function(){return Do}});var So=typeof self<\"u\"&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(t){let e=Date.now();return self.setTimeout(function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})},1)},Do=typeof self<\"u\"&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(t){return clearTimeout(t)};(typeof $.default==\"function\"||typeof $.default==\"object\"&&$.default!==null)&&typeof $.default.__esModule>\"u\"&&(Object.defineProperty($.default,\"__esModule\",{value:!0}),Object.assign($.default,$),Xr.exports=$.default)});var tn=h((H,en)=>{\"use strict\";Object.defineProperty(H,\"__esModule\",{value:!0});Object.defineProperty(H,\"useIntersection\",{enumerable:!0,get:function(){return Io}});var _e=de(),Zr=Qr(),Jr=typeof IntersectionObserver==\"function\",At=new Map,je=[];function jo(t){let e={root:t.root||null,margin:t.rootMargin||\"\"},n=je.find(f=>f.root===e.root&&f.margin===e.margin),i;if(n&&(i=At.get(n),i))return i;let o=new Map,s=new IntersectionObserver(f=>{f.forEach(d=>{let u=o.get(d.target),p=d.isIntersecting||d.intersectionRatio>0;u&&p&&u(p)})},t);return i={id:e,observer:s,elements:o},je.push(e),At.set(e,i),i}function Ao(t,e,n){let{id:i,observer:o,elements:s}=jo(n);return s.set(t,e),o.observe(t),function(){if(s.delete(t),o.unobserve(t),s.size===0){o.disconnect(),At.delete(i);let d=je.findIndex(u=>u.root===i.root&&u.margin===i.margin);d>-1&&je.splice(d,1)}}}function Io(t){let{rootRef:e,rootMargin:n,disabled:i}=t,o=i||!Jr,[s,f]=(0,_e.useState)(!1),d=(0,_e.useRef)(null),u=(0,_e.useCallback)(x=>{d.current=x},[]);(0,_e.useEffect)(()=>{if(Jr){if(o||s)return;let x=d.current;if(x&&x.tagName)return Ao(x,T=>T&&f(T),{root:e?.current,rootMargin:n})}else if(!s){let x=(0,Zr.requestIdleCallback)(()=>f(!0));return()=>(0,Zr.cancelIdleCallback)(x)}},[o,n,e,s,d.current]);let p=(0,_e.useCallback)(()=>{f(!1)},[]);return[u,s,p]}(typeof H.default==\"function\"||typeof H.default==\"object\"&&H.default!==null)&&typeof H.default.__esModule>\"u\"&&(Object.defineProperty(H.default,\"__esModule\",{value:!0}),Object.assign(H.default,H),en.exports=H.default)});var rn=h(It=>{\"use strict\";Object.defineProperty(It,\"__esModule\",{value:!0});Object.defineProperty(It,\"normalizeLocalePath\",{enumerable:!0,get:function(){return Uo}});function Uo(t,e){let n,i=t.split(\"/\");return(e||[]).some(o=>i[1]&&i[1].toLowerCase()===o.toLowerCase()?(n=o,i.splice(1,1),t=i.join(\"/\")||\"/\",!0):!1),{pathname:t,detectedLocale:n}}});var on=h((K,nn)=>{\"use strict\";Object.defineProperty(K,\"__esModule\",{value:!0});Object.defineProperty(K,\"normalizeLocalePath\",{enumerable:!0,get:function(){return Wo}});var Wo=(t,e)=>process.env.__NEXT_I18N_SUPPORT?rn().normalizeLocalePath(t,e):{pathname:t,detectedLocale:void 0};(typeof K.default==\"function\"||typeof K.default==\"object\"&&K.default!==null)&&typeof K.default.__esModule>\"u\"&&(Object.defineProperty(K.default,\"__esModule\",{value:!0}),Object.assign(K.default,K),nn.exports=K.default)});var sn=h(Ut=>{\"use strict\";Object.defineProperty(Ut,\"__esModule\",{value:!0});Object.defineProperty(Ut,\"detectDomainLocale\",{enumerable:!0,get:function(){return zo}});function zo(t,e,n){if(t){n&&(n=n.toLowerCase());for(let s of t){var i,o;let f=(i=s.domain)==null?void 0:i.split(\":\")[0].toLowerCase();if(e===f||n===s.defaultLocale.toLowerCase()||(o=s.locales)!=null&&o.some(d=>d.toLowerCase()===n))return s}}}});var an=h((Y,ln)=>{\"use strict\";Object.defineProperty(Y,\"__esModule\",{value:!0});Object.defineProperty(Y,\"detectDomainLocale\",{enumerable:!0,get:function(){return Mo}});var Mo=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(process.env.__NEXT_I18N_SUPPORT)return sn().detectDomainLocale(...e)};(typeof Y.default==\"function\"||typeof Y.default==\"object\"&&Y.default!==null)&&typeof Y.default.__esModule>\"u\"&&(Object.defineProperty(Y.default,\"__esModule\",{value:!0}),Object.assign(Y.default,Y),ln.exports=Y.default)});var cn=h((B,un)=>{\"use strict\";Object.defineProperty(B,\"__esModule\",{value:!0});Object.defineProperty(B,\"getDomainLocale\",{enumerable:!0,get:function(){return Fo}});var Lo=be(),qo=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Fo(t,e,n,i){if(process.env.__NEXT_I18N_SUPPORT){let o=on().normalizeLocalePath,s=an().detectDomainLocale,f=e||o(t,n).detectedLocale,d=s(i,void 0,f);if(d){let u=\"http\"+(d.http?\"\":\"s\")+\"://\",p=f===d.defaultLocale?\"\":\"/\"+f;return\"\"+u+d.domain+(0,Lo.normalizePathTrailingSlash)(\"\"+qo+p+t)}return!1}else return!1}(typeof B.default==\"function\"||typeof B.default==\"object\"&&B.default!==null)&&typeof B.default.__esModule>\"u\"&&(Object.defineProperty(B.default,\"__esModule\",{value:!0}),Object.assign(B.default,B),un.exports=B.default)});var fn=h((X,dn)=>{\"use strict\";Object.defineProperty(X,\"__esModule\",{value:!0});Object.defineProperty(X,\"addBasePath\",{enumerable:!0,get:function(){return Ho}});var Go=Ot(),Vo=be(),$o=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Ho(t,e){return(0,Vo.normalizePathTrailingSlash)(process.env.__NEXT_MANUAL_CLIENT_BASE_PATH&&!e?t:(0,Go.addPathPrefix)(t,$o))}(typeof X.default==\"function\"||typeof X.default==\"object\"&&X.default!==null)&&typeof X.default.__esModule>\"u\"&&(Object.defineProperty(X.default,\"__esModule\",{value:!0}),Object.assign(X.default,X),dn.exports=X.default)});var mn=h((Q,pn)=>{\"use strict\";Object.defineProperty(Q,\"__esModule\",{value:!0});function Ko(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Ko(Q,{PrefetchKind:function(){return Wt},ACTION_REFRESH:function(){return Yo},ACTION_NAVIGATE:function(){return Bo},ACTION_RESTORE:function(){return Xo},ACTION_SERVER_PATCH:function(){return Qo},ACTION_PREFETCH:function(){return Zo},ACTION_FAST_REFRESH:function(){return Jo},ACTION_SERVER_ACTION:function(){return es}});var Yo=\"refresh\",Bo=\"navigate\",Xo=\"restore\",Qo=\"server-patch\",Zo=\"prefetch\",Jo=\"fast-refresh\",es=\"server-action\",Wt;(function(t){t.AUTO=\"auto\",t.FULL=\"full\",t.TEMPORARY=\"temporary\"})(Wt||(Wt={}));(typeof Q.default==\"function\"||typeof Q.default==\"object\"&&Q.default!==null)&&typeof Q.default.__esModule>\"u\"&&(Object.defineProperty(Q.default,\"__esModule\",{value:!0}),Object.assign(Q.default,Q),pn.exports=Q.default)});var vn=h((Z,Nn)=>{\"use client\";\"use strict\";Object.defineProperty(Z,\"__esModule\",{value:!0});Object.defineProperty(Z,\"default\",{enumerable:!0,get:function(){return ps}});var ts=Oe(),A=ts._(de()),hn=zr(),yn=ft(),rs=Ze(),ns=he(),is=Fr(),os=Vr(),ss=Br(),ls=tn(),as=cn(),us=fn(),bn=mn(),_n=new Set;function zt(t,e,n,i,o,s){if(typeof window>\"u\"||!s&&!(0,yn.isLocalURL)(e))return;if(!i.bypassPrefetchedCheck){let d=typeof i.locale<\"u\"?i.locale:\"locale\"in t?t.locale:void 0,u=e+\"%\"+n+\"%\"+d;if(_n.has(u))return;_n.add(u)}let f=s?t.prefetch(e,o):t.prefetch(e,n,i);Promise.resolve(f).catch(d=>{throw d})}function cs(t){let n=t.currentTarget.getAttribute(\"target\");return n&&n!==\"_self\"||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&t.nativeEvent.which===2}function ds(t,e,n,i,o,s,f,d,u,p){let{nodeName:x}=t.currentTarget;if(x.toUpperCase()===\"A\"&&(cs(t)||!u&&!(0,yn.isLocalURL)(n)))return;t.preventDefault();let T=()=>{let U=f??!0;\"beforePopState\"in e?e[o?\"replace\":\"push\"](n,i,{shallow:s,locale:d,scroll:U}):e[o?\"replace\":\"push\"](i||n,{forceOptimisticNavigation:!p,scroll:U})};u?A.default.startTransition(T):T()}function gn(t){return typeof t==\"string\"?t:(0,rs.formatUrl)(t)}var fs=A.default.forwardRef(function(e,n){let i,{href:o,as:s,children:f,prefetch:d=null,passHref:u,replace:p,shallow:x,scroll:S,locale:T,onClick:U,onMouseEnter:le,onTouchStart:ge,legacyBehavior:I=!1,...te}=e;i=f,I&&(typeof i==\"string\"||typeof i==\"number\")&&(i=A.default.createElement(\"a\",null,i));let _=A.default.useContext(os.RouterContext),Ie=A.default.useContext(ss.AppRouterContext),z=_??Ie,M=!_,ie=d!==!1,ae=d===null?bn.PrefetchKind.AUTO:bn.PrefetchKind.FULL;{let b=function(g){return new Error(\"Failed prop type: The prop `\"+g.key+\"` expects a \"+g.expected+\" in `<Link>`, but got `\"+g.actual+\"` instead.\"+(typeof window<\"u\"?`\nOpen your browser's console to view the Component stack trace.`:\"\"))};Object.keys({href:!0}).forEach(g=>{if(g===\"href\"){if(e[g]==null||typeof e[g]!=\"string\"&&typeof e[g]!=\"object\")throw b({key:g,expected:\"`string` or `object`\",actual:e[g]===null?\"null\":typeof e[g]})}else{let D=g}}),Object.keys({as:!0,replace:!0,scroll:!0,shallow:!0,passHref:!0,prefetch:!0,locale:!0,onClick:!0,onMouseEnter:!0,onTouchStart:!0,legacyBehavior:!0}).forEach(g=>{let D=typeof e[g];if(g===\"as\"){if(e[g]&&D!==\"string\"&&D!==\"object\")throw b({key:g,expected:\"`string` or `object`\",actual:D})}else if(g===\"locale\"){if(e[g]&&D!==\"string\")throw b({key:g,expected:\"`string`\",actual:D})}else if(g===\"onClick\"||g===\"onMouseEnter\"||g===\"onTouchStart\"){if(e[g]&&D!==\"function\")throw b({key:g,expected:\"`function`\",actual:D})}else if(g===\"replace\"||g===\"scroll\"||g===\"shallow\"||g===\"passHref\"||g===\"prefetch\"||g===\"legacyBehavior\"){if(e[g]!=null&&D!==\"boolean\")throw b({key:g,expected:\"`boolean`\",actual:D})}else{let Pe=g}});let xe=A.default.useRef(!1);e.prefetch&&!xe.current&&!M&&(xe.current=!0,console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\"))}if(M&&!s){let b;if(typeof o==\"string\"?b=o:typeof o==\"object\"&&typeof o.pathname==\"string\"&&(b=o.pathname),b&&b.split(\"/\").some(ee=>ee.startsWith(\"[\")&&ee.endsWith(\"]\")))throw new Error(\"Dynamic href `\"+b+\"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\")}let{href:L,as:O}=A.default.useMemo(()=>{if(!_){let ee=gn(o);return{href:ee,as:s?gn(s):ee}}let[b,ne]=(0,hn.resolveHref)(_,o,!0);return{href:b,as:s?(0,hn.resolveHref)(_,s):ne||b}},[_,o,s]),ye=A.default.useRef(L),Ne=A.default.useRef(O),E;if(I){U&&console.warn('\"onClick\" was passed to <Link> with `href` of `'+o+'` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link'),le&&console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `'+o+'` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');try{E=A.default.Children.only(i)}catch{throw i?new Error(\"Multiple children were passed to <Link> with `href` of `\"+o+\"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\"+(typeof window<\"u\"?` \nOpen your browser's console to view the Component stack trace.`:\"\")):new Error(\"No children were passed to <Link> with `href` of `\"+o+\"` but one child is required https://nextjs.org/docs/messages/link-no-children\")}}else if(i?.type===\"a\")throw new Error(`Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor`);let w=I?E&&typeof E==\"object\"&&E.ref:n,[J,re,fe]=(0,ls.useIntersection)({rootMargin:\"200px\"}),ve=A.default.useCallback(b=>{(Ne.current!==O||ye.current!==L)&&(fe(),Ne.current=O,ye.current=L),J(b),w&&(typeof w==\"function\"?w(b):typeof w==\"object\"&&(w.current=b))},[O,w,L,fe,J]);A.default.useEffect(()=>{},[O,L,re,T,ie,_?.locale,z,M,ae]);let oe={ref:ve,onClick(b){if(!b)throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');!I&&typeof U==\"function\"&&U(b),I&&E.props&&typeof E.props.onClick==\"function\"&&E.props.onClick(b),z&&(b.defaultPrevented||ds(b,z,L,O,p,x,S,T,M,ie))},onMouseEnter(b){!I&&typeof le==\"function\"&&le(b),I&&E.props&&typeof E.props.onMouseEnter==\"function\"&&E.props.onMouseEnter(b),z&&((!ie||!0)&&M||zt(z,L,O,{locale:T,priority:!0,bypassPrefetchedCheck:!0},{kind:ae},M))},onTouchStart(b){!I&&typeof ge==\"function\"&&ge(b),I&&E.props&&typeof E.props.onTouchStart==\"function\"&&E.props.onTouchStart(b),z&&(!ie&&M||zt(z,L,O,{locale:T,priority:!0,bypassPrefetchedCheck:!0},{kind:ae},M))}};if((0,ns.isAbsoluteUrl)(O))oe.href=O;else if(!I||u||E.type===\"a\"&&!(\"href\"in E.props)){let b=typeof T<\"u\"?T:_?.locale,ne=_?.isLocaleDomain&&(0,as.getDomainLocale)(O,b,_?.locales,_?.domainLocales);oe.href=ne||(0,us.addBasePath)((0,is.addLocale)(O,b,_?.defaultLocale))}return I?A.default.cloneElement(E,oe):A.default.createElement(\"a\",{...te,...oe},i)}),ps=fs;(typeof Z.default==\"function\"||typeof Z.default==\"object\"&&Z.default!==null)&&typeof Z.default.__esModule>\"u\"&&(Object.defineProperty(Z.default,\"__esModule\",{value:!0}),Object.assign(Z.default,Z),Nn.exports=Z.default)});var Pn=h((Qs,xn)=>{xn.exports=vn()});var gs={};ei(gs,{default:()=>_s,faqData:()=>hs,frontmatter:()=>ms});var a=Jt(nr());function He({children:t,quizSlug:e}){return React.createElement(\"article\",{className:\"max-w-4xl mx-auto text-gray-900\"},React.createElement(\"div\",{className:\"prose prose-lg max-w-none text-gray-900 [&>*]:text-gray-900\"},t))}var Tn=Jt(Pn());function Ae({task:t,href:e}){let n=e||`/quiz/${t}`;return React.createElement(\"div\",{className:\"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\"},React.createElement(\"div\",{className:\"text-center\"},React.createElement(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-2\"},\"Not sure which AI fits your workflow?\"),React.createElement(\"p\",{className:\"text-gray-600 mb-4\"},\"Take our 30-second quiz to get a personalized recommendation\"),React.createElement(Tn.default,{href:n,className:\"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\"},\"Take the \",t.charAt(0).toUpperCase()+t.slice(1),\" Quiz \\u2192\")))}var ms={title:\"Best AI for Writing (2025) \\u2014 Claude 3.5 vs GPT-4o, Gemini & more\",description:\"Comprehensive comparison of Claude 3.5, GPT-4o, Gemini, Perplexity & Grok for bloggers, students, and marketers. Find your perfect AI writing assistant.\",slug:\"best-ai-for-writing-2025-claude-3-5-vs-gpt-4o-gemini-more\",template:\"pillar\",cluster:\"text\",priority:\"Low\",lastUpdated:\"2025-07-22\"},hs=[{q:\"What is the best free AI for writing?\",a:\"Perplexity offers the strongest free tier for fact-based writing, while Claude's free tier is best for creative prose.\"},{q:\"Can Google penalise AI-generated content?\",a:\"Google ranks helpful content regardless of how it's produced; thin or spammy AI text can be penalised.\"},{q:\"What's a context window and why does it matter?\",a:\"It's the amount of text an AI can 'remember'. Bigger windows (e.g., Claude's 200 k tokens) keep long documents coherent.\"},{q:\"Which AI is best for creative writing?\",a:\"Claude 3.5 Sonnet consistently produces the most human-like, nuanced prose.\"},{q:\"Which AI provides reliable citations?\",a:\"Perplexity Pro surfaces sources and clickable references by default.\"},{q:\"Is GPT-4o still king in 2025?\",a:\"It's the best all-rounder, but Claude wins on style and Perplexity on accuracy.\"}];function Rn(t){let e=Object.assign({h2:\"h2\",p:\"p\",strong:\"strong\",ul:\"ul\",li:\"li\",hr:\"hr\",em:\"em\",blockquote:\"blockquote\",h3:\"h3\",a:\"a\"},t.components);return(0,a.jsxDEV)(He,{quizSlug:\"writing\",children:[(0,a.jsxDEV)(Ae,{task:\"writing\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:16,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\"Who are you writing for ?\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:18,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.strong,{children:\"The Blogger\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:20,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:20,columnNumber:1},this),(0,a.jsxDEV)(e.ul,{children:[`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Pain\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:22,columnNumber:3},this),\" \\u2013 needs original long-form content that won't feel robotic or earn an SEO penalty.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:22,columnNumber:1},this),`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Ideal output\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:23,columnNumber:3},this),\" \\u2013 an AI blog generator that keeps a consistent tone.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Killer feature\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:24,columnNumber:3},this),\" \\u2013 a huge context window to track details across thousands of words.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:24,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:22,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.strong,{children:\"The Student\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:26,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:26,columnNumber:1},this),(0,a.jsxDEV)(e.ul,{children:[`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Pain\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:28,columnNumber:3},this),\" \\u2013 must research, structure, and cite accurately while avoiding plagiarism.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:28,columnNumber:1},this),`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Ideal output\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:29,columnNumber:3},this),\" \\u2013 an AI essay writer that returns verifiable facts with citations.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Killer feature\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:30,columnNumber:3},this),\" \\u2013 can ingest PDFs and analyse them directly.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:30,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:28,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.strong,{children:\"The Marketer\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:32,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:32,columnNumber:1},this),(0,a.jsxDEV)(e.ul,{children:[`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Pain\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:34,columnNumber:3},this),\" \\u2013 high-volume, mixed-format content plus brand-voice consistency.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:34,columnNumber:1},this),`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Ideal output\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:35,columnNumber:3},this),\" \\u2013 a tool that plugs into Google Workspace and accelerates campaigns.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:35,columnNumber:1},this),`\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\"Killer feature\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:36,columnNumber:3},this),\" \\u2013 analyses spreadsheet data and builds project plans.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:36,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:34,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:38,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:'A market of specialists, not one \"best\" model'},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:40,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[\"Perplexity is an \",(0,a.jsxDEV)(e.strong,{children:\"answer engine\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:42,columnNumber:18},this),\", Claude a \",(0,a.jsxDEV)(e.strong,{children:\"creative prose specialist\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:42,columnNumber:46},this),\", and Gemini a \",(0,a.jsxDEV)(e.strong,{children:\"productivity layer\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:42,columnNumber:90},this),\" for Docs, Sheets, and Gmail. The takeaway: \",(0,a.jsxDEV)(e.em,{children:\"choose by task\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:42,columnNumber:156},this),\", not by raw IQ.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:42,columnNumber:1},this),(0,a.jsxDEV)(e.blockquote,{children:[`\n`,(0,a.jsxDEV)(e.h3,{children:\"\\u26A0 Premium trap\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:44,columnNumber:3},this),`\n`,(0,a.jsxDEV)(e.p,{children:`The $20 Pro tiers are no longer the cutting edge. OpenAI, Anthropic, Perplexity, and xAI now sell $100\\u2013$300 \"Max / Heavy\" plans aimed at enterprises. For most writers the $20 tier remains the ROI sweet-spot, but know you're not buying the absolute top model.`},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:45,columnNumber:3},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:44,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:47,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\"2025 AI-writer scorecard\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:49,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[`| Model | Best for (archetype) | Stand-out feature | Context window | Free tier | Pro price | Key limitation |\n|-------|----------------------|-------------------|---------------|-----------|-----------|----------------|\n| `,(0,a.jsxDEV)(e.strong,{children:\"Claude 3.5 Sonnet\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:53,columnNumber:3},this),` | Creative writing (Poet) | \"Artifacts\" live editor | 200 k tokens | Yes (daily cap) | $20 | No native real-time web search |\n| `,(0,a.jsxDEV)(e.strong,{children:\"GPT-4o\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:54,columnNumber:3},this),` | Generalist (Polymath) | Multimodal + Custom GPTs | 128 k tokens | Yes (cap) | $20 | Output can feel robotic |\n| `,(0,a.jsxDEV)(e.strong,{children:\"Gemini Advanced\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:55,columnNumber:3},this),` | Productivity (Producer) | Deep Workspace integration | 1 M + tokens | Yes (std) | $19.99 | Creative flair weaker than Claude |\n| `,(0,a.jsxDEV)(e.strong,{children:\"Perplexity Pro\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:56,columnNumber:3},this),` | Research (Professor) | Clickable citations, Deep Research | \\u2014 | Yes (cap) | $20 | Not a creative writer |\n| `,(0,a.jsxDEV)(e.strong,{children:\"Grok\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:57,columnNumber:3},this),\" | Real-time insights (Provocateur) | Live X / Twitter data | \\u2014 | Yes (cap) | $30 | Pricey; edgy tone not for all |\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:51,columnNumber:1},this),(0,a.jsxDEV)(\"div\",{style:{textAlign:\"right\",fontSize:\"0.9rem\"},children:(0,a.jsxDEV)(\"a\",{href:\"/export/scorecard.csv\",children:\"Export to Sheets \\u2192\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:59,columnNumber:52},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:59,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:61,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\"Speed test \\u26A1\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:63,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.em,{children:\"[Speed comparison GIF placeholder - GPT-4o vs Claude 3.5 vs Gemini]\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:65,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:65,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\"GPT-4o starts generating sooner and streams tokens faster, a win for brainstorms and quick Q & A. Claude is ~2\\xD7 faster than its predecessor Opus but still trails GPT-4o on sheer responsiveness.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:67,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:69,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\"Deep-dive profiles\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:71,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\"Claude 3.5 Sonnet \\u2014 \",(0,a.jsxDEV)(e.em,{children:\"the creative wordsmith\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:73,columnNumber:25},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:73,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\"Strengths.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:75,columnNumber:1},this),` Thoughtful, expressive prose; 200 k-token context; \"Artifacts\" side-panel for iterative editing.\n`,(0,a.jsxDEV)(e.strong,{children:\"Weaknesses.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:76,columnNumber:1},this),` No built-in web browsing; free tier message cap.\n`,(0,a.jsxDEV)(e.em,{children:[\"Read the full \",(0,a.jsxDEV)(e.a,{href:\"/claude-3-5-for-blogging-review\",children:\"Claude 3.5 blogging review\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:77,columnNumber:16},this),\".\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:77,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:75,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:79,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\"GPT-4o \\u2014 \",(0,a.jsxDEV)(e.em,{children:\"the versatile all-rounder\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:81,columnNumber:14},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:81,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:`Handles writing, code, data and images in one chat. Custom GPTs unlock niche workflows.\nCriticisms: increasing verbosity, smaller window than Claude, privacy concerns.`},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:83,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:86,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\"Gemini Advanced \\u2014 \",(0,a.jsxDEV)(e.em,{children:\"the integrated productivity engine\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:88,columnNumber:23},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:88,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[`Native in Docs, Sheets, Gmail. Perfect for campaign tables, email summarising, Drive file look-ups.\nDeep dive: `,(0,a.jsxDEV)(e.a,{href:\"/gemini-advanced-for-marketers-guide\",children:\"Gemini for marketers\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:91,columnNumber:12},this),\".\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:90,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:93,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\"Perplexity Pro \\u2014 \",(0,a.jsxDEV)(e.em,{children:\"the research powerhouse\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:95,columnNumber:22},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:95,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[`Delivers answers with numbered citations; \"Deep Research\" builds exhaustive reports.\nGuide: `,(0,a.jsxDEV)(e.a,{href:\"/how-to-use-perplexity-for-academic-research\",children:\"How to use Perplexity for academic research\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:98,columnNumber:8},this),\".\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:97,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:100,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\"Grok \\u2014 \",(0,a.jsxDEV)(e.em,{children:\"the real-time provocateur\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:102,columnNumber:12},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:102,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\"Live social-media pulse plus a snarky attitude. Great for trend analysts, overkill for everyday writing.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:104,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:106,columnNumber:1},this),(0,a.jsxDEV)(Ae,{task:\"writing\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:108,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:110,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\",lineNumber:14,columnNumber:1},this)}function bs(t={}){let{wrapper:e}=t.components||{};return e?(0,a.jsxDEV)(e,Object.assign({},t,{children:(0,a.jsxDEV)(Rn,t,void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\"},this):Rn(t)}var _s=bs;return ti(gs);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "pillars/best-ai-for-writing.mdx", "_raw": {"sourceFilePath": "pillars/best-ai-for-writing.mdx", "sourceFileName": "best-ai-for-writing.mdx", "sourceFileDir": "pillars", "contentType": "mdx", "flattenedPath": "pillars/best-ai-for-writing"}, "type": "<PERSON><PERSON>", "url": "/best-ai-for-writing-2025-claude-3-5-vs-gpt-4o-gemini-more", "slugFromPath": "pillars/best-ai-for-writing"}, "documentHash": "1753144961487", "hasWarnings": false, "documentTypeName": "<PERSON><PERSON>"}, "pillars/best-ai-image-generators.mdx": {"document": {"title": "Best AI Image & Texture Generators (2025) — Midjourney vs Stable Diffusion, Ideogram & DALL-E", "description": "Professional comparison of Midjourney, Stable Diffusion, Ideogram, and DALL-E for game developers, digital artists, and marketers. Find your perfect AI image generator.", "slug": "best-ai-image-generators-2025-midjourney-vs-stable-diffusion-ideogram-dalle", "cluster": "image", "template": "pillar", "priority": "Medium", "lastUpdated": "2025-07-22", "body": {"raw": "\nimport PillarLayout from \"@/templates/PillarLayout\"\nimport QuizCta from \"@/components/QuizCta\"\n\n<PillarLayout quizSlug=\"image\">\n\n<QuizCta task=\"image\" />\n\n## The New Creative Frontier: Beyond Novelty to Professional Integration\n\nThe era of AI image generation has moved beyond novelty and into a phase of serious professional integration. For game developers, digital artists, and marketers, these tools are no longer experimental toys but powerful assets capable of accelerating workflows, inspiring new concepts, and producing commercial-grade visuals.\n\nThe market has matured into a competitive landscape where the leading platforms—**Midjourney, Stable Diffusion, Ideogram, and DALL-E**—each champion a distinct approach to creation. This analysis reveals that there is no single \"best\" generator. The optimal choice balances **Aesthetic Quality, Technical Control, Typographic Fidelity, and Commercial Viability**.\n\n---\n\n## The Gauntlet: A Unified Prompt Showdown\n\nTo test the limits of each model, we used a single, complex prompt designed to evaluate photorealism, object detail, lighting, depth of field, and in-image text generation.\n\n**The Prompt:** *\"Photorealistic product shot for a marketing campaign. A sleek, matte black bottle of high-end perfume named '<PERSON>therea' sits on a wet, reflective marble surface. In the background, out of focus, are glowing neon orchids. The words 'Etherea: The Scent of Tomorrow' are elegantly displayed in a modern sans-serif font at the bottom.\"*\n\n### The Results: Side-by-Side Visual Analysis\n\n**Midjourney v6:** Delivers unparalleled photorealism. The lighting on the wet marble, the texture of the matte bottle, and the soft bokeh of the neon orchids are exceptionally cinematic and \"art-directed\". However, it struggles with the text, rendering a stylized but illegible version of the brand name and tagline.\n\n**Stable Diffusion XL Lightning:** Produces a high-quality image with remarkable speed, a testament to its efficient architecture. The composition is strong, but the fine details and lighting nuances lack the hyper-realistic polish of Midjourney. It represents a powerful balance of speed and quality, ideal for rapid iteration.\n\n**Ideogram:** Excels where others fail. The text \"Etherea: The Scent of Tomorrow\" is rendered with near-perfect clarity and elegant composition, demonstrating its core strength. The surrounding image is competent but less photorealistic than Midjourney or DALL-E, appearing more like a high-quality digital illustration than a photograph.\n\n**DALL-E 4 (via GPT-4o):** Shows superior prompt comprehension. It successfully interprets and renders every element of the prompt correctly—the matte bottle, wet surface, neon bokeh, and the text. The integration with ChatGPT allows for this nuanced understanding. While the text is legible, Ideogram's is more typographically refined.\n\n---\n\n## Prompt Showdown Scorecard\n\n| Platform | Photorealism (1-5) | Prompt Adherence (1-5) | Typography (1-5) | Overall Aesthetic (1-5) | Verdict |\n|----------|---------------------|-------------------------|-------------------|-------------------------|---------|\n| **Midjourney v6** | 5 | 4 | 2 | 5 | The Artist: Unmatched beauty, but can't write. |\n| **SDXL Lightning** | 4 | 4 | 2 | 4 | The Engineer: Fast and capable, a workhorse. |\n| **Ideogram** | 3 | 4 | 5 | 4 | The Typographer: Flawless text, good-enough image. |\n| **DALL-E 4** | 4 | 5 | 4 | 4 | The Co-Creator: Understands everything, a true generalist. |\n\n<div style={{textAlign:'right',fontSize:'0.9rem'}}><a href=\"/export/image-scorecard.csv\">Export to Sheets →</a></div>\n\n---\n\n## The Business Imperative: Cost vs. Commercial Rights\n\nChoosing a platform involves more than just creative output; it requires a careful analysis of cost and legal considerations.\n\n### Economic Analysis: The Cost of 1,000 Images\n\nThe cost per image varies dramatically depending on the platform's pricing model—subscription, credits, or pay-per-image API calls.\n\n| Platform | Recommended Plan/Method | Total Cost for 1,000 Images | Effective Cost-per-Image | Key Considerations |\n|----------|-------------------------|------------------------------|--------------------------|-------------------|\n| **Midjourney** | Standard Plan ($30/mo) | ~$30 | ~$0.03 | Subscription includes ~900 fast generations. |\n| **SDXL Lightning** | Replicate API | ~$1.40 | ~$0.0014 | Requires technical setup; API pricing varies by provider. |\n| **Ideogram** | Plus Plan ($16/mo, billed yearly) | ~$16 | ~$0.016 | Includes 1,000 priority credits/month. |\n| **DALL-E 4 (API)** | OpenAI API (DALL-E 3) | ~$40 | $0.04 | Pay-as-you-go; price is for standard quality. |\n\n### Legal Framework: Commercial Licensing\n\nThe ability to use AI-generated images commercially is complex. Under U.S. law, AI-generated images generally cannot be copyrighted, making the platform's Terms of Service paramount.\n\n| Platform | Ownership of Output | Commercial Use Allowed? | Key Restrictions & Revenue Caps | Private Generation? |\n|----------|---------------------|-------------------------|--------------------------------|-------------------|\n| **Midjourney** | You own assets you create. | Yes, with paid plans. | Businesses with >$1M gross annual revenue must use Pro/Mega plan. | Yes (Pro/Mega plans) |\n| **Stable Diffusion** | You own the output. | Yes, with license compliance. | Use is restricted from specific harmful applications. Enterprise license needed for companies >$1M revenue. | N/A (local/private) |\n| **Ideogram** | You are free to use images. | Yes, on all plans. | Free plan generations are public by default, posing a risk for proprietary work. | Yes (Plus/Pro plans) |\n| **DALL-E** | You own assets you create. | Yes, per OpenAI's terms. | Subject to OpenAI's Content Policy; no explicit revenue caps mentioned for basic use. | Yes (via API/ChatGPT) |\n\n---\n\n## Strategic Recommendations by Professional Persona\n\n### 🎮 **For the Game Developer**\nA hybrid approach is best. Use **Midjourney** for high-fidelity concept art and marketing visuals. For asset creation, **Stable Diffusion** is superior for generating tileable textures, large asset batches, and maintaining character consistency with tools like ControlNet.\n\n### 🎨 **For the Digital Artist / Illustrator**\n**Midjourney** is the primary tool for its unparalleled aesthetic control and painterly results. Use **DALL-E 4** for its conversational interface to rapidly brainstorm and overcome creative blocks.\n\n### 📈 **For the Marketer / Designer**\nStart with **Ideogram** for any asset requiring text, such as social media graphics or logos. Use **DALL-E 4** for quickly generating blog headers and A/B testing ad concepts. For high-stakes campaign hero images, Midjourney's Pro plan ensures both top-tier quality and privacy.\n\n---\n\n## Deep-dive profiles\n\n### Midjourney — _the aesthetic perfectionist_\n\n**Strengths.** Unmatched photorealism and artistic quality; intuitive Discord interface; strong community and prompt sharing.\n**Weaknesses.** Poor text rendering; requires Discord; limited fine-tuning control.\n*Perfect for: Concept art, marketing hero images, artistic illustrations.*\n\n### Stable Diffusion — _the technical powerhouse_\n\n**Strengths.** Open-source flexibility; ControlNet for precise control; can run locally; extensive model ecosystem.\n**Weaknesses.** Steep learning curve; requires technical setup; inconsistent quality without expertise.\n*Perfect for: Bulk generation, custom training, technical workflows.*\n\n### Ideogram — _the typography specialist_\n\n**Strengths.** Exceptional text rendering; clean, modern aesthetic; affordable pricing; good prompt adherence.\n**Weaknesses.** Less photorealistic than competitors; smaller community; newer platform.\n*Perfect for: Logos, social media graphics, text-heavy designs.*\n\n### DALL-E 4 — _the conversational creator_\n\n**Strengths.** Superior prompt understanding; ChatGPT integration; consistent quality; ethical safeguards.\n**Weaknesses.** More expensive than alternatives; less artistic flair than Midjourney; API-dependent.\n*Perfect for: Rapid ideation, conversational workflows, general-purpose generation.*\n\n---\n\n<QuizCta task=\"image\" />\n\n---\n\nexport const faqData = [\n  { q: \"What is the best AI for generating photorealistic images?\", a: \"Midjourney is widely regarded as the leader for photorealistic images due to its advanced aesthetic model and parameters like --style raw. However, DALL-E 4 and Stable Diffusion XL can also produce high-quality realistic images, especially with careful prompting.\" },\n  { q: \"Which AI image generator is best for creating logos with text?\", a: \"Ideogram is the specialist for generating images with accurate and well-composed text. Its models are specifically trained for typography, making it the top choice for logos, posters, and marketing materials with text. DALL-E 4 is also improving in this area.\" },\n  { q: \"Can I use images from Midjourney or DALL-E for commercial purposes?\", a: \"Yes, both platforms allow commercial use, but with important conditions. You own the assets you create. However, Midjourney requires a Pro or Mega plan for companies with over $1M in annual revenue. Furthermore, AI-generated images currently exist in a legal gray area and may not be eligible for copyright protection.\" },\n  { q: \"What is the cheapest way to generate a lot of AI images?\", a: \"Using an API for a speed-optimized model like Stable Diffusion XL Lightning is often the most cost-effective method for high-volume generation, with costs potentially under $0.002 per image. However, this requires technical setup. For subscription models, Ideogram's paid plans offer a large number of credits for a relatively low monthly cost.\" },\n  { q: \"What's the difference between Midjourney and Stable Diffusion?\", a: \"The main difference is control versus ease of use. Midjourney offers a curated, high-quality aesthetic experience through its Discord interface. Stable Diffusion is an open-source model that provides maximum control and customization (e.g., training your own models, using ControlNet) but has a much steeper learning curve and often requires a powerful local computer or API integration.\" },\n  { q: \"Which AI image generator is best for game development?\", a: \"A hybrid approach works best: Midjourney for concept art and hero images, Stable Diffusion for bulk asset generation and textures. Stable Diffusion's ControlNet feature is particularly valuable for maintaining consistency across character designs and creating tileable textures.\" }\n]\n\n</PillarLayout>\n", "code": "var Component=(()=>{var Gn=Object.create;var we=Object.defineProperty;var Xn=Object.getOwnPropertyDescriptor;var Qn=Object.getOwnPropertyNames;var Zn=Object.getPrototypeOf,Jn=Object.prototype.hasOwnProperty;var h=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),ei=(t,e)=>{for(var n in e)we(t,n,{get:e[n],enumerable:!0})},Zt=(t,e,n,i)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let o of Qn(e))!Jn.call(t,o)&&o!==n&&we(t,o,{get:()=>e[o],enumerable:!(i=Xn(e,o))||i.enumerable});return t};var Jt=(t,e,n)=>(n=t!=null?Gn(Zn(t)):{},Zt(e||!t||!t.__esModule?we(n,\"default\",{value:t,enumerable:!0}):n,t)),ti=t=>Zt(we({},\"__esModule\",{value:!0}),t);var de=h((Na,er)=>{er.exports=React});var tr=h(Ye=>{\"use strict\";(function(){\"use strict\";var t=de(),e=Symbol.for(\"react.element\"),n=Symbol.for(\"react.portal\"),i=Symbol.for(\"react.fragment\"),o=Symbol.for(\"react.strict_mode\"),a=Symbol.for(\"react.profiler\"),f=Symbol.for(\"react.provider\"),d=Symbol.for(\"react.context\"),c=Symbol.for(\"react.forward_ref\"),p=Symbol.for(\"react.suspense\"),x=Symbol.for(\"react.suspense_list\"),S=Symbol.for(\"react.memo\"),P=Symbol.for(\"react.lazy\"),U=Symbol.for(\"react.offscreen\"),se=Symbol.iterator,ge=\"@@iterator\";function I(r){if(r===null||typeof r!=\"object\")return null;var l=se&&r[se]||r[ge];return typeof l==\"function\"?l:null}var te=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function _(r){{for(var l=arguments.length,u=new Array(l>1?l-1:0),m=1;m<l;m++)u[m-1]=arguments[m];Ie(\"error\",r,u)}}function Ie(r,l,u){{var m=te.ReactDebugCurrentFrame,v=m.getStackAddendum();v!==\"\"&&(l+=\"%s\",u=u.concat([v]));var T=u.map(function(N){return String(N)});T.unshift(\"Warning: \"+l),Function.prototype.apply.call(console[r],console,T)}}var W=!1,M=!1,ie=!1,le=!1,z=!1,O;O=Symbol.for(\"react.module.reference\");function ye(r){return!!(typeof r==\"string\"||typeof r==\"function\"||r===i||r===a||z||r===o||r===p||r===x||le||r===U||W||M||ie||typeof r==\"object\"&&r!==null&&(r.$$typeof===P||r.$$typeof===S||r.$$typeof===f||r.$$typeof===d||r.$$typeof===c||r.$$typeof===O||r.getModuleId!==void 0))}function Ne(r,l,u){var m=r.displayName;if(m)return m;var v=l.displayName||l.name||\"\";return v!==\"\"?u+\"(\"+v+\")\":u}function R(r){return r.displayName||\"Context\"}function w(r){if(r==null)return null;if(typeof r.tag==\"number\"&&_(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof r==\"function\")return r.displayName||r.name||null;if(typeof r==\"string\")return r;switch(r){case i:return\"Fragment\";case n:return\"Portal\";case a:return\"Profiler\";case o:return\"StrictMode\";case p:return\"Suspense\";case x:return\"SuspenseList\"}if(typeof r==\"object\")switch(r.$$typeof){case d:var l=r;return R(l)+\".Consumer\";case f:var u=r;return R(u._context)+\".Provider\";case c:return Ne(r,r.render,\"ForwardRef\");case S:var m=r.displayName||null;return m!==null?m:w(r.type)||\"Memo\";case P:{var v=r,T=v._payload,N=v._init;try{return w(N(T))}catch{return null}}}return null}var J=Object.assign,re=0,fe,ve,oe,b,ne,ee,Ue;function Le(){}Le.__reactDisabledLog=!0;function xe(){{if(re===0){fe=console.log,ve=console.info,oe=console.warn,b=console.error,ne=console.group,ee=console.groupCollapsed,Ue=console.groupEnd;var r={configurable:!0,enumerable:!0,value:Le,writable:!0};Object.defineProperties(console,{info:r,log:r,warn:r,error:r,group:r,groupCollapsed:r,groupEnd:r})}re++}}function g(){{if(re--,re===0){var r={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:J({},r,{value:fe}),info:J({},r,{value:ve}),warn:J({},r,{value:oe}),error:J({},r,{value:b}),group:J({},r,{value:ne}),groupCollapsed:J({},r,{value:ee}),groupEnd:J({},r,{value:Ue})})}re<0&&_(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var C=te.ReactCurrentDispatcher,Te;function Pe(r,l,u){{if(Te===void 0)try{throw Error()}catch(v){var m=v.stack.trim().match(/\\n( *(at )?)/);Te=m&&m[1]||\"\"}return`\n`+Te+r}}var We=!1,Ee;{var kn=typeof WeakMap==\"function\"?WeakMap:Map;Ee=new kn}function Mt(r,l){if(!r||We)return\"\";{var u=Ee.get(r);if(u!==void 0)return u}var m;We=!0;var v=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var T;T=C.current,C.current=null,xe();try{if(l){var N=function(){throw Error()};if(Object.defineProperty(N.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(N,[])}catch(j){m=j}Reflect.construct(r,[],N)}else{try{N.call()}catch(j){m=j}r.call(N.prototype)}}else{try{throw Error()}catch(j){m=j}r()}}catch(j){if(j&&m&&typeof j.stack==\"string\"){for(var y=j.stack.split(`\n`),D=m.stack.split(`\n`),E=y.length-1,k=D.length-1;E>=1&&k>=0&&y[E]!==D[k];)k--;for(;E>=1&&k>=0;E--,k--)if(y[E]!==D[k]){if(E!==1||k!==1)do if(E--,k--,k<0||y[E]!==D[k]){var L=`\n`+y[E].replace(\" at new \",\" at \");return r.displayName&&L.includes(\"<anonymous>\")&&(L=L.replace(\"<anonymous>\",r.displayName)),typeof r==\"function\"&&Ee.set(r,L),L}while(E>=1&&k>=0);break}}}finally{We=!1,C.current=T,g(),Error.prepareStackTrace=v}var ue=r?r.displayName||r.name:\"\",ae=ue?Pe(ue):\"\";return typeof r==\"function\"&&Ee.set(r,ae),ae}function Rn(r,l,u){return Mt(r,!1)}function wn(r){var l=r.prototype;return!!(l&&l.isReactComponent)}function ke(r,l,u){if(r==null)return\"\";if(typeof r==\"function\")return Mt(r,wn(r));if(typeof r==\"string\")return Pe(r);switch(r){case p:return Pe(\"Suspense\");case x:return Pe(\"SuspenseList\")}if(typeof r==\"object\")switch(r.$$typeof){case c:return Rn(r.render);case S:return ke(r.type,l,u);case P:{var m=r,v=m._payload,T=m._init;try{return ke(T(v),l,u)}catch{}}}return\"\"}var pe=Object.prototype.hasOwnProperty,zt={},qt=te.ReactDebugCurrentFrame;function Re(r){if(r){var l=r._owner,u=ke(r.type,r._source,l?l.type:null);qt.setExtraStackFrame(u)}else qt.setExtraStackFrame(null)}function On(r,l,u,m,v){{var T=Function.call.bind(pe);for(var N in r)if(T(r,N)){var y=void 0;try{if(typeof r[N]!=\"function\"){var D=Error((m||\"React class\")+\": \"+u+\" type `\"+N+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof r[N]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw D.name=\"Invariant Violation\",D}y=r[N](l,N,m,u,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(E){y=E}y&&!(y instanceof Error)&&(Re(v),_(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",m||\"React class\",u,N,typeof y),Re(null)),y instanceof Error&&!(y.message in zt)&&(zt[y.message]=!0,Re(v),_(\"Failed %s type: %s\",u,y.message),Re(null))}}}var Dn=Array.isArray;function Me(r){return Dn(r)}function Sn(r){{var l=typeof Symbol==\"function\"&&Symbol.toStringTag,u=l&&r[Symbol.toStringTag]||r.constructor.name||\"Object\";return u}}function Cn(r){try{return Ft(r),!1}catch{return!0}}function Ft(r){return\"\"+r}function Vt(r){if(Cn(r))return _(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",Sn(r)),Ft(r)}var me=te.ReactCurrentOwner,jn={key:!0,ref:!0,__self:!0,__source:!0},$t,Yt,ze;ze={};function An(r){if(pe.call(r,\"ref\")){var l=Object.getOwnPropertyDescriptor(r,\"ref\").get;if(l&&l.isReactWarning)return!1}return r.ref!==void 0}function In(r){if(pe.call(r,\"key\")){var l=Object.getOwnPropertyDescriptor(r,\"key\").get;if(l&&l.isReactWarning)return!1}return r.key!==void 0}function Un(r,l){if(typeof r.ref==\"string\"&&me.current&&l&&me.current.stateNode!==l){var u=w(me.current.type);ze[u]||(_('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',w(me.current.type),r.ref),ze[u]=!0)}}function Ln(r,l){{var u=function(){$t||($t=!0,_(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",l))};u.isReactWarning=!0,Object.defineProperty(r,\"key\",{get:u,configurable:!0})}}function Wn(r,l){{var u=function(){Yt||(Yt=!0,_(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",l))};u.isReactWarning=!0,Object.defineProperty(r,\"ref\",{get:u,configurable:!0})}}var Mn=function(r,l,u,m,v,T,N){var y={$$typeof:e,type:r,key:l,ref:u,props:N,_owner:T};return y._store={},Object.defineProperty(y._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(y,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:m}),Object.defineProperty(y,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:v}),Object.freeze&&(Object.freeze(y.props),Object.freeze(y)),y};function zn(r,l,u,m,v){{var T,N={},y=null,D=null;u!==void 0&&(Vt(u),y=\"\"+u),In(l)&&(Vt(l.key),y=\"\"+l.key),An(l)&&(D=l.ref,Un(l,v));for(T in l)pe.call(l,T)&&!jn.hasOwnProperty(T)&&(N[T]=l[T]);if(r&&r.defaultProps){var E=r.defaultProps;for(T in E)N[T]===void 0&&(N[T]=E[T])}if(y||D){var k=typeof r==\"function\"?r.displayName||r.name||\"Unknown\":r;y&&Ln(N,k),D&&Wn(N,k)}return Mn(r,y,D,v,m,me.current,N)}}var qe=te.ReactCurrentOwner,Ht=te.ReactDebugCurrentFrame;function ce(r){if(r){var l=r._owner,u=ke(r.type,r._source,l?l.type:null);Ht.setExtraStackFrame(u)}else Ht.setExtraStackFrame(null)}var Fe;Fe=!1;function Ve(r){return typeof r==\"object\"&&r!==null&&r.$$typeof===e}function Bt(){{if(qe.current){var r=w(qe.current.type);if(r)return`\n\nCheck the render method of \\``+r+\"`.\"}return\"\"}}function qn(r){{if(r!==void 0){var l=r.fileName.replace(/^.*[\\\\\\/]/,\"\"),u=r.lineNumber;return`\n\nCheck your code at `+l+\":\"+u+\".\"}return\"\"}}var Kt={};function Fn(r){{var l=Bt();if(!l){var u=typeof r==\"string\"?r:r.displayName||r.name;u&&(l=`\n\nCheck the top-level render call using <`+u+\">.\")}return l}}function Gt(r,l){{if(!r._store||r._store.validated||r.key!=null)return;r._store.validated=!0;var u=Fn(l);if(Kt[u])return;Kt[u]=!0;var m=\"\";r&&r._owner&&r._owner!==qe.current&&(m=\" It was passed a child from \"+w(r._owner.type)+\".\"),ce(r),_('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',u,m),ce(null)}}function Xt(r,l){{if(typeof r!=\"object\")return;if(Me(r))for(var u=0;u<r.length;u++){var m=r[u];Ve(m)&&Gt(m,l)}else if(Ve(r))r._store&&(r._store.validated=!0);else if(r){var v=I(r);if(typeof v==\"function\"&&v!==r.entries)for(var T=v.call(r),N;!(N=T.next()).done;)Ve(N.value)&&Gt(N.value,l)}}}function Vn(r){{var l=r.type;if(l==null||typeof l==\"string\")return;var u;if(typeof l==\"function\")u=l.propTypes;else if(typeof l==\"object\"&&(l.$$typeof===c||l.$$typeof===S))u=l.propTypes;else return;if(u){var m=w(l);On(u,r.props,\"prop\",m,r)}else if(l.PropTypes!==void 0&&!Fe){Fe=!0;var v=w(l);_(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",v||\"Unknown\")}typeof l.getDefaultProps==\"function\"&&!l.getDefaultProps.isReactClassApproved&&_(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function $n(r){{for(var l=Object.keys(r.props),u=0;u<l.length;u++){var m=l[u];if(m!==\"children\"&&m!==\"key\"){ce(r),_(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",m),ce(null);break}}r.ref!==null&&(ce(r),_(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),ce(null))}}var Qt={};function Yn(r,l,u,m,v,T){{var N=ye(r);if(!N){var y=\"\";(r===void 0||typeof r==\"object\"&&r!==null&&Object.keys(r).length===0)&&(y+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var D=qn(v);D?y+=D:y+=Bt();var E;r===null?E=\"null\":Me(r)?E=\"array\":r!==void 0&&r.$$typeof===e?(E=\"<\"+(w(r.type)||\"Unknown\")+\" />\",y=\" Did you accidentally export a JSX literal instead of a component?\"):E=typeof r,_(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",E,y)}var k=zn(r,l,u,v,T);if(k==null)return k;if(N){var L=l.children;if(L!==void 0)if(m)if(Me(L)){for(var ue=0;ue<L.length;ue++)Xt(L[ue],r);Object.freeze&&Object.freeze(L)}else _(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else Xt(L,r)}if(pe.call(l,\"key\")){var ae=w(r),j=Object.keys(l).filter(function(Kn){return Kn!==\"key\"}),$e=j.length>0?\"{key: someKey, \"+j.join(\": ..., \")+\": ...}\":\"{key: someKey}\";if(!Qt[ae+$e]){var Bn=j.length>0?\"{\"+j.join(\": ..., \")+\": ...}\":\"{}\";_(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,$e,ae,Bn,ae),Qt[ae+$e]=!0}}return r===i?$n(k):Vn(k),k}}var Hn=Yn;Ye.Fragment=i,Ye.jsxDEV=Hn})()});var nr=h((xa,rr)=>{\"use strict\";rr.exports=tr()});var Oe=h(Be=>{\"use strict\";Be._=Be._interop_require_default=ri;function ri(t){return t&&t.__esModule?t:{default:t}}});var Ge=h(Ke=>{\"use strict\";Object.defineProperty(Ke,\"__esModule\",{value:!0});function ni(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}ni(Ke,{searchParamsToUrlQuery:function(){return ii},urlQueryToSearchParams:function(){return oi},assign:function(){return ai}});function ii(t){let e={};return t.forEach((n,i)=>{typeof e[i]>\"u\"?e[i]=n:Array.isArray(e[i])?e[i].push(n):e[i]=[e[i],n]}),e}function ir(t){return typeof t==\"string\"||typeof t==\"number\"&&!isNaN(t)||typeof t==\"boolean\"?String(t):\"\"}function oi(t){let e=new URLSearchParams;return Object.entries(t).forEach(n=>{let[i,o]=n;Array.isArray(o)?o.forEach(a=>e.append(i,ir(a))):e.set(i,ir(o))}),e}function ai(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return n.forEach(o=>{Array.from(o.keys()).forEach(a=>t.delete(a)),o.forEach((a,f)=>t.append(f,a))}),t}});var ar=h(Xe=>{\"use strict\";function or(t){if(typeof WeakMap!=\"function\")return null;var e=new WeakMap,n=new WeakMap;return(or=function(i){return i?n:e})(t)}Xe._=Xe._interop_require_wildcard=si;function si(t,e){if(!e&&t&&t.__esModule)return t;if(t===null||typeof t!=\"object\"&&typeof t!=\"function\")return{default:t};var n=or(e);if(n&&n.has(t))return n.get(t);var i={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in t)if(a!==\"default\"&&Object.prototype.hasOwnProperty.call(t,a)){var f=o?Object.getOwnPropertyDescriptor(t,a):null;f&&(f.get||f.set)?Object.defineProperty(i,a,f):i[a]=t[a]}return i.default=t,n&&n.set(t,i),i}});var Ze=h(Qe=>{\"use strict\";Object.defineProperty(Qe,\"__esModule\",{value:!0});function li(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}li(Qe,{formatUrl:function(){return sr},urlObjectKeys:function(){return lr},formatWithValidation:function(){return fi}});var ci=ar(),ui=ci._(Ge()),di=/https?|ftp|gopher|file/;function sr(t){let{auth:e,hostname:n}=t,i=t.protocol||\"\",o=t.pathname||\"\",a=t.hash||\"\",f=t.query||\"\",d=!1;e=e?encodeURIComponent(e).replace(/%3A/i,\":\")+\"@\":\"\",t.host?d=e+t.host:n&&(d=e+(~n.indexOf(\":\")?\"[\"+n+\"]\":n),t.port&&(d+=\":\"+t.port)),f&&typeof f==\"object\"&&(f=String(ui.urlQueryToSearchParams(f)));let c=t.search||f&&\"?\"+f||\"\";return i&&!i.endsWith(\":\")&&(i+=\":\"),t.slashes||(!i||di.test(i))&&d!==!1?(d=\"//\"+(d||\"\"),o&&o[0]!==\"/\"&&(o=\"/\"+o)):d||(d=\"\"),a&&a[0]!==\"#\"&&(a=\"#\"+a),c&&c[0]!==\"?\"&&(c=\"?\"+c),o=o.replace(/[?#]/g,encodeURIComponent),c=c.replace(\"#\",\"%23\"),\"\"+i+d+o+c+a}var lr=[\"auth\",\"hash\",\"host\",\"hostname\",\"href\",\"path\",\"pathname\",\"port\",\"protocol\",\"query\",\"search\",\"slashes\"];function fi(t){return t!==null&&typeof t==\"object\"&&Object.keys(t).forEach(e=>{lr.includes(e)||console.warn(\"Unknown key passed via urlObject into url.format: \"+e)}),sr(t)}});var cr=h(Je=>{\"use strict\";Object.defineProperty(Je,\"__esModule\",{value:!0});Object.defineProperty(Je,\"omit\",{enumerable:!0,get:function(){return pi}});function pi(t,e){let n={};return Object.keys(t).forEach(i=>{e.includes(i)||(n[i]=t[i])}),n}});var he=h(ot=>{\"use strict\";Object.defineProperty(ot,\"__esModule\",{value:!0});function mi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}mi(ot,{WEB_VITALS:function(){return hi},execOnce:function(){return bi},isAbsoluteUrl:function(){return gi},getLocationOrigin:function(){return ur},getURL:function(){return yi},getDisplayName:function(){return De},isResSent:function(){return dr},normalizeRepeatedSlashes:function(){return Ni},loadGetInitialProps:function(){return fr},SP:function(){return pr},ST:function(){return vi},DecodeError:function(){return et},NormalizeError:function(){return tt},PageNotFoundError:function(){return rt},MissingStaticPage:function(){return nt},MiddlewareNotFoundError:function(){return it},stringifyError:function(){return xi}});var hi=[\"CLS\",\"FCP\",\"FID\",\"INP\",\"LCP\",\"TTFB\"];function bi(t){let e=!1,n;return function(){for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return e||(e=!0,n=t(...o)),n}}var _i=/^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/,gi=t=>_i.test(t);function ur(){let{protocol:t,hostname:e,port:n}=window.location;return t+\"//\"+e+(n?\":\"+n:\"\")}function yi(){let{href:t}=window.location,e=ur();return t.substring(e.length)}function De(t){return typeof t==\"string\"?t:t.displayName||t.name||\"Unknown\"}function dr(t){return t.finished||t.headersSent}function Ni(t){let e=t.split(\"?\");return e[0].replace(/\\\\/g,\"/\").replace(/\\/\\/+/g,\"/\")+(e[1]?\"?\"+e.slice(1).join(\"?\"):\"\")}async function fr(t,e){var n;if((n=t.prototype)!=null&&n.getInitialProps){let a='\"'+De(t)+'.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';throw new Error(a)}let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await fr(e.Component,e.ctx)}:{};let o=await t.getInitialProps(e);if(i&&dr(i))return o;if(!o){let a='\"'+De(t)+'.getInitialProps()\" should resolve to an object. But found \"'+o+'\" instead.';throw new Error(a)}return Object.keys(o).length===0&&!e.ctx&&console.warn(\"\"+De(t)+\" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\"),o}var pr=typeof performance<\"u\",vi=pr&&[\"mark\",\"measure\",\"getEntriesByName\"].every(t=>typeof performance[t]==\"function\"),et=class extends Error{},tt=class extends Error{},rt=class extends Error{constructor(e){super(),this.code=\"ENOENT\",this.name=\"PageNotFoundError\",this.message=\"Cannot find module for page: \"+e}},nt=class extends Error{constructor(e,n){super(),this.message=\"Failed to load static file for page: \"+e+\" \"+n}},it=class extends Error{constructor(){super(),this.code=\"ENOENT\",this.message=\"Cannot find the middleware module\"}};function xi(t){return JSON.stringify({message:t.message,stack:t.stack})}});var st=h(at=>{\"use strict\";Object.defineProperty(at,\"__esModule\",{value:!0});Object.defineProperty(at,\"removeTrailingSlash\",{enumerable:!0,get:function(){return Ti}});function Ti(t){return t.replace(/\\/$/,\"\")||\"/\"}});var Se=h(lt=>{\"use strict\";Object.defineProperty(lt,\"__esModule\",{value:!0});Object.defineProperty(lt,\"parsePath\",{enumerable:!0,get:function(){return Pi}});function Pi(t){let e=t.indexOf(\"#\"),n=t.indexOf(\"?\"),i=n>-1&&(e<0||n<e);return i||e>-1?{pathname:t.substring(0,i?n:e),query:i?t.substring(n,e>-1?e:void 0):\"\",hash:e>-1?t.slice(e):\"\"}:{pathname:t,query:\"\",hash:\"\"}}});var be=h((q,hr)=>{\"use strict\";Object.defineProperty(q,\"__esModule\",{value:!0});Object.defineProperty(q,\"normalizePathTrailingSlash\",{enumerable:!0,get:function(){return ki}});var mr=st(),Ei=Se(),ki=t=>{if(!t.startsWith(\"/\")||process.env.__NEXT_MANUAL_TRAILING_SLASH)return t;let{pathname:e,query:n,hash:i}=(0,Ei.parsePath)(t);return process.env.__NEXT_TRAILING_SLASH?/\\.[^/]+\\/?$/.test(e)?\"\"+(0,mr.removeTrailingSlash)(e)+n+i:e.endsWith(\"/\")?\"\"+e+n+i:e+\"/\"+n+i:\"\"+(0,mr.removeTrailingSlash)(e)+n+i};(typeof q.default==\"function\"||typeof q.default==\"object\"&&q.default!==null)&&typeof q.default.__esModule>\"u\"&&(Object.defineProperty(q.default,\"__esModule\",{value:!0}),Object.assign(q.default,q),hr.exports=q.default)});var ut=h(ct=>{\"use strict\";Object.defineProperty(ct,\"__esModule\",{value:!0});Object.defineProperty(ct,\"pathHasPrefix\",{enumerable:!0,get:function(){return wi}});var Ri=Se();function wi(t,e){if(typeof t!=\"string\")return!1;let{pathname:n}=(0,Ri.parsePath)(t);return n===e||n.startsWith(e+\"/\")}});var _r=h((F,br)=>{\"use strict\";Object.defineProperty(F,\"__esModule\",{value:!0});Object.defineProperty(F,\"hasBasePath\",{enumerable:!0,get:function(){return Si}});var Oi=ut(),Di=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Si(t){return(0,Oi.pathHasPrefix)(t,Di)}(typeof F.default==\"function\"||typeof F.default==\"object\"&&F.default!==null)&&typeof F.default.__esModule>\"u\"&&(Object.defineProperty(F.default,\"__esModule\",{value:!0}),Object.assign(F.default,F),br.exports=F.default)});var ft=h(dt=>{\"use strict\";Object.defineProperty(dt,\"__esModule\",{value:!0});Object.defineProperty(dt,\"isLocalURL\",{enumerable:!0,get:function(){return ji}});var gr=he(),Ci=_r();function ji(t){if(!(0,gr.isAbsoluteUrl)(t))return!0;try{let e=(0,gr.getLocationOrigin)(),n=new URL(t,e);return n.origin===e&&(0,Ci.hasBasePath)(n.pathname)}catch{return!1}}});var yr=h(mt=>{\"use strict\";Object.defineProperty(mt,\"__esModule\",{value:!0});Object.defineProperty(mt,\"getSortedRoutes\",{enumerable:!0,get:function(){return Ai}});var pt=class t{insert(e){this._insert(e.split(\"/\").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){e===void 0&&(e=\"/\");let n=[...this.children.keys()].sort();this.slugName!==null&&n.splice(n.indexOf(\"[]\"),1),this.restSlugName!==null&&n.splice(n.indexOf(\"[...]\"),1),this.optionalRestSlugName!==null&&n.splice(n.indexOf(\"[[...]]\"),1);let i=n.map(o=>this.children.get(o)._smoosh(\"\"+e+o+\"/\")).reduce((o,a)=>[...o,...a],[]);if(this.slugName!==null&&i.push(...this.children.get(\"[]\")._smoosh(e+\"[\"+this.slugName+\"]/\")),!this.placeholder){let o=e===\"/\"?\"/\":e.slice(0,-1);if(this.optionalRestSlugName!=null)throw new Error('You cannot define a route with the same specificity as a optional catch-all route (\"'+o+'\" and \"'+o+\"[[...\"+this.optionalRestSlugName+']]\").');i.unshift(o)}return this.restSlugName!==null&&i.push(...this.children.get(\"[...]\")._smoosh(e+\"[...\"+this.restSlugName+\"]/\")),this.optionalRestSlugName!==null&&i.push(...this.children.get(\"[[...]]\")._smoosh(e+\"[[...\"+this.optionalRestSlugName+\"]]/\")),i}_insert(e,n,i){if(e.length===0){this.placeholder=!1;return}if(i)throw new Error(\"Catch-all must be the last part of the URL.\");let o=e[0];if(o.startsWith(\"[\")&&o.endsWith(\"]\")){let d=function(c,p){if(c!==null&&c!==p)throw new Error(\"You cannot use different slug names for the same dynamic path ('\"+c+\"' !== '\"+p+\"').\");n.forEach(x=>{if(x===p)throw new Error('You cannot have the same slug name \"'+p+'\" repeat within a single dynamic path');if(x.replace(/\\W/g,\"\")===o.replace(/\\W/g,\"\"))throw new Error('You cannot have the slug names \"'+x+'\" and \"'+p+'\" differ only by non-word symbols within a single dynamic path')}),n.push(p)},a=o.slice(1,-1),f=!1;if(a.startsWith(\"[\")&&a.endsWith(\"]\")&&(a=a.slice(1,-1),f=!0),a.startsWith(\"...\")&&(a=a.substring(3),i=!0),a.startsWith(\"[\")||a.endsWith(\"]\"))throw new Error(\"Segment names may not start or end with extra brackets ('\"+a+\"').\");if(a.startsWith(\".\"))throw new Error(\"Segment names may not start with erroneous periods ('\"+a+\"').\");if(i)if(f){if(this.restSlugName!=null)throw new Error('You cannot use both an required and optional catch-all route at the same level (\"[...'+this.restSlugName+']\" and \"'+e[0]+'\" ).');d(this.optionalRestSlugName,a),this.optionalRestSlugName=a,o=\"[[...]]\"}else{if(this.optionalRestSlugName!=null)throw new Error('You cannot use both an optional and required catch-all route at the same level (\"[[...'+this.optionalRestSlugName+']]\" and \"'+e[0]+'\").');d(this.restSlugName,a),this.restSlugName=a,o=\"[...]\"}else{if(f)throw new Error('Optional route parameters are not yet supported (\"'+e[0]+'\").');d(this.slugName,a),this.slugName=a,o=\"[]\"}}this.children.has(o)||this.children.set(o,new t),this.children.get(o)._insert(e.slice(1),n,i)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}};function Ai(t){let e=new pt;return t.forEach(n=>e.insert(n)),e.smoosh()}});var Nr=h(ht=>{\"use strict\";Object.defineProperty(ht,\"__esModule\",{value:!0});Object.defineProperty(ht,\"isDynamicRoute\",{enumerable:!0,get:function(){return Ui}});var Ii=/\\/\\[[^/]+?\\](?=\\/|$)/;function Ui(t){return Ii.test(t)}});var vr=h(bt=>{\"use strict\";Object.defineProperty(bt,\"__esModule\",{value:!0});function Li(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Li(bt,{getSortedRoutes:function(){return Wi.getSortedRoutes},isDynamicRoute:function(){return Mi.isDynamicRoute}});var Wi=yr(),Mi=Nr()});var xr=h(_t=>{\"use strict\";Object.defineProperty(_t,\"__esModule\",{value:!0});Object.defineProperty(_t,\"getRouteMatcher\",{enumerable:!0,get:function(){return qi}});var zi=he();function qi(t){let{re:e,groups:n}=t;return i=>{let o=e.exec(i);if(!o)return!1;let a=d=>{try{return decodeURIComponent(d)}catch{throw new zi.DecodeError(\"failed to decode param\")}},f={};return Object.keys(n).forEach(d=>{let c=n[d],p=o[c.pos];p!==void 0&&(f[d]=~p.indexOf(\"/\")?p.split(\"/\").map(x=>a(x)):c.repeat?[a(p)]:a(p))}),f}}});var Tr=h(gt=>{\"use strict\";Object.defineProperty(gt,\"__esModule\",{value:!0});Object.defineProperty(gt,\"ensureLeadingSlash\",{enumerable:!0,get:function(){return Fi}});function Fi(t){return t.startsWith(\"/\")?t:\"/\"+t}});var Pr=h(yt=>{\"use strict\";Object.defineProperty(yt,\"__esModule\",{value:!0});Object.defineProperty(yt,\"isGroupSegment\",{enumerable:!0,get:function(){return Vi}});function Vi(t){return t[0]===\"(\"&&t.endsWith(\")\")}});var Er=h(Nt=>{\"use strict\";Object.defineProperty(Nt,\"__esModule\",{value:!0});function $i(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}$i(Nt,{normalizeAppPath:function(){return Bi},normalizeRscPath:function(){return Ki}});var Yi=Tr(),Hi=Pr();function Bi(t){return(0,Yi.ensureLeadingSlash)(t.split(\"/\").reduce((e,n,i,o)=>!n||(0,Hi.isGroupSegment)(n)||n[0]===\"@\"||(n===\"page\"||n===\"route\")&&i===o.length-1?e:e+\"/\"+n,\"\"))}function Ki(t,e){return e?t.replace(/\\.rsc($|\\?)/,\"$1\"):t}});var kr=h(xt=>{\"use strict\";Object.defineProperty(xt,\"__esModule\",{value:!0});function Gi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Gi(xt,{INTERCEPTION_ROUTE_MARKERS:function(){return vt},isInterceptionRouteAppPath:function(){return Qi},extractInterceptionRouteInformation:function(){return Zi}});var Xi=Er(),vt=[\"(..)(..)\",\"(.)\",\"(..)\",\"(...)\"];function Qi(t){return t.split(\"/\").find(e=>vt.find(n=>e.startsWith(n)))!==void 0}function Zi(t){let e,n,i;for(let o of t.split(\"/\"))if(n=vt.find(a=>o.startsWith(a)),n){[e,i]=t.split(n,2);break}if(!e||!n||!i)throw new Error(`Invalid interception route: ${t}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(e=(0,Xi.normalizeAppPath)(e),n){case\"(.)\":e===\"/\"?i=`/${i}`:i=e+\"/\"+i;break;case\"(..)\":if(e===\"/\")throw new Error(`Invalid interception route: ${t}. Cannot use (..) marker at the root level, use (.) instead.`);i=e.split(\"/\").slice(0,-1).concat(i).join(\"/\");break;case\"(...)\":i=\"/\"+i;break;case\"(..)(..)\":let o=e.split(\"/\");if(o.length<=2)throw new Error(`Invalid interception route: ${t}. Cannot use (..)(..) marker at the root level or one level up.`);i=o.slice(0,-2).concat(i).join(\"/\");break;default:throw new Error(\"Invariant: unexpected marker\")}return{interceptingRoute:e,interceptedRoute:i}}});var Rr=h(Tt=>{\"use strict\";Object.defineProperty(Tt,\"__esModule\",{value:!0});Object.defineProperty(Tt,\"escapeStringRegexp\",{enumerable:!0,get:function(){return to}});var Ji=/[|\\\\{}()[\\]^$+*?.-]/,eo=/[|\\\\{}()[\\]^$+*?.-]/g;function to(t){return Ji.test(t)?t.replace(eo,\"\\\\$&\"):t}});var Ar=h(kt=>{\"use strict\";Object.defineProperty(kt,\"__esModule\",{value:!0});function ro(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}ro(kt,{getRouteRegex:function(){return Cr},getNamedRouteRegex:function(){return ao},getNamedMiddlewareRegex:function(){return so}});var Or=kr(),Pt=Rr(),Dr=st(),no=\"nxtP\",io=\"nxtI\";function Et(t){let e=t.startsWith(\"[\")&&t.endsWith(\"]\");e&&(t=t.slice(1,-1));let n=t.startsWith(\"...\");return n&&(t=t.slice(3)),{key:t,repeat:n,optional:e}}function Sr(t){let e=(0,Dr.removeTrailingSlash)(t).slice(1).split(\"/\"),n={},i=1;return{parameterizedRoute:e.map(o=>{let a=Or.INTERCEPTION_ROUTE_MARKERS.find(d=>o.startsWith(d)),f=o.match(/\\[((?:\\[.*\\])|.+)\\]/);if(a&&f){let{key:d,optional:c,repeat:p}=Et(f[1]);return n[d]={pos:i++,repeat:p,optional:c},\"/\"+(0,Pt.escapeStringRegexp)(a)+\"([^/]+?)\"}else if(f){let{key:d,repeat:c,optional:p}=Et(f[1]);return n[d]={pos:i++,repeat:c,optional:p},c?p?\"(?:/(.+?))?\":\"/(.+?)\":\"/([^/]+?)\"}else return\"/\"+(0,Pt.escapeStringRegexp)(o)}).join(\"\"),groups:n}}function Cr(t){let{parameterizedRoute:e,groups:n}=Sr(t);return{re:new RegExp(\"^\"+e+\"(?:/)?$\"),groups:n}}function oo(){let t=0;return()=>{let e=\"\",n=++t;for(;n>0;)e+=String.fromCharCode(97+(n-1)%26),n=Math.floor((n-1)/26);return e}}function wr(t){let{getSafeRouteKey:e,segment:n,routeKeys:i,keyPrefix:o}=t,{key:a,optional:f,repeat:d}=Et(n),c=a.replace(/\\W/g,\"\");o&&(c=\"\"+o+c);let p=!1;return(c.length===0||c.length>30)&&(p=!0),isNaN(parseInt(c.slice(0,1)))||(p=!0),p&&(c=e()),o?i[c]=\"\"+o+a:i[c]=\"\"+a,d?f?\"(?:/(?<\"+c+\">.+?))?\":\"/(?<\"+c+\">.+?)\":\"/(?<\"+c+\">[^/]+?)\"}function jr(t,e){let n=(0,Dr.removeTrailingSlash)(t).slice(1).split(\"/\"),i=oo(),o={};return{namedParameterizedRoute:n.map(a=>{let f=Or.INTERCEPTION_ROUTE_MARKERS.some(c=>a.startsWith(c)),d=a.match(/\\[((?:\\[.*\\])|.+)\\]/);return f&&d?wr({getSafeRouteKey:i,segment:d[1],routeKeys:o,keyPrefix:e?io:void 0}):d?wr({getSafeRouteKey:i,segment:d[1],routeKeys:o,keyPrefix:e?no:void 0}):\"/\"+(0,Pt.escapeStringRegexp)(a)}).join(\"\"),routeKeys:o}}function ao(t,e){let n=jr(t,e);return{...Cr(t),namedRegex:\"^\"+n.namedParameterizedRoute+\"(?:/)?$\",routeKeys:n.routeKeys}}function so(t,e){let{parameterizedRoute:n}=Sr(t),{catchAll:i=!0}=e;if(n===\"/\")return{namedRegex:\"^/\"+(i?\".*\":\"\")+\"$\"};let{namedParameterizedRoute:o}=jr(t,!1),a=i?\"(?:(/.*)?)\":\"\";return{namedRegex:\"^\"+o+a+\"$\"}}});var Ir=h(Rt=>{\"use strict\";Object.defineProperty(Rt,\"__esModule\",{value:!0});Object.defineProperty(Rt,\"interpolateAs\",{enumerable:!0,get:function(){return uo}});var lo=xr(),co=Ar();function uo(t,e,n){let i=\"\",o=(0,co.getRouteRegex)(t),a=o.groups,f=(e!==t?(0,lo.getRouteMatcher)(o)(e):\"\")||n;i=t;let d=Object.keys(a);return d.every(c=>{let p=f[c]||\"\",{repeat:x,optional:S}=a[c],P=\"[\"+(x?\"...\":\"\")+c+\"]\";return S&&(P=(p?\"\":\"/\")+\"[\"+P+\"]\"),x&&!Array.isArray(p)&&(p=[p]),(S||c in f)&&(i=i.replace(P,x?p.map(U=>encodeURIComponent(U)).join(\"/\"):encodeURIComponent(p))||\"/\")})||(i=\"\"),{params:d,result:i}}});var Wr=h((V,Lr)=>{\"use strict\";Object.defineProperty(V,\"__esModule\",{value:!0});Object.defineProperty(V,\"resolveHref\",{enumerable:!0,get:function(){return yo}});var fo=Ge(),Ur=Ze(),po=cr(),mo=he(),ho=be(),bo=ft(),_o=vr(),go=Ir();function yo(t,e,n){let i,o=typeof e==\"string\"?e:(0,Ur.formatWithValidation)(e),a=o.match(/^[a-zA-Z]{1,}:\\/\\//),f=a?o.slice(a[0].length):o;if((f.split(\"?\")[0]||\"\").match(/(\\/\\/|\\\\)/)){console.error(\"Invalid href '\"+o+\"' passed to next/router in page: '\"+t.pathname+\"'. Repeated forward-slashes (//) or backslashes \\\\ are not valid in the href.\");let c=(0,mo.normalizeRepeatedSlashes)(f);o=(a?a[0]:\"\")+c}if(!(0,bo.isLocalURL)(o))return n?[o]:o;try{i=new URL(o.startsWith(\"#\")?t.asPath:t.pathname,\"http://n\")}catch{i=new URL(\"/\",\"http://n\")}try{let c=new URL(o,i);c.pathname=(0,ho.normalizePathTrailingSlash)(c.pathname);let p=\"\";if((0,_o.isDynamicRoute)(c.pathname)&&c.searchParams&&n){let S=(0,fo.searchParamsToUrlQuery)(c.searchParams),{result:P,params:U}=(0,go.interpolateAs)(c.pathname,c.pathname,S);P&&(p=(0,Ur.formatWithValidation)({pathname:P,hash:c.hash,query:(0,po.omit)(S,U)}))}let x=c.origin===i.origin?c.href.slice(c.origin.length):c.href;return n?[x,p||x]:x}catch{return n?[o]:o}}(typeof V.default==\"function\"||typeof V.default==\"object\"&&V.default!==null)&&typeof V.default.__esModule>\"u\"&&(Object.defineProperty(V.default,\"__esModule\",{value:!0}),Object.assign(V.default,V),Lr.exports=V.default)});var Ot=h(wt=>{\"use strict\";Object.defineProperty(wt,\"__esModule\",{value:!0});Object.defineProperty(wt,\"addPathPrefix\",{enumerable:!0,get:function(){return vo}});var No=Se();function vo(t,e){if(!t.startsWith(\"/\")||!e)return t;let{pathname:n,query:i,hash:o}=(0,No.parsePath)(t);return\"\"+e+n+i+o}});var zr=h(Dt=>{\"use strict\";Object.defineProperty(Dt,\"__esModule\",{value:!0});Object.defineProperty(Dt,\"addLocale\",{enumerable:!0,get:function(){return To}});var xo=Ot(),Mr=ut();function To(t,e,n,i){if(!e||e===n)return t;let o=t.toLowerCase();return!i&&((0,Mr.pathHasPrefix)(o,\"/api\")||(0,Mr.pathHasPrefix)(o,\"/\"+e.toLowerCase()))?t:(0,xo.addPathPrefix)(t,\"/\"+e)}});var Fr=h(($,qr)=>{\"use strict\";Object.defineProperty($,\"__esModule\",{value:!0});Object.defineProperty($,\"addLocale\",{enumerable:!0,get:function(){return Eo}});var Po=be(),Eo=function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return process.env.__NEXT_I18N_SUPPORT?(0,Po.normalizePathTrailingSlash)(zr().addLocale(t,...n)):t};(typeof $.default==\"function\"||typeof $.default==\"object\"&&$.default!==null)&&typeof $.default.__esModule>\"u\"&&(Object.defineProperty($.default,\"__esModule\",{value:!0}),Object.assign($.default,$),qr.exports=$.default)});var $r=h(St=>{\"use strict\";Object.defineProperty(St,\"__esModule\",{value:!0});Object.defineProperty(St,\"RouterContext\",{enumerable:!0,get:function(){return Vr}});var ko=Oe(),Ro=ko._(de()),Vr=Ro.default.createContext(null);Vr.displayName=\"RouterContext\"});var Gr=h(jt=>{\"use client\";\"use strict\";Object.defineProperty(jt,\"__esModule\",{value:!0});function wo(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}wo(jt,{CacheStates:function(){return Ct},AppRouterContext:function(){return Yr},LayoutRouterContext:function(){return Hr},GlobalLayoutRouterContext:function(){return Br},TemplateContext:function(){return Kr}});var Oo=Oe(),Ce=Oo._(de()),Ct;(function(t){t.LAZY_INITIALIZED=\"LAZYINITIALIZED\",t.DATA_FETCH=\"DATAFETCH\",t.READY=\"READY\"})(Ct||(Ct={}));var Yr=Ce.default.createContext(null),Hr=Ce.default.createContext(null),Br=Ce.default.createContext(null),Kr=Ce.default.createContext(null);Yr.displayName=\"AppRouterContext\",Hr.displayName=\"LayoutRouterContext\",Br.displayName=\"GlobalLayoutRouterContext\",Kr.displayName=\"TemplateContext\"});var Qr=h((Y,Xr)=>{\"use strict\";Object.defineProperty(Y,\"__esModule\",{value:!0});function Do(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Do(Y,{requestIdleCallback:function(){return So},cancelIdleCallback:function(){return Co}});var So=typeof self<\"u\"&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(t){let e=Date.now();return self.setTimeout(function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})},1)},Co=typeof self<\"u\"&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(t){return clearTimeout(t)};(typeof Y.default==\"function\"||typeof Y.default==\"object\"&&Y.default!==null)&&typeof Y.default.__esModule>\"u\"&&(Object.defineProperty(Y.default,\"__esModule\",{value:!0}),Object.assign(Y.default,Y),Xr.exports=Y.default)});var tn=h((H,en)=>{\"use strict\";Object.defineProperty(H,\"__esModule\",{value:!0});Object.defineProperty(H,\"useIntersection\",{enumerable:!0,get:function(){return Io}});var _e=de(),Zr=Qr(),Jr=typeof IntersectionObserver==\"function\",At=new Map,je=[];function jo(t){let e={root:t.root||null,margin:t.rootMargin||\"\"},n=je.find(f=>f.root===e.root&&f.margin===e.margin),i;if(n&&(i=At.get(n),i))return i;let o=new Map,a=new IntersectionObserver(f=>{f.forEach(d=>{let c=o.get(d.target),p=d.isIntersecting||d.intersectionRatio>0;c&&p&&c(p)})},t);return i={id:e,observer:a,elements:o},je.push(e),At.set(e,i),i}function Ao(t,e,n){let{id:i,observer:o,elements:a}=jo(n);return a.set(t,e),o.observe(t),function(){if(a.delete(t),o.unobserve(t),a.size===0){o.disconnect(),At.delete(i);let d=je.findIndex(c=>c.root===i.root&&c.margin===i.margin);d>-1&&je.splice(d,1)}}}function Io(t){let{rootRef:e,rootMargin:n,disabled:i}=t,o=i||!Jr,[a,f]=(0,_e.useState)(!1),d=(0,_e.useRef)(null),c=(0,_e.useCallback)(x=>{d.current=x},[]);(0,_e.useEffect)(()=>{if(Jr){if(o||a)return;let x=d.current;if(x&&x.tagName)return Ao(x,P=>P&&f(P),{root:e?.current,rootMargin:n})}else if(!a){let x=(0,Zr.requestIdleCallback)(()=>f(!0));return()=>(0,Zr.cancelIdleCallback)(x)}},[o,n,e,a,d.current]);let p=(0,_e.useCallback)(()=>{f(!1)},[]);return[c,a,p]}(typeof H.default==\"function\"||typeof H.default==\"object\"&&H.default!==null)&&typeof H.default.__esModule>\"u\"&&(Object.defineProperty(H.default,\"__esModule\",{value:!0}),Object.assign(H.default,H),en.exports=H.default)});var rn=h(It=>{\"use strict\";Object.defineProperty(It,\"__esModule\",{value:!0});Object.defineProperty(It,\"normalizeLocalePath\",{enumerable:!0,get:function(){return Uo}});function Uo(t,e){let n,i=t.split(\"/\");return(e||[]).some(o=>i[1]&&i[1].toLowerCase()===o.toLowerCase()?(n=o,i.splice(1,1),t=i.join(\"/\")||\"/\",!0):!1),{pathname:t,detectedLocale:n}}});var on=h((B,nn)=>{\"use strict\";Object.defineProperty(B,\"__esModule\",{value:!0});Object.defineProperty(B,\"normalizeLocalePath\",{enumerable:!0,get:function(){return Lo}});var Lo=(t,e)=>process.env.__NEXT_I18N_SUPPORT?rn().normalizeLocalePath(t,e):{pathname:t,detectedLocale:void 0};(typeof B.default==\"function\"||typeof B.default==\"object\"&&B.default!==null)&&typeof B.default.__esModule>\"u\"&&(Object.defineProperty(B.default,\"__esModule\",{value:!0}),Object.assign(B.default,B),nn.exports=B.default)});var an=h(Ut=>{\"use strict\";Object.defineProperty(Ut,\"__esModule\",{value:!0});Object.defineProperty(Ut,\"detectDomainLocale\",{enumerable:!0,get:function(){return Wo}});function Wo(t,e,n){if(t){n&&(n=n.toLowerCase());for(let a of t){var i,o;let f=(i=a.domain)==null?void 0:i.split(\":\")[0].toLowerCase();if(e===f||n===a.defaultLocale.toLowerCase()||(o=a.locales)!=null&&o.some(d=>d.toLowerCase()===n))return a}}}});var ln=h((K,sn)=>{\"use strict\";Object.defineProperty(K,\"__esModule\",{value:!0});Object.defineProperty(K,\"detectDomainLocale\",{enumerable:!0,get:function(){return Mo}});var Mo=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(process.env.__NEXT_I18N_SUPPORT)return an().detectDomainLocale(...e)};(typeof K.default==\"function\"||typeof K.default==\"object\"&&K.default!==null)&&typeof K.default.__esModule>\"u\"&&(Object.defineProperty(K.default,\"__esModule\",{value:!0}),Object.assign(K.default,K),sn.exports=K.default)});var un=h((G,cn)=>{\"use strict\";Object.defineProperty(G,\"__esModule\",{value:!0});Object.defineProperty(G,\"getDomainLocale\",{enumerable:!0,get:function(){return Fo}});var zo=be(),qo=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Fo(t,e,n,i){if(process.env.__NEXT_I18N_SUPPORT){let o=on().normalizeLocalePath,a=ln().detectDomainLocale,f=e||o(t,n).detectedLocale,d=a(i,void 0,f);if(d){let c=\"http\"+(d.http?\"\":\"s\")+\"://\",p=f===d.defaultLocale?\"\":\"/\"+f;return\"\"+c+d.domain+(0,zo.normalizePathTrailingSlash)(\"\"+qo+p+t)}return!1}else return!1}(typeof G.default==\"function\"||typeof G.default==\"object\"&&G.default!==null)&&typeof G.default.__esModule>\"u\"&&(Object.defineProperty(G.default,\"__esModule\",{value:!0}),Object.assign(G.default,G),cn.exports=G.default)});var fn=h((X,dn)=>{\"use strict\";Object.defineProperty(X,\"__esModule\",{value:!0});Object.defineProperty(X,\"addBasePath\",{enumerable:!0,get:function(){return Ho}});var Vo=Ot(),$o=be(),Yo=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Ho(t,e){return(0,$o.normalizePathTrailingSlash)(process.env.__NEXT_MANUAL_CLIENT_BASE_PATH&&!e?t:(0,Vo.addPathPrefix)(t,Yo))}(typeof X.default==\"function\"||typeof X.default==\"object\"&&X.default!==null)&&typeof X.default.__esModule>\"u\"&&(Object.defineProperty(X.default,\"__esModule\",{value:!0}),Object.assign(X.default,X),dn.exports=X.default)});var mn=h((Q,pn)=>{\"use strict\";Object.defineProperty(Q,\"__esModule\",{value:!0});function Bo(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Bo(Q,{PrefetchKind:function(){return Lt},ACTION_REFRESH:function(){return Ko},ACTION_NAVIGATE:function(){return Go},ACTION_RESTORE:function(){return Xo},ACTION_SERVER_PATCH:function(){return Qo},ACTION_PREFETCH:function(){return Zo},ACTION_FAST_REFRESH:function(){return Jo},ACTION_SERVER_ACTION:function(){return ea}});var Ko=\"refresh\",Go=\"navigate\",Xo=\"restore\",Qo=\"server-patch\",Zo=\"prefetch\",Jo=\"fast-refresh\",ea=\"server-action\",Lt;(function(t){t.AUTO=\"auto\",t.FULL=\"full\",t.TEMPORARY=\"temporary\"})(Lt||(Lt={}));(typeof Q.default==\"function\"||typeof Q.default==\"object\"&&Q.default!==null)&&typeof Q.default.__esModule>\"u\"&&(Object.defineProperty(Q.default,\"__esModule\",{value:!0}),Object.assign(Q.default,Q),pn.exports=Q.default)});var vn=h((Z,Nn)=>{\"use client\";\"use strict\";Object.defineProperty(Z,\"__esModule\",{value:!0});Object.defineProperty(Z,\"default\",{enumerable:!0,get:function(){return pa}});var ta=Oe(),A=ta._(de()),hn=Wr(),yn=ft(),ra=Ze(),na=he(),ia=Fr(),oa=$r(),aa=Gr(),sa=tn(),la=un(),ca=fn(),bn=mn(),_n=new Set;function Wt(t,e,n,i,o,a){if(typeof window>\"u\"||!a&&!(0,yn.isLocalURL)(e))return;if(!i.bypassPrefetchedCheck){let d=typeof i.locale<\"u\"?i.locale:\"locale\"in t?t.locale:void 0,c=e+\"%\"+n+\"%\"+d;if(_n.has(c))return;_n.add(c)}let f=a?t.prefetch(e,o):t.prefetch(e,n,i);Promise.resolve(f).catch(d=>{throw d})}function ua(t){let n=t.currentTarget.getAttribute(\"target\");return n&&n!==\"_self\"||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&t.nativeEvent.which===2}function da(t,e,n,i,o,a,f,d,c,p){let{nodeName:x}=t.currentTarget;if(x.toUpperCase()===\"A\"&&(ua(t)||!c&&!(0,yn.isLocalURL)(n)))return;t.preventDefault();let P=()=>{let U=f??!0;\"beforePopState\"in e?e[o?\"replace\":\"push\"](n,i,{shallow:a,locale:d,scroll:U}):e[o?\"replace\":\"push\"](i||n,{forceOptimisticNavigation:!p,scroll:U})};c?A.default.startTransition(P):P()}function gn(t){return typeof t==\"string\"?t:(0,ra.formatUrl)(t)}var fa=A.default.forwardRef(function(e,n){let i,{href:o,as:a,children:f,prefetch:d=null,passHref:c,replace:p,shallow:x,scroll:S,locale:P,onClick:U,onMouseEnter:se,onTouchStart:ge,legacyBehavior:I=!1,...te}=e;i=f,I&&(typeof i==\"string\"||typeof i==\"number\")&&(i=A.default.createElement(\"a\",null,i));let _=A.default.useContext(oa.RouterContext),Ie=A.default.useContext(aa.AppRouterContext),W=_??Ie,M=!_,ie=d!==!1,le=d===null?bn.PrefetchKind.AUTO:bn.PrefetchKind.FULL;{let b=function(g){return new Error(\"Failed prop type: The prop `\"+g.key+\"` expects a \"+g.expected+\" in `<Link>`, but got `\"+g.actual+\"` instead.\"+(typeof window<\"u\"?`\nOpen your browser's console to view the Component stack trace.`:\"\"))};Object.keys({href:!0}).forEach(g=>{if(g===\"href\"){if(e[g]==null||typeof e[g]!=\"string\"&&typeof e[g]!=\"object\")throw b({key:g,expected:\"`string` or `object`\",actual:e[g]===null?\"null\":typeof e[g]})}else{let C=g}}),Object.keys({as:!0,replace:!0,scroll:!0,shallow:!0,passHref:!0,prefetch:!0,locale:!0,onClick:!0,onMouseEnter:!0,onTouchStart:!0,legacyBehavior:!0}).forEach(g=>{let C=typeof e[g];if(g===\"as\"){if(e[g]&&C!==\"string\"&&C!==\"object\")throw b({key:g,expected:\"`string` or `object`\",actual:C})}else if(g===\"locale\"){if(e[g]&&C!==\"string\")throw b({key:g,expected:\"`string`\",actual:C})}else if(g===\"onClick\"||g===\"onMouseEnter\"||g===\"onTouchStart\"){if(e[g]&&C!==\"function\")throw b({key:g,expected:\"`function`\",actual:C})}else if(g===\"replace\"||g===\"scroll\"||g===\"shallow\"||g===\"passHref\"||g===\"prefetch\"||g===\"legacyBehavior\"){if(e[g]!=null&&C!==\"boolean\")throw b({key:g,expected:\"`boolean`\",actual:C})}else{let Te=g}});let xe=A.default.useRef(!1);e.prefetch&&!xe.current&&!M&&(xe.current=!0,console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\"))}if(M&&!a){let b;if(typeof o==\"string\"?b=o:typeof o==\"object\"&&typeof o.pathname==\"string\"&&(b=o.pathname),b&&b.split(\"/\").some(ee=>ee.startsWith(\"[\")&&ee.endsWith(\"]\")))throw new Error(\"Dynamic href `\"+b+\"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\")}let{href:z,as:O}=A.default.useMemo(()=>{if(!_){let ee=gn(o);return{href:ee,as:a?gn(a):ee}}let[b,ne]=(0,hn.resolveHref)(_,o,!0);return{href:b,as:a?(0,hn.resolveHref)(_,a):ne||b}},[_,o,a]),ye=A.default.useRef(z),Ne=A.default.useRef(O),R;if(I){U&&console.warn('\"onClick\" was passed to <Link> with `href` of `'+o+'` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link'),se&&console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `'+o+'` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');try{R=A.default.Children.only(i)}catch{throw i?new Error(\"Multiple children were passed to <Link> with `href` of `\"+o+\"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\"+(typeof window<\"u\"?` \nOpen your browser's console to view the Component stack trace.`:\"\")):new Error(\"No children were passed to <Link> with `href` of `\"+o+\"` but one child is required https://nextjs.org/docs/messages/link-no-children\")}}else if(i?.type===\"a\")throw new Error(`Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor`);let w=I?R&&typeof R==\"object\"&&R.ref:n,[J,re,fe]=(0,sa.useIntersection)({rootMargin:\"200px\"}),ve=A.default.useCallback(b=>{(Ne.current!==O||ye.current!==z)&&(fe(),Ne.current=O,ye.current=z),J(b),w&&(typeof w==\"function\"?w(b):typeof w==\"object\"&&(w.current=b))},[O,w,z,fe,J]);A.default.useEffect(()=>{},[O,z,re,P,ie,_?.locale,W,M,le]);let oe={ref:ve,onClick(b){if(!b)throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');!I&&typeof U==\"function\"&&U(b),I&&R.props&&typeof R.props.onClick==\"function\"&&R.props.onClick(b),W&&(b.defaultPrevented||da(b,W,z,O,p,x,S,P,M,ie))},onMouseEnter(b){!I&&typeof se==\"function\"&&se(b),I&&R.props&&typeof R.props.onMouseEnter==\"function\"&&R.props.onMouseEnter(b),W&&((!ie||!0)&&M||Wt(W,z,O,{locale:P,priority:!0,bypassPrefetchedCheck:!0},{kind:le},M))},onTouchStart(b){!I&&typeof ge==\"function\"&&ge(b),I&&R.props&&typeof R.props.onTouchStart==\"function\"&&R.props.onTouchStart(b),W&&(!ie&&M||Wt(W,z,O,{locale:P,priority:!0,bypassPrefetchedCheck:!0},{kind:le},M))}};if((0,na.isAbsoluteUrl)(O))oe.href=O;else if(!I||c||R.type===\"a\"&&!(\"href\"in R.props)){let b=typeof P<\"u\"?P:_?.locale,ne=_?.isLocaleDomain&&(0,la.getDomainLocale)(O,b,_?.locales,_?.domainLocales);oe.href=ne||(0,ca.addBasePath)((0,ia.addLocale)(O,b,_?.defaultLocale))}return I?A.default.cloneElement(R,oe):A.default.createElement(\"a\",{...te,...oe},i)}),pa=fa;(typeof Z.default==\"function\"||typeof Z.default==\"object\"&&Z.default!==null)&&typeof Z.default.__esModule>\"u\"&&(Object.defineProperty(Z.default,\"__esModule\",{value:!0}),Object.assign(Z.default,Z),Nn.exports=Z.default)});var Tn=h((Qa,xn)=>{xn.exports=vn()});var ga={};ei(ga,{default:()=>_a,faqData:()=>ha,frontmatter:()=>ma});var s=Jt(nr());function He({children:t,quizSlug:e}){return React.createElement(\"article\",{className:\"max-w-4xl mx-auto text-gray-900\"},React.createElement(\"div\",{className:\"prose prose-lg max-w-none text-gray-900 [&>*]:text-gray-900\"},t))}var Pn=Jt(Tn());function Ae({task:t,href:e}){let n=e||`/quiz/${t}`;return React.createElement(\"div\",{className:\"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\"},React.createElement(\"div\",{className:\"text-center\"},React.createElement(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-2\"},\"Not sure which AI fits your workflow?\"),React.createElement(\"p\",{className:\"text-gray-600 mb-4\"},\"Take our 30-second quiz to get a personalized recommendation\"),React.createElement(Pn.default,{href:n,className:\"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\"},\"Take the \",t.charAt(0).toUpperCase()+t.slice(1),\" Quiz \\u2192\")))}var ma={title:\"Best AI Image & Texture Generators (2025) \\u2014 Midjourney vs Stable Diffusion, Ideogram & DALL-E\",description:\"Professional comparison of Midjourney, Stable Diffusion, Ideogram, and DALL-E for game developers, digital artists, and marketers. Find your perfect AI image generator.\",slug:\"best-ai-image-generators-2025-midjourney-vs-stable-diffusion-ideogram-dalle\",template:\"pillar\",cluster:\"image\",priority:\"Medium\",lastUpdated:\"2025-07-22\"},ha=[{q:\"What is the best AI for generating photorealistic images?\",a:\"Midjourney is widely regarded as the leader for photorealistic images due to its advanced aesthetic model and parameters like --style raw. However, DALL-E 4 and Stable Diffusion XL can also produce high-quality realistic images, especially with careful prompting.\"},{q:\"Which AI image generator is best for creating logos with text?\",a:\"Ideogram is the specialist for generating images with accurate and well-composed text. Its models are specifically trained for typography, making it the top choice for logos, posters, and marketing materials with text. DALL-E 4 is also improving in this area.\"},{q:\"Can I use images from Midjourney or DALL-E for commercial purposes?\",a:\"Yes, both platforms allow commercial use, but with important conditions. You own the assets you create. However, Midjourney requires a Pro or Mega plan for companies with over $1M in annual revenue. Furthermore, AI-generated images currently exist in a legal gray area and may not be eligible for copyright protection.\"},{q:\"What is the cheapest way to generate a lot of AI images?\",a:\"Using an API for a speed-optimized model like Stable Diffusion XL Lightning is often the most cost-effective method for high-volume generation, with costs potentially under $0.002 per image. However, this requires technical setup. For subscription models, Ideogram's paid plans offer a large number of credits for a relatively low monthly cost.\"},{q:\"What's the difference between Midjourney and Stable Diffusion?\",a:\"The main difference is control versus ease of use. Midjourney offers a curated, high-quality aesthetic experience through its Discord interface. Stable Diffusion is an open-source model that provides maximum control and customization (e.g., training your own models, using ControlNet) but has a much steeper learning curve and often requires a powerful local computer or API integration.\"},{q:\"Which AI image generator is best for game development?\",a:\"A hybrid approach works best: Midjourney for concept art and hero images, Stable Diffusion for bulk asset generation and textures. Stable Diffusion's ControlNet feature is particularly valuable for maintaining consistency across character designs and creating tileable textures.\"}];function En(t){let e=Object.assign({h2:\"h2\",p:\"p\",strong:\"strong\",hr:\"hr\",em:\"em\",h3:\"h3\"},t.components);return(0,s.jsxDEV)(He,{quizSlug:\"image\",children:[(0,s.jsxDEV)(Ae,{task:\"image\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:16,columnNumber:1},this),(0,s.jsxDEV)(e.h2,{children:\"The New Creative Frontier: Beyond Novelty to Professional Integration\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:18,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:\"The era of AI image generation has moved beyond novelty and into a phase of serious professional integration. For game developers, digital artists, and marketers, these tools are no longer experimental toys but powerful assets capable of accelerating workflows, inspiring new concepts, and producing commercial-grade visuals.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:20,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[\"The market has matured into a competitive landscape where the leading platforms\\u2014\",(0,s.jsxDEV)(e.strong,{children:\"Midjourney, Stable Diffusion, Ideogram, and DALL-E\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:22,columnNumber:81},this),'\\u2014each champion a distinct approach to creation. This analysis reveals that there is no single \"best\" generator. The optimal choice balances ',(0,s.jsxDEV)(e.strong,{children:\"Aesthetic Quality, Technical Control, Typographic Fidelity, and Commercial Viability\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:22,columnNumber:275},this),\".\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:22,columnNumber:1},this),(0,s.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:24,columnNumber:1},this),(0,s.jsxDEV)(e.h2,{children:\"The Gauntlet: A Unified Prompt Showdown\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:26,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:\"To test the limits of each model, we used a single, complex prompt designed to evaluate photorealism, object detail, lighting, depth of field, and in-image text generation.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:28,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"The Prompt:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:30,columnNumber:1},this),\" \",(0,s.jsxDEV)(e.em,{children:`\"Photorealistic product shot for a marketing campaign. A sleek, matte black bottle of high-end perfume named 'Etherea' sits on a wet, reflective marble surface. In the background, out of focus, are glowing neon orchids. The words 'Etherea: The Scent of Tomorrow' are elegantly displayed in a modern sans-serif font at the bottom.\"`},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:30,columnNumber:17},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:30,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:\"The Results: Side-by-Side Visual Analysis\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:32,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Midjourney v6:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:34,columnNumber:1},this),' Delivers unparalleled photorealism. The lighting on the wet marble, the texture of the matte bottle, and the soft bokeh of the neon orchids are exceptionally cinematic and \"art-directed\". However, it struggles with the text, rendering a stylized but illegible version of the brand name and tagline.']},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:34,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Stable Diffusion XL Lightning:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:36,columnNumber:1},this),\" Produces a high-quality image with remarkable speed, a testament to its efficient architecture. The composition is strong, but the fine details and lighting nuances lack the hyper-realistic polish of Midjourney. It represents a powerful balance of speed and quality, ideal for rapid iteration.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:36,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Ideogram:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:38,columnNumber:1},this),' Excels where others fail. The text \"Etherea: The Scent of Tomorrow\" is rendered with near-perfect clarity and elegant composition, demonstrating its core strength. The surrounding image is competent but less photorealistic than Midjourney or DALL-E, appearing more like a high-quality digital illustration than a photograph.']},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:38,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"DALL-E 4 (via GPT-4o):\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:40,columnNumber:1},this),\" Shows superior prompt comprehension. It successfully interprets and renders every element of the prompt correctly\\u2014the matte bottle, wet surface, neon bokeh, and the text. The integration with ChatGPT allows for this nuanced understanding. While the text is legible, Ideogram's is more typographically refined.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:40,columnNumber:1},this),(0,s.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:42,columnNumber:1},this),(0,s.jsxDEV)(e.h2,{children:\"Prompt Showdown Scorecard\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:44,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[`| Platform | Photorealism (1-5) | Prompt Adherence (1-5) | Typography (1-5) | Overall Aesthetic (1-5) | Verdict |\n|----------|---------------------|-------------------------|-------------------|-------------------------|---------|\n| `,(0,s.jsxDEV)(e.strong,{children:\"Midjourney v6\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:48,columnNumber:3},this),` | 5 | 4 | 2 | 5 | The Artist: Unmatched beauty, but can't write. |\n| `,(0,s.jsxDEV)(e.strong,{children:\"SDXL Lightning\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:49,columnNumber:3},this),` | 4 | 4 | 2 | 4 | The Engineer: Fast and capable, a workhorse. |\n| `,(0,s.jsxDEV)(e.strong,{children:\"Ideogram\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:50,columnNumber:3},this),` | 3 | 4 | 5 | 4 | The Typographer: Flawless text, good-enough image. |\n| `,(0,s.jsxDEV)(e.strong,{children:\"DALL-E 4\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:51,columnNumber:3},this),\" | 4 | 5 | 4 | 4 | The Co-Creator: Understands everything, a true generalist. |\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:46,columnNumber:1},this),(0,s.jsxDEV)(\"div\",{style:{textAlign:\"right\",fontSize:\"0.9rem\"},children:(0,s.jsxDEV)(\"a\",{href:\"/export/image-scorecard.csv\",children:\"Export to Sheets \\u2192\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:53,columnNumber:52},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:53,columnNumber:1},this),(0,s.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:55,columnNumber:1},this),(0,s.jsxDEV)(e.h2,{children:\"The Business Imperative: Cost vs. Commercial Rights\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:57,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:\"Choosing a platform involves more than just creative output; it requires a careful analysis of cost and legal considerations.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:59,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:\"Economic Analysis: The Cost of 1,000 Images\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:61,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:\"The cost per image varies dramatically depending on the platform's pricing model\\u2014subscription, credits, or pay-per-image API calls.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:63,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[`| Platform | Recommended Plan/Method | Total Cost for 1,000 Images | Effective Cost-per-Image | Key Considerations |\n|----------|-------------------------|------------------------------|--------------------------|-------------------|\n| `,(0,s.jsxDEV)(e.strong,{children:\"Midjourney\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:67,columnNumber:3},this),` | Standard Plan ($30/mo) | ~$30 | ~$0.03 | Subscription includes ~900 fast generations. |\n| `,(0,s.jsxDEV)(e.strong,{children:\"SDXL Lightning\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:68,columnNumber:3},this),` | Replicate API | ~$1.40 | ~$0.0014 | Requires technical setup; API pricing varies by provider. |\n| `,(0,s.jsxDEV)(e.strong,{children:\"Ideogram\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:69,columnNumber:3},this),` | Plus Plan ($16/mo, billed yearly) | ~$16 | ~$0.016 | Includes 1,000 priority credits/month. |\n| `,(0,s.jsxDEV)(e.strong,{children:\"DALL-E 4 (API)\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:70,columnNumber:3},this),\" | OpenAI API (DALL-E 3) | ~$40 | $0.04 | Pay-as-you-go; price is for standard quality. |\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:65,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:\"Legal Framework: Commercial Licensing\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:72,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:\"The ability to use AI-generated images commercially is complex. Under U.S. law, AI-generated images generally cannot be copyrighted, making the platform's Terms of Service paramount.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:74,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[`| Platform | Ownership of Output | Commercial Use Allowed? | Key Restrictions & Revenue Caps | Private Generation? |\n|----------|---------------------|-------------------------|--------------------------------|-------------------|\n| `,(0,s.jsxDEV)(e.strong,{children:\"Midjourney\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:78,columnNumber:3},this),` | You own assets you create. | Yes, with paid plans. | Businesses with >$1M gross annual revenue must use Pro/Mega plan. | Yes (Pro/Mega plans) |\n| `,(0,s.jsxDEV)(e.strong,{children:\"Stable Diffusion\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:79,columnNumber:3},this),` | You own the output. | Yes, with license compliance. | Use is restricted from specific harmful applications. Enterprise license needed for companies >$1M revenue. | N/A (local/private) |\n| `,(0,s.jsxDEV)(e.strong,{children:\"Ideogram\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:80,columnNumber:3},this),` | You are free to use images. | Yes, on all plans. | Free plan generations are public by default, posing a risk for proprietary work. | Yes (Plus/Pro plans) |\n| `,(0,s.jsxDEV)(e.strong,{children:\"DALL-E\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:81,columnNumber:3},this),\" | You own assets you create. | Yes, per OpenAI's terms. | Subject to OpenAI's Content Policy; no explicit revenue caps mentioned for basic use. | Yes (via API/ChatGPT) |\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:76,columnNumber:1},this),(0,s.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:83,columnNumber:1},this),(0,s.jsxDEV)(e.h2,{children:\"Strategic Recommendations by Professional Persona\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:85,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:[\"\\u{1F3AE} \",(0,s.jsxDEV)(e.strong,{children:\"For the Game Developer\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:87,columnNumber:8},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:87,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[\"A hybrid approach is best. Use \",(0,s.jsxDEV)(e.strong,{children:\"Midjourney\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:88,columnNumber:32},this),\" for high-fidelity concept art and marketing visuals. For asset creation, \",(0,s.jsxDEV)(e.strong,{children:\"Stable Diffusion\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:88,columnNumber:120},this),\" is superior for generating tileable textures, large asset batches, and maintaining character consistency with tools like ControlNet.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:88,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:[\"\\u{1F3A8} \",(0,s.jsxDEV)(e.strong,{children:\"For the Digital Artist / Illustrator\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:90,columnNumber:8},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:90,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Midjourney\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:91,columnNumber:1},this),\" is the primary tool for its unparalleled aesthetic control and painterly results. Use \",(0,s.jsxDEV)(e.strong,{children:\"DALL-E 4\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:91,columnNumber:102},this),\" for its conversational interface to rapidly brainstorm and overcome creative blocks.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:91,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:[\"\\u{1F4C8} \",(0,s.jsxDEV)(e.strong,{children:\"For the Marketer / Designer\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:93,columnNumber:8},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:93,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[\"Start with \",(0,s.jsxDEV)(e.strong,{children:\"Ideogram\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:94,columnNumber:12},this),\" for any asset requiring text, such as social media graphics or logos. Use \",(0,s.jsxDEV)(e.strong,{children:\"DALL-E 4\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:94,columnNumber:99},this),\" for quickly generating blog headers and A/B testing ad concepts. For high-stakes campaign hero images, Midjourney's Pro plan ensures both top-tier quality and privacy.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:94,columnNumber:1},this),(0,s.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:96,columnNumber:1},this),(0,s.jsxDEV)(e.h2,{children:\"Deep-dive profiles\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:98,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:[\"Midjourney \\u2014 \",(0,s.jsxDEV)(e.em,{children:\"the aesthetic perfectionist\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:100,columnNumber:18},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:100,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Strengths.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:102,columnNumber:1},this),` Unmatched photorealism and artistic quality; intuitive Discord interface; strong community and prompt sharing.\n`,(0,s.jsxDEV)(e.strong,{children:\"Weaknesses.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:103,columnNumber:1},this),` Poor text rendering; requires Discord; limited fine-tuning control.\n`,(0,s.jsxDEV)(e.em,{children:\"Perfect for: Concept art, marketing hero images, artistic illustrations.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:104,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:102,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:[\"Stable Diffusion \\u2014 \",(0,s.jsxDEV)(e.em,{children:\"the technical powerhouse\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:106,columnNumber:24},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:106,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Strengths.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:108,columnNumber:1},this),` Open-source flexibility; ControlNet for precise control; can run locally; extensive model ecosystem.\n`,(0,s.jsxDEV)(e.strong,{children:\"Weaknesses.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:109,columnNumber:1},this),` Steep learning curve; requires technical setup; inconsistent quality without expertise.\n`,(0,s.jsxDEV)(e.em,{children:\"Perfect for: Bulk generation, custom training, technical workflows.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:110,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:108,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:[\"Ideogram \\u2014 \",(0,s.jsxDEV)(e.em,{children:\"the typography specialist\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:112,columnNumber:16},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:112,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Strengths.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:114,columnNumber:1},this),` Exceptional text rendering; clean, modern aesthetic; affordable pricing; good prompt adherence.\n`,(0,s.jsxDEV)(e.strong,{children:\"Weaknesses.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:115,columnNumber:1},this),` Less photorealistic than competitors; smaller community; newer platform.\n`,(0,s.jsxDEV)(e.em,{children:\"Perfect for: Logos, social media graphics, text-heavy designs.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:116,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:114,columnNumber:1},this),(0,s.jsxDEV)(e.h3,{children:[\"DALL-E 4 \\u2014 \",(0,s.jsxDEV)(e.em,{children:\"the conversational creator\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:118,columnNumber:16},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:118,columnNumber:1},this),(0,s.jsxDEV)(e.p,{children:[(0,s.jsxDEV)(e.strong,{children:\"Strengths.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:120,columnNumber:1},this),` Superior prompt understanding; ChatGPT integration; consistent quality; ethical safeguards.\n`,(0,s.jsxDEV)(e.strong,{children:\"Weaknesses.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:121,columnNumber:1},this),` More expensive than alternatives; less artistic flair than Midjourney; API-dependent.\n`,(0,s.jsxDEV)(e.em,{children:\"Perfect for: Rapid ideation, conversational workflows, general-purpose generation.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:122,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:120,columnNumber:1},this),(0,s.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:124,columnNumber:1},this),(0,s.jsxDEV)(Ae,{task:\"image\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:126,columnNumber:1},this),(0,s.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:128,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\",lineNumber:14,columnNumber:1},this)}function ba(t={}){let{wrapper:e}=t.components||{};return e?(0,s.jsxDEV)(e,Object.assign({},t,{children:(0,s.jsxDEV)(En,t,void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-06fccc4c-10a7-42cb-92ba-cc03d36354b2.mdx\"},this):En(t)}var _a=ba;return ti(ga);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "pillars/best-ai-image-generators.mdx", "_raw": {"sourceFilePath": "pillars/best-ai-image-generators.mdx", "sourceFileName": "best-ai-image-generators.mdx", "sourceFileDir": "pillars", "contentType": "mdx", "flattenedPath": "pillars/best-ai-image-generators"}, "type": "<PERSON><PERSON>", "url": "/best-ai-image-generators-2025-midjourney-vs-stable-diffusion-ideogram-dalle", "slugFromPath": "pillars/best-ai-image-generators"}, "documentHash": "1753200152932", "hasWarnings": false, "documentTypeName": "<PERSON><PERSON>"}, "pillars/best-ai-video-generators.mdx": {"document": {"title": "AI Video Generators & Editors (2025) — CapCut vs Runway, Pika & Descript", "description": "Professional comparison of AI video tools for creators, marketers, and filmmakers. Auto-editors, prompt-to-video engines, and AI post-production solutions.", "slug": "best-ai-video-generators-2025-capcut-vs-runway-pika-descript", "cluster": "video", "template": "pillar", "priority": "Medium", "lastUpdated": "2025-07-22", "body": {"raw": "\nimport PillarLayout from \"@/templates/PillarLayout\"\nimport Quiz<PERSON><PERSON> from \"@/components/QuizCta\"\n\n<PillarLayout quizSlug=\"video\">\n\n<QuizCta task=\"video\" />\n\n## 2025 Report — The New AI-Video Stack\n\nAI video is no longer a gimmick. Creators assemble **multi-tool workflows** that swap hours of manual work for minutes of prompt-driven generation. The landscape has evolved into three distinct layers, each serving different creative needs and skill levels.\n\nWe compare tools across three critical layers:\n\n1. **Auto-editors** (CapCut, Veed.io) for TikTok/Reels velocity\n2. **Prompt-to-video engines** (Pika, Runway Gen-3) for original footage\n3. **AI post-production** (Descript, Captions.ai) for polish & localization\n\n---\n\n## Layer 1 — Auto-Editors for Social Media Velocity\n\n### CapCut Pro — The Creator's Flywheel\n\n**Perfect for solo creators who need dozens of clips per week.**\n\n| Metric | Value |\n|--------|-------|\n| **Max Resolution** | 4K 60fps |\n| **Render Time (15s clip)** | 30-60 seconds |\n| **Free Watermark** | **No** |\n| **AI Features** | Auto-captions, background removal, voice enhancement |\n| **Pricing** | Free tier available, Pro $9.99/month |\n\n**Strengths:** Mobile-first design, extensive template library, seamless TikTok integration, no watermark on free tier.\n**Weaknesses:** Limited collaboration features, primarily mobile-focused workflow.\n\n### Veed.io — The Collaborative SaaS Solution\n\n**Built-in Brand Kit + shared workspaces make it the marketer's choice.**\n\n| Metric | Value |\n|--------|-------|\n| **Max Resolution** | 4K |\n| **Render Time (15s clip)** | 45-75 seconds |\n| **Free Watermark** | **Yes** |\n| **AI Features** | Auto-subtitles, background removal, voice cloning |\n| **Pricing** | Free tier with watermark, Pro $24/month |\n\n**Strengths:** Team collaboration, brand consistency tools, web-based workflow, extensive format support.\n**Weaknesses:** Watermark on free tier, higher pricing for advanced features.\n\n---\n\n## Layer 2 — Prompt-to-Video Synthesis\n\nThe frontier of AI video generation, where text prompts become moving images.\n\n| Tool | Strength | Weakness | Best For |\n|------|----------|----------|----------|\n| **Pika** | Fast generation, surreal motion effects, Modify Region & Lip Sync | 1080p max resolution, credit-based pricing | Creative experiments, social content |\n| **Runway Gen-3 Alpha** | Cinematic realism, advanced keyframing, precise camera controls | 1280×768 resolution cap, queue times during peak | Professional filmmaking, commercials |\n\n### Pika — The Creative Experimenter\n\n**Rapid iteration meets surreal possibilities.**\n\n- **Generation Speed:** 30-60 seconds per 3-second clip\n- **Unique Features:** Modify Region (edit specific areas), Lip Sync, Expand Canvas\n- **Pricing:** Credit-based system, $10/month for 700 credits\n- **Resolution:** Up to 1080p\n- **Best Use Cases:** Social media content, creative experiments, rapid prototyping\n\n### Runway Gen-3 Alpha — The Cinematic Powerhouse\n\n**Industry-leading realism with professional controls.**\n\n- **Generation Speed:** 2-5 minutes per 4-second clip\n- **Unique Features:** Camera controls, motion brush, keyframe animation\n- **Pricing:** $15/month for 625 credits\n- **Resolution:** Up to 1280×768\n- **Best Use Cases:** Film production, commercials, high-end marketing content\n\n---\n\n## Layer 3 — AI Post-Production\n\n### Descript — The Text-Based Editor\n\n**Edit video by editing the transcript. Revolutionary for long-form content.**\n\n**Key Features:**\n- **Overdub:** AI voice cloning for corrections\n- **Filler Word Removal:** Automatic \"um\" and \"uh\" elimination\n- **Studio Sound:** AI audio enhancement\n- **Transcription:** Industry-leading accuracy\n- **Pricing:** Free tier available, Creator $12/month\n\n**Ideal for:** Podcasts, webinars, training videos, interview content.\n\n### Captions.ai — The Social Subtitle Engine\n\n**Animated, mobile-style captions that drive engagement.**\n\n**Key Features:**\n- **AI Dubbing:** 29+ languages with voice matching\n- **Eye Contact Correction:** AI-powered gaze adjustment\n- **Script Writer:** AI-generated video scripts\n- **Animated Captions:** TikTok-style subtitle animations\n- **Pricing:** Free tier with unlimited exports, Pro $20/month\n\n**Ideal for:** Social media content, international marketing, accessibility compliance.\n\n---\n\n## Case Study: \"Bigfoot Boys\" — Daily AI Vlogs at 80% Less Effort\n\n**The Challenge:** Creating daily vlog content without burning out.\n\n**The AI-Powered Solution:**\n1. **Script Generation:** ChatGPT creates engaging daily topics and scripts\n2. **Video Creation:** Google Veo 3 generates talking-head footage\n3. **Auto-Publishing:** Zapier automatically posts to TikTok and Instagram\n\n**Results:**\n- **3.1M followers** across platforms\n- **Active work per clip:** ~10 minutes (vs. 3-5 hours traditional)\n- **Consistency:** Daily uploads without creator fatigue\n- **Scalability:** Multiple characters and storylines\n\n**Key Insight:** The most successful AI video creators focus on storytelling and audience engagement, letting AI handle the technical production.\n\n---\n\n## Strategic Recommendations by Creator Type\n\n### 🎬 **For the Social Media Manager**\n**Recommended Stack:** CapCut + Captions.ai\n- **Workflow:** Bulk content creation with consistent branding\n- **Budget:** $30/month combined\n- **Output:** 20-50 videos per month\n- **Key Benefit:** Speed and consistency at scale\n\n### 🎥 **For the Indie Filmmaker**\n**Recommended Stack:** Runway Gen-3 + Descript\n- **Workflow:** High-quality concept videos with professional post-production\n- **Budget:** $27/month combined\n- **Output:** 5-10 high-quality videos per month\n- **Key Benefit:** Cinematic quality without crew costs\n\n### 🏢 **For the Corporate Marketer**\n**Recommended Stack:** Veed.io + Descript\n- **Workflow:** Team collaboration with brand consistency\n- **Budget:** $36/month combined\n- **Output:** 10-20 branded videos per month\n- **Key Benefit:** Team workflows with professional polish\n\n### 📱 **For the Content Creator**\n**Recommended Stack:** Pika + Captions.ai\n- **Workflow:** Creative experimentation with viral potential\n- **Budget:** $30/month combined\n- **Output:** 30+ experimental videos per month\n- **Key Benefit:** Unique content that stands out\n\n---\n\n## The 2025 Video AI Landscape: What's Coming\n\n### **Emerging Trends:**\n- **Higher Resolutions:** 4K prompt-to-video expected by Q3 2025\n- **Longer Clips:** 60-second generations becoming standard\n- **Real-Time Generation:** Live streaming with AI backgrounds\n- **Voice-Video Sync:** Perfect lip-sync across all tools\n- **Multi-Modal Integration:** Text, image, and audio prompts combined\n\n### **Market Consolidation:**\n- Major platforms acquiring specialized tools\n- Integration between editing and generation platforms\n- API-first approaches for custom workflows\n\n---\n\n## Deep-Dive Tool Profiles\n\n### CapCut — _the mobile-first velocity engine_\n\n**Strengths:** No watermark on free tier; extensive template library; seamless social media integration; mobile-optimized workflow.\n**Weaknesses:** Limited collaboration features; primarily mobile-focused; fewer advanced AI features.\n*Perfect for: Solo creators, TikTok/Instagram content, high-volume social media posting.*\n\n### Runway Gen-3 — _the cinematic realism leader_\n\n**Strengths:** Industry-leading video quality; advanced camera controls; professional-grade output; extensive customization.\n**Weaknesses:** Higher cost per generation; longer processing times; resolution limitations.\n*Perfect for: Film production, commercials, high-end marketing content, creative agencies.*\n\n### Pika — _the creative experimenter's playground_\n\n**Strengths:** Fast generation times; unique creative features; affordable pricing; innovative editing tools.\n**Weaknesses:** Resolution limitations; credit-based pricing can add up; less photorealistic than competitors.\n*Perfect for: Creative experiments, social media content, rapid prototyping, artistic projects.*\n\n### Veed.io — _the collaborative marketing platform_\n\n**Strengths:** Team collaboration features; brand consistency tools; web-based workflow; extensive format support.\n**Weaknesses:** Watermark on free tier; higher pricing; less advanced AI features than specialized tools.\n*Perfect for: Marketing teams, brand content, collaborative projects, corporate communications.*\n\n### Descript — _the text-based editing revolution_\n\n**Strengths:** Revolutionary transcript-based editing; excellent audio AI; voice cloning capabilities; intuitive workflow.\n**Weaknesses:** Learning curve for traditional editors; limited video effects; primarily suited for talking-head content.\n*Perfect for: Podcasts, interviews, educational content, webinars, voice-over work.*\n\n### Captions.ai — _the social engagement optimizer_\n\n**Strengths:** Unlimited free exports; no watermark; excellent subtitle animations; multi-language dubbing.\n**Weaknesses:** Limited editing features; focused primarily on captions; fewer creative tools.\n*Perfect for: Social media optimization, international content, accessibility compliance, engagement boosting.*\n\n---\n\n<QuizCta task=\"video\" />\n\n---\n\nexport const faqData = [\n  { q: \"Which AI makes the most realistic video?\", a: \"Runway Gen-3 Alpha currently leads on photorealism and camera control, producing cinema-quality footage that's often indistinguishable from traditional filming for short clips.\" },\n  { q: \"What's the cheapest way to create lots of social media videos?\", a: \"CapCut's free plan offers the best value: no watermark, mobile-first design, auto-templates, and seamless social media integration. Perfect for high-volume content creation.\" },\n  { q: \"Can I monetize AI-generated video footage?\", a: \"Yes, but check each tool's license. Runway and Veed allow commercial use on paid tiers. CapCut and Pika also permit commercial use. Always review terms for your specific use case.\" },\n  { q: \"What resolution limits should I expect from AI video tools?\", a: \"Prompt-to-video tools are currently capped at HD (Runway 1280×768, Pika 1080p). Auto-editors like CapCut and Veed can export 4K. Expect 4K prompt-to-video by late 2025.\" },\n  { q: \"Which tool is best for team collaboration on video projects?\", a: \"Veed.io excels at team collaboration with shared workspaces, brand kits, and commenting features. Descript also offers good collaboration for transcript-based editing workflows.\" },\n  { q: \"How long does it take to generate AI video?\", a: \"Generation times vary: Pika (30-60 seconds for 3s clips), Runway Gen-3 (2-5 minutes for 4s clips). Auto-editors like CapCut render 15-second clips in 30-60 seconds.\" }\n]\n\n</PillarLayout>\n", "code": "var Component=(()=>{var Yr=Object.create;var Ue=Object.defineProperty;var Xr=Object.getOwnPropertyDescriptor;var Qr=Object.getOwnPropertyNames;var Zr=Object.getPrototypeOf,Jr=Object.prototype.hasOwnProperty;var b=(n,e)=>()=>(e||n((e={exports:{}}).exports,e),e.exports),ei=(n,e)=>{for(var i in e)Ue(n,i,{get:e[i],enumerable:!0})},Jn=(n,e,i,o)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let l of Qr(e))!Jr.call(n,l)&&l!==i&&Ue(n,l,{get:()=>e[l],enumerable:!(o=Xr(e,l))||o.enumerable});return n};var et=(n,e,i)=>(i=n!=null?Yr(Zr(n)):{},Jn(e||!n||!n.__esModule?Ue(i,\"default\",{value:n,enumerable:!0}):i,n)),ni=n=>Jn(Ue({},\"__esModule\",{value:!0}),n);var de=b((yl,nt)=>{nt.exports=React});var tt=b(Ke=>{\"use strict\";(function(){\"use strict\";var n=de(),e=Symbol.for(\"react.element\"),i=Symbol.for(\"react.portal\"),o=Symbol.for(\"react.fragment\"),l=Symbol.for(\"react.strict_mode\"),s=Symbol.for(\"react.profiler\"),m=Symbol.for(\"react.provider\"),d=Symbol.for(\"react.context\"),u=Symbol.for(\"react.forward_ref\"),f=Symbol.for(\"react.suspense\"),k=Symbol.for(\"react.suspense_list\"),E=Symbol.for(\"react.memo\"),v=Symbol.for(\"react.lazy\"),A=Symbol.for(\"react.offscreen\"),se=Symbol.iterator,Ne=\"@@iterator\";function S(r){if(r===null||typeof r!=\"object\")return null;var a=se&&r[se]||r[Ne];return typeof a==\"function\"?a:null}var ne=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function _(r){{for(var a=arguments.length,c=new Array(a>1?a-1:0),p=1;p<a;p++)c[p-1]=arguments[p];Se(\"error\",r,c)}}function Se(r,a,c){{var p=ne.ReactDebugCurrentFrame,g=p.getStackAddendum();g!==\"\"&&(a+=\"%s\",c=c.concat([g]));var T=c.map(function(y){return String(y)});T.unshift(\"Warning: \"+a),Function.prototype.apply.call(console[r],console,T)}}var j=!1,M=!1,ie=!1,ae=!1,L=!1,R;R=Symbol.for(\"react.module.reference\");function xe(r){return!!(typeof r==\"string\"||typeof r==\"function\"||r===o||r===s||L||r===l||r===f||r===k||ae||r===A||j||M||ie||typeof r==\"object\"&&r!==null&&(r.$$typeof===v||r.$$typeof===E||r.$$typeof===m||r.$$typeof===d||r.$$typeof===u||r.$$typeof===R||r.getModuleId!==void 0))}function ye(r,a,c){var p=r.displayName;if(p)return p;var g=a.displayName||a.name||\"\";return g!==\"\"?c+\"(\"+g+\")\":c}function W(r){return r.displayName||\"Context\"}function U(r){if(r==null)return null;if(typeof r.tag==\"number\"&&_(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof r==\"function\")return r.displayName||r.name||null;if(typeof r==\"string\")return r;switch(r){case o:return\"Fragment\";case i:return\"Portal\";case s:return\"Profiler\";case l:return\"StrictMode\";case f:return\"Suspense\";case k:return\"SuspenseList\"}if(typeof r==\"object\")switch(r.$$typeof){case d:var a=r;return W(a)+\".Consumer\";case m:var c=r;return W(c._context)+\".Provider\";case u:return ye(r,r.render,\"ForwardRef\");case E:var p=r.displayName||null;return p!==null?p:U(r.type)||\"Memo\";case v:{var g=r,T=g._payload,y=g._init;try{return U(y(T))}catch{return null}}}return null}var J=Object.assign,te=0,me,ge,oe,h,re,ee,Ae;function Ie(){}Ie.__reactDisabledLog=!0;function ke(){{if(te===0){me=console.log,ge=console.info,oe=console.warn,h=console.error,re=console.group,ee=console.groupCollapsed,Ae=console.groupEnd;var r={configurable:!0,enumerable:!0,value:Ie,writable:!0};Object.defineProperties(console,{info:r,log:r,warn:r,error:r,group:r,groupCollapsed:r,groupEnd:r})}te++}}function N(){{if(te--,te===0){var r={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:J({},r,{value:me}),info:J({},r,{value:ge}),warn:J({},r,{value:oe}),error:J({},r,{value:h}),group:J({},r,{value:re}),groupCollapsed:J({},r,{value:ee}),groupEnd:J({},r,{value:Ae})})}te<0&&_(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var w=ne.ReactCurrentDispatcher,Te;function ve(r,a,c){{if(Te===void 0)try{throw Error()}catch(g){var p=g.stack.trim().match(/\\n( *(at )?)/);Te=p&&p[1]||\"\"}return`\n`+Te+r}}var je=!1,De;{var Pr=typeof WeakMap==\"function\"?WeakMap:Map;De=new Pr}function Ln(r,a){if(!r||je)return\"\";{var c=De.get(r);if(c!==void 0)return c}var p;je=!0;var g=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var T;T=w.current,w.current=null,ke();try{if(a){var y=function(){throw Error()};if(Object.defineProperty(y.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(y,[])}catch(O){p=O}Reflect.construct(r,[],y)}else{try{y.call()}catch(O){p=O}r.call(y.prototype)}}else{try{throw Error()}catch(O){p=O}r()}}catch(O){if(O&&p&&typeof O.stack==\"string\"){for(var x=O.stack.split(`\n`),z=p.stack.split(`\n`),D=x.length-1,P=z.length-1;D>=1&&P>=0&&x[D]!==z[P];)P--;for(;D>=1&&P>=0;D--,P--)if(x[D]!==z[P]){if(D!==1||P!==1)do if(D--,P--,P<0||x[D]!==z[P]){var I=`\n`+x[D].replace(\" at new \",\" at \");return r.displayName&&I.includes(\"<anonymous>\")&&(I=I.replace(\"<anonymous>\",r.displayName)),typeof r==\"function\"&&De.set(r,I),I}while(D>=1&&P>=0);break}}}finally{je=!1,w.current=T,N(),Error.prepareStackTrace=g}var ce=r?r.displayName||r.name:\"\",le=ce?ve(ce):\"\";return typeof r==\"function\"&&De.set(r,le),le}function Wr(r,a,c){return Ln(r,!1)}function Ur(r){var a=r.prototype;return!!(a&&a.isReactComponent)}function Pe(r,a,c){if(r==null)return\"\";if(typeof r==\"function\")return Ln(r,Ur(r));if(typeof r==\"string\")return ve(r);switch(r){case f:return ve(\"Suspense\");case k:return ve(\"SuspenseList\")}if(typeof r==\"object\")switch(r.$$typeof){case u:return Wr(r.render);case E:return Pe(r.type,a,c);case v:{var p=r,g=p._payload,T=p._init;try{return Pe(T(g),a,c)}catch{}}}return\"\"}var fe=Object.prototype.hasOwnProperty,qn={},Fn=ne.ReactDebugCurrentFrame;function We(r){if(r){var a=r._owner,c=Pe(r.type,r._source,a?a.type:null);Fn.setExtraStackFrame(c)}else Fn.setExtraStackFrame(null)}function Rr(r,a,c,p,g){{var T=Function.call.bind(fe);for(var y in r)if(T(r,y)){var x=void 0;try{if(typeof r[y]!=\"function\"){var z=Error((p||\"React class\")+\": \"+c+\" type `\"+y+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof r[y]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw z.name=\"Invariant Violation\",z}x=r[y](a,y,p,c,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(D){x=D}x&&!(x instanceof Error)&&(We(g),_(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",p||\"React class\",c,y,typeof x),We(null)),x instanceof Error&&!(x.message in qn)&&(qn[x.message]=!0,We(g),_(\"Failed %s type: %s\",c,x.message),We(null))}}}var zr=Array.isArray;function Me(r){return zr(r)}function Er(r){{var a=typeof Symbol==\"function\"&&Symbol.toStringTag,c=a&&r[Symbol.toStringTag]||r.constructor.name||\"Object\";return c}}function wr(r){try{return Vn(r),!1}catch{return!0}}function Vn(r){return\"\"+r}function Bn(r){if(wr(r))return _(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",Er(r)),Vn(r)}var pe=ne.ReactCurrentOwner,Or={key:!0,ref:!0,__self:!0,__source:!0},Kn,$n,Le;Le={};function Cr(r){if(fe.call(r,\"ref\")){var a=Object.getOwnPropertyDescriptor(r,\"ref\").get;if(a&&a.isReactWarning)return!1}return r.ref!==void 0}function Sr(r){if(fe.call(r,\"key\")){var a=Object.getOwnPropertyDescriptor(r,\"key\").get;if(a&&a.isReactWarning)return!1}return r.key!==void 0}function Ar(r,a){if(typeof r.ref==\"string\"&&pe.current&&a&&pe.current.stateNode!==a){var c=U(pe.current.type);Le[c]||(_('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',U(pe.current.type),r.ref),Le[c]=!0)}}function Ir(r,a){{var c=function(){Kn||(Kn=!0,_(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",a))};c.isReactWarning=!0,Object.defineProperty(r,\"key\",{get:c,configurable:!0})}}function jr(r,a){{var c=function(){$n||($n=!0,_(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",a))};c.isReactWarning=!0,Object.defineProperty(r,\"ref\",{get:c,configurable:!0})}}var Mr=function(r,a,c,p,g,T,y){var x={$$typeof:e,type:r,key:a,ref:c,props:y,_owner:T};return x._store={},Object.defineProperty(x._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(x,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:p}),Object.defineProperty(x,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:g}),Object.freeze&&(Object.freeze(x.props),Object.freeze(x)),x};function Lr(r,a,c,p,g){{var T,y={},x=null,z=null;c!==void 0&&(Bn(c),x=\"\"+c),Sr(a)&&(Bn(a.key),x=\"\"+a.key),Cr(a)&&(z=a.ref,Ar(a,g));for(T in a)fe.call(a,T)&&!Or.hasOwnProperty(T)&&(y[T]=a[T]);if(r&&r.defaultProps){var D=r.defaultProps;for(T in D)y[T]===void 0&&(y[T]=D[T])}if(x||z){var P=typeof r==\"function\"?r.displayName||r.name||\"Unknown\":r;x&&Ir(y,P),z&&jr(y,P)}return Mr(r,x,z,g,p,pe.current,y)}}var qe=ne.ReactCurrentOwner,Hn=ne.ReactDebugCurrentFrame;function ue(r){if(r){var a=r._owner,c=Pe(r.type,r._source,a?a.type:null);Hn.setExtraStackFrame(c)}else Hn.setExtraStackFrame(null)}var Fe;Fe=!1;function Ve(r){return typeof r==\"object\"&&r!==null&&r.$$typeof===e}function Gn(){{if(qe.current){var r=U(qe.current.type);if(r)return`\n\nCheck the render method of \\``+r+\"`.\"}return\"\"}}function qr(r){{if(r!==void 0){var a=r.fileName.replace(/^.*[\\\\\\/]/,\"\"),c=r.lineNumber;return`\n\nCheck your code at `+a+\":\"+c+\".\"}return\"\"}}var Yn={};function Fr(r){{var a=Gn();if(!a){var c=typeof r==\"string\"?r:r.displayName||r.name;c&&(a=`\n\nCheck the top-level render call using <`+c+\">.\")}return a}}function Xn(r,a){{if(!r._store||r._store.validated||r.key!=null)return;r._store.validated=!0;var c=Fr(a);if(Yn[c])return;Yn[c]=!0;var p=\"\";r&&r._owner&&r._owner!==qe.current&&(p=\" It was passed a child from \"+U(r._owner.type)+\".\"),ue(r),_('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',c,p),ue(null)}}function Qn(r,a){{if(typeof r!=\"object\")return;if(Me(r))for(var c=0;c<r.length;c++){var p=r[c];Ve(p)&&Xn(p,a)}else if(Ve(r))r._store&&(r._store.validated=!0);else if(r){var g=S(r);if(typeof g==\"function\"&&g!==r.entries)for(var T=g.call(r),y;!(y=T.next()).done;)Ve(y.value)&&Xn(y.value,a)}}}function Vr(r){{var a=r.type;if(a==null||typeof a==\"string\")return;var c;if(typeof a==\"function\")c=a.propTypes;else if(typeof a==\"object\"&&(a.$$typeof===u||a.$$typeof===E))c=a.propTypes;else return;if(c){var p=U(a);Rr(c,r.props,\"prop\",p,r)}else if(a.PropTypes!==void 0&&!Fe){Fe=!0;var g=U(a);_(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",g||\"Unknown\")}typeof a.getDefaultProps==\"function\"&&!a.getDefaultProps.isReactClassApproved&&_(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function Br(r){{for(var a=Object.keys(r.props),c=0;c<a.length;c++){var p=a[c];if(p!==\"children\"&&p!==\"key\"){ue(r),_(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",p),ue(null);break}}r.ref!==null&&(ue(r),_(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),ue(null))}}var Zn={};function Kr(r,a,c,p,g,T){{var y=xe(r);if(!y){var x=\"\";(r===void 0||typeof r==\"object\"&&r!==null&&Object.keys(r).length===0)&&(x+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var z=qr(g);z?x+=z:x+=Gn();var D;r===null?D=\"null\":Me(r)?D=\"array\":r!==void 0&&r.$$typeof===e?(D=\"<\"+(U(r.type)||\"Unknown\")+\" />\",x=\" Did you accidentally export a JSX literal instead of a component?\"):D=typeof r,_(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",D,x)}var P=Lr(r,a,c,g,T);if(P==null)return P;if(y){var I=a.children;if(I!==void 0)if(p)if(Me(I)){for(var ce=0;ce<I.length;ce++)Qn(I[ce],r);Object.freeze&&Object.freeze(I)}else _(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else Qn(I,r)}if(fe.call(a,\"key\")){var le=U(r),O=Object.keys(a).filter(function(Gr){return Gr!==\"key\"}),Be=O.length>0?\"{key: someKey, \"+O.join(\": ..., \")+\": ...}\":\"{key: someKey}\";if(!Zn[le+Be]){var Hr=O.length>0?\"{\"+O.join(\": ..., \")+\": ...}\":\"{}\";_(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,Be,le,Hr,le),Zn[le+Be]=!0}}return r===o?Br(P):Vr(P),P}}var $r=Kr;Ke.Fragment=o,Ke.jsxDEV=$r})()});var it=b((kl,rt)=>{\"use strict\";rt.exports=tt()});var Re=b(He=>{\"use strict\";He._=He._interop_require_default=ti;function ti(n){return n&&n.__esModule?n:{default:n}}});var Ye=b(Ge=>{\"use strict\";Object.defineProperty(Ge,\"__esModule\",{value:!0});function ri(n,e){for(var i in e)Object.defineProperty(n,i,{enumerable:!0,get:e[i]})}ri(Ge,{searchParamsToUrlQuery:function(){return ii},urlQueryToSearchParams:function(){return oi},assign:function(){return li}});function ii(n){let e={};return n.forEach((i,o)=>{typeof e[o]>\"u\"?e[o]=i:Array.isArray(e[o])?e[o].push(i):e[o]=[e[o],i]}),e}function ot(n){return typeof n==\"string\"||typeof n==\"number\"&&!isNaN(n)||typeof n==\"boolean\"?String(n):\"\"}function oi(n){let e=new URLSearchParams;return Object.entries(n).forEach(i=>{let[o,l]=i;Array.isArray(l)?l.forEach(s=>e.append(o,ot(s))):e.set(o,ot(l))}),e}function li(n){for(var e=arguments.length,i=new Array(e>1?e-1:0),o=1;o<e;o++)i[o-1]=arguments[o];return i.forEach(l=>{Array.from(l.keys()).forEach(s=>n.delete(s)),l.forEach((s,m)=>n.append(m,s))}),n}});var st=b(Xe=>{\"use strict\";function lt(n){if(typeof WeakMap!=\"function\")return null;var e=new WeakMap,i=new WeakMap;return(lt=function(o){return o?i:e})(n)}Xe._=Xe._interop_require_wildcard=si;function si(n,e){if(!e&&n&&n.__esModule)return n;if(n===null||typeof n!=\"object\"&&typeof n!=\"function\")return{default:n};var i=lt(e);if(i&&i.has(n))return i.get(n);var o={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in n)if(s!==\"default\"&&Object.prototype.hasOwnProperty.call(n,s)){var m=l?Object.getOwnPropertyDescriptor(n,s):null;m&&(m.get||m.set)?Object.defineProperty(o,s,m):o[s]=n[s]}return o.default=n,i&&i.set(n,o),o}});var Ze=b(Qe=>{\"use strict\";Object.defineProperty(Qe,\"__esModule\",{value:!0});function ai(n,e){for(var i in e)Object.defineProperty(n,i,{enumerable:!0,get:e[i]})}ai(Qe,{formatUrl:function(){return at},urlObjectKeys:function(){return ut},formatWithValidation:function(){return mi}});var ui=st(),ci=ui._(Ye()),di=/https?|ftp|gopher|file/;function at(n){let{auth:e,hostname:i}=n,o=n.protocol||\"\",l=n.pathname||\"\",s=n.hash||\"\",m=n.query||\"\",d=!1;e=e?encodeURIComponent(e).replace(/%3A/i,\":\")+\"@\":\"\",n.host?d=e+n.host:i&&(d=e+(~i.indexOf(\":\")?\"[\"+i+\"]\":i),n.port&&(d+=\":\"+n.port)),m&&typeof m==\"object\"&&(m=String(ci.urlQueryToSearchParams(m)));let u=n.search||m&&\"?\"+m||\"\";return o&&!o.endsWith(\":\")&&(o+=\":\"),n.slashes||(!o||di.test(o))&&d!==!1?(d=\"//\"+(d||\"\"),l&&l[0]!==\"/\"&&(l=\"/\"+l)):d||(d=\"\"),s&&s[0]!==\"#\"&&(s=\"#\"+s),u&&u[0]!==\"?\"&&(u=\"?\"+u),l=l.replace(/[?#]/g,encodeURIComponent),u=u.replace(\"#\",\"%23\"),\"\"+o+d+l+u+s}var ut=[\"auth\",\"hash\",\"host\",\"hostname\",\"href\",\"path\",\"pathname\",\"port\",\"protocol\",\"query\",\"search\",\"slashes\"];function mi(n){return n!==null&&typeof n==\"object\"&&Object.keys(n).forEach(e=>{ut.includes(e)||console.warn(\"Unknown key passed via urlObject into url.format: \"+e)}),at(n)}});var ct=b(Je=>{\"use strict\";Object.defineProperty(Je,\"__esModule\",{value:!0});Object.defineProperty(Je,\"omit\",{enumerable:!0,get:function(){return fi}});function fi(n,e){let i={};return Object.keys(n).forEach(o=>{e.includes(o)||(i[o]=n[o])}),i}});var be=b(ln=>{\"use strict\";Object.defineProperty(ln,\"__esModule\",{value:!0});function pi(n,e){for(var i in e)Object.defineProperty(n,i,{enumerable:!0,get:e[i]})}pi(ln,{WEB_VITALS:function(){return bi},execOnce:function(){return hi},isAbsoluteUrl:function(){return Ni},getLocationOrigin:function(){return dt},getURL:function(){return xi},getDisplayName:function(){return ze},isResSent:function(){return mt},normalizeRepeatedSlashes:function(){return yi},loadGetInitialProps:function(){return ft},SP:function(){return pt},ST:function(){return gi},DecodeError:function(){return en},NormalizeError:function(){return nn},PageNotFoundError:function(){return tn},MissingStaticPage:function(){return rn},MiddlewareNotFoundError:function(){return on},stringifyError:function(){return ki}});var bi=[\"CLS\",\"FCP\",\"FID\",\"INP\",\"LCP\",\"TTFB\"];function hi(n){let e=!1,i;return function(){for(var o=arguments.length,l=new Array(o),s=0;s<o;s++)l[s]=arguments[s];return e||(e=!0,i=n(...l)),i}}var _i=/^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/,Ni=n=>_i.test(n);function dt(){let{protocol:n,hostname:e,port:i}=window.location;return n+\"//\"+e+(i?\":\"+i:\"\")}function xi(){let{href:n}=window.location,e=dt();return n.substring(e.length)}function ze(n){return typeof n==\"string\"?n:n.displayName||n.name||\"Unknown\"}function mt(n){return n.finished||n.headersSent}function yi(n){let e=n.split(\"?\");return e[0].replace(/\\\\/g,\"/\").replace(/\\/\\/+/g,\"/\")+(e[1]?\"?\"+e.slice(1).join(\"?\"):\"\")}async function ft(n,e){var i;if((i=n.prototype)!=null&&i.getInitialProps){let s='\"'+ze(n)+'.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';throw new Error(s)}let o=e.res||e.ctx&&e.ctx.res;if(!n.getInitialProps)return e.ctx&&e.Component?{pageProps:await ft(e.Component,e.ctx)}:{};let l=await n.getInitialProps(e);if(o&&mt(o))return l;if(!l){let s='\"'+ze(n)+'.getInitialProps()\" should resolve to an object. But found \"'+l+'\" instead.';throw new Error(s)}return Object.keys(l).length===0&&!e.ctx&&console.warn(\"\"+ze(n)+\" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\"),l}var pt=typeof performance<\"u\",gi=pt&&[\"mark\",\"measure\",\"getEntriesByName\"].every(n=>typeof performance[n]==\"function\"),en=class extends Error{},nn=class extends Error{},tn=class extends Error{constructor(e){super(),this.code=\"ENOENT\",this.name=\"PageNotFoundError\",this.message=\"Cannot find module for page: \"+e}},rn=class extends Error{constructor(e,i){super(),this.message=\"Failed to load static file for page: \"+e+\" \"+i}},on=class extends Error{constructor(){super(),this.code=\"ENOENT\",this.message=\"Cannot find the middleware module\"}};function ki(n){return JSON.stringify({message:n.message,stack:n.stack})}});var an=b(sn=>{\"use strict\";Object.defineProperty(sn,\"__esModule\",{value:!0});Object.defineProperty(sn,\"removeTrailingSlash\",{enumerable:!0,get:function(){return Ti}});function Ti(n){return n.replace(/\\/$/,\"\")||\"/\"}});var Ee=b(un=>{\"use strict\";Object.defineProperty(un,\"__esModule\",{value:!0});Object.defineProperty(un,\"parsePath\",{enumerable:!0,get:function(){return vi}});function vi(n){let e=n.indexOf(\"#\"),i=n.indexOf(\"?\"),o=i>-1&&(e<0||i<e);return o||e>-1?{pathname:n.substring(0,o?i:e),query:o?n.substring(i,e>-1?e:void 0):\"\",hash:e>-1?n.slice(e):\"\"}:{pathname:n,query:\"\",hash:\"\"}}});var he=b((q,ht)=>{\"use strict\";Object.defineProperty(q,\"__esModule\",{value:!0});Object.defineProperty(q,\"normalizePathTrailingSlash\",{enumerable:!0,get:function(){return Pi}});var bt=an(),Di=Ee(),Pi=n=>{if(!n.startsWith(\"/\")||process.env.__NEXT_MANUAL_TRAILING_SLASH)return n;let{pathname:e,query:i,hash:o}=(0,Di.parsePath)(n);return process.env.__NEXT_TRAILING_SLASH?/\\.[^/]+\\/?$/.test(e)?\"\"+(0,bt.removeTrailingSlash)(e)+i+o:e.endsWith(\"/\")?\"\"+e+i+o:e+\"/\"+i+o:\"\"+(0,bt.removeTrailingSlash)(e)+i+o};(typeof q.default==\"function\"||typeof q.default==\"object\"&&q.default!==null)&&typeof q.default.__esModule>\"u\"&&(Object.defineProperty(q.default,\"__esModule\",{value:!0}),Object.assign(q.default,q),ht.exports=q.default)});var dn=b(cn=>{\"use strict\";Object.defineProperty(cn,\"__esModule\",{value:!0});Object.defineProperty(cn,\"pathHasPrefix\",{enumerable:!0,get:function(){return Ui}});var Wi=Ee();function Ui(n,e){if(typeof n!=\"string\")return!1;let{pathname:i}=(0,Wi.parsePath)(n);return i===e||i.startsWith(e+\"/\")}});var Nt=b((F,_t)=>{\"use strict\";Object.defineProperty(F,\"__esModule\",{value:!0});Object.defineProperty(F,\"hasBasePath\",{enumerable:!0,get:function(){return Ei}});var Ri=dn(),zi=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Ei(n){return(0,Ri.pathHasPrefix)(n,zi)}(typeof F.default==\"function\"||typeof F.default==\"object\"&&F.default!==null)&&typeof F.default.__esModule>\"u\"&&(Object.defineProperty(F.default,\"__esModule\",{value:!0}),Object.assign(F.default,F),_t.exports=F.default)});var fn=b(mn=>{\"use strict\";Object.defineProperty(mn,\"__esModule\",{value:!0});Object.defineProperty(mn,\"isLocalURL\",{enumerable:!0,get:function(){return Oi}});var xt=be(),wi=Nt();function Oi(n){if(!(0,xt.isAbsoluteUrl)(n))return!0;try{let e=(0,xt.getLocationOrigin)(),i=new URL(n,e);return i.origin===e&&(0,wi.hasBasePath)(i.pathname)}catch{return!1}}});var yt=b(bn=>{\"use strict\";Object.defineProperty(bn,\"__esModule\",{value:!0});Object.defineProperty(bn,\"getSortedRoutes\",{enumerable:!0,get:function(){return Ci}});var pn=class n{insert(e){this._insert(e.split(\"/\").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){e===void 0&&(e=\"/\");let i=[...this.children.keys()].sort();this.slugName!==null&&i.splice(i.indexOf(\"[]\"),1),this.restSlugName!==null&&i.splice(i.indexOf(\"[...]\"),1),this.optionalRestSlugName!==null&&i.splice(i.indexOf(\"[[...]]\"),1);let o=i.map(l=>this.children.get(l)._smoosh(\"\"+e+l+\"/\")).reduce((l,s)=>[...l,...s],[]);if(this.slugName!==null&&o.push(...this.children.get(\"[]\")._smoosh(e+\"[\"+this.slugName+\"]/\")),!this.placeholder){let l=e===\"/\"?\"/\":e.slice(0,-1);if(this.optionalRestSlugName!=null)throw new Error('You cannot define a route with the same specificity as a optional catch-all route (\"'+l+'\" and \"'+l+\"[[...\"+this.optionalRestSlugName+']]\").');o.unshift(l)}return this.restSlugName!==null&&o.push(...this.children.get(\"[...]\")._smoosh(e+\"[...\"+this.restSlugName+\"]/\")),this.optionalRestSlugName!==null&&o.push(...this.children.get(\"[[...]]\")._smoosh(e+\"[[...\"+this.optionalRestSlugName+\"]]/\")),o}_insert(e,i,o){if(e.length===0){this.placeholder=!1;return}if(o)throw new Error(\"Catch-all must be the last part of the URL.\");let l=e[0];if(l.startsWith(\"[\")&&l.endsWith(\"]\")){let d=function(u,f){if(u!==null&&u!==f)throw new Error(\"You cannot use different slug names for the same dynamic path ('\"+u+\"' !== '\"+f+\"').\");i.forEach(k=>{if(k===f)throw new Error('You cannot have the same slug name \"'+f+'\" repeat within a single dynamic path');if(k.replace(/\\W/g,\"\")===l.replace(/\\W/g,\"\"))throw new Error('You cannot have the slug names \"'+k+'\" and \"'+f+'\" differ only by non-word symbols within a single dynamic path')}),i.push(f)},s=l.slice(1,-1),m=!1;if(s.startsWith(\"[\")&&s.endsWith(\"]\")&&(s=s.slice(1,-1),m=!0),s.startsWith(\"...\")&&(s=s.substring(3),o=!0),s.startsWith(\"[\")||s.endsWith(\"]\"))throw new Error(\"Segment names may not start or end with extra brackets ('\"+s+\"').\");if(s.startsWith(\".\"))throw new Error(\"Segment names may not start with erroneous periods ('\"+s+\"').\");if(o)if(m){if(this.restSlugName!=null)throw new Error('You cannot use both an required and optional catch-all route at the same level (\"[...'+this.restSlugName+']\" and \"'+e[0]+'\" ).');d(this.optionalRestSlugName,s),this.optionalRestSlugName=s,l=\"[[...]]\"}else{if(this.optionalRestSlugName!=null)throw new Error('You cannot use both an optional and required catch-all route at the same level (\"[[...'+this.optionalRestSlugName+']]\" and \"'+e[0]+'\").');d(this.restSlugName,s),this.restSlugName=s,l=\"[...]\"}else{if(m)throw new Error('Optional route parameters are not yet supported (\"'+e[0]+'\").');d(this.slugName,s),this.slugName=s,l=\"[]\"}}this.children.has(l)||this.children.set(l,new n),this.children.get(l)._insert(e.slice(1),i,o)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}};function Ci(n){let e=new pn;return n.forEach(i=>e.insert(i)),e.smoosh()}});var gt=b(hn=>{\"use strict\";Object.defineProperty(hn,\"__esModule\",{value:!0});Object.defineProperty(hn,\"isDynamicRoute\",{enumerable:!0,get:function(){return Ai}});var Si=/\\/\\[[^/]+?\\](?=\\/|$)/;function Ai(n){return Si.test(n)}});var kt=b(_n=>{\"use strict\";Object.defineProperty(_n,\"__esModule\",{value:!0});function Ii(n,e){for(var i in e)Object.defineProperty(n,i,{enumerable:!0,get:e[i]})}Ii(_n,{getSortedRoutes:function(){return ji.getSortedRoutes},isDynamicRoute:function(){return Mi.isDynamicRoute}});var ji=yt(),Mi=gt()});var Tt=b(Nn=>{\"use strict\";Object.defineProperty(Nn,\"__esModule\",{value:!0});Object.defineProperty(Nn,\"getRouteMatcher\",{enumerable:!0,get:function(){return qi}});var Li=be();function qi(n){let{re:e,groups:i}=n;return o=>{let l=e.exec(o);if(!l)return!1;let s=d=>{try{return decodeURIComponent(d)}catch{throw new Li.DecodeError(\"failed to decode param\")}},m={};return Object.keys(i).forEach(d=>{let u=i[d],f=l[u.pos];f!==void 0&&(m[d]=~f.indexOf(\"/\")?f.split(\"/\").map(k=>s(k)):u.repeat?[s(f)]:s(f))}),m}}});var vt=b(xn=>{\"use strict\";Object.defineProperty(xn,\"__esModule\",{value:!0});Object.defineProperty(xn,\"ensureLeadingSlash\",{enumerable:!0,get:function(){return Fi}});function Fi(n){return n.startsWith(\"/\")?n:\"/\"+n}});var Dt=b(yn=>{\"use strict\";Object.defineProperty(yn,\"__esModule\",{value:!0});Object.defineProperty(yn,\"isGroupSegment\",{enumerable:!0,get:function(){return Vi}});function Vi(n){return n[0]===\"(\"&&n.endsWith(\")\")}});var Pt=b(gn=>{\"use strict\";Object.defineProperty(gn,\"__esModule\",{value:!0});function Bi(n,e){for(var i in e)Object.defineProperty(n,i,{enumerable:!0,get:e[i]})}Bi(gn,{normalizeAppPath:function(){return Hi},normalizeRscPath:function(){return Gi}});var Ki=vt(),$i=Dt();function Hi(n){return(0,Ki.ensureLeadingSlash)(n.split(\"/\").reduce((e,i,o,l)=>!i||(0,$i.isGroupSegment)(i)||i[0]===\"@\"||(i===\"page\"||i===\"route\")&&o===l.length-1?e:e+\"/\"+i,\"\"))}function Gi(n,e){return e?n.replace(/\\.rsc($|\\?)/,\"$1\"):n}});var Wt=b(Tn=>{\"use strict\";Object.defineProperty(Tn,\"__esModule\",{value:!0});function Yi(n,e){for(var i in e)Object.defineProperty(n,i,{enumerable:!0,get:e[i]})}Yi(Tn,{INTERCEPTION_ROUTE_MARKERS:function(){return kn},isInterceptionRouteAppPath:function(){return Qi},extractInterceptionRouteInformation:function(){return Zi}});var Xi=Pt(),kn=[\"(..)(..)\",\"(.)\",\"(..)\",\"(...)\"];function Qi(n){return n.split(\"/\").find(e=>kn.find(i=>e.startsWith(i)))!==void 0}function Zi(n){let e,i,o;for(let l of n.split(\"/\"))if(i=kn.find(s=>l.startsWith(s)),i){[e,o]=n.split(i,2);break}if(!e||!i||!o)throw new Error(`Invalid interception route: ${n}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(e=(0,Xi.normalizeAppPath)(e),i){case\"(.)\":e===\"/\"?o=`/${o}`:o=e+\"/\"+o;break;case\"(..)\":if(e===\"/\")throw new Error(`Invalid interception route: ${n}. Cannot use (..) marker at the root level, use (.) instead.`);o=e.split(\"/\").slice(0,-1).concat(o).join(\"/\");break;case\"(...)\":o=\"/\"+o;break;case\"(..)(..)\":let l=e.split(\"/\");if(l.length<=2)throw new Error(`Invalid interception route: ${n}. Cannot use (..)(..) marker at the root level or one level up.`);o=l.slice(0,-2).concat(o).join(\"/\");break;default:throw new Error(\"Invariant: unexpected marker\")}return{interceptingRoute:e,interceptedRoute:o}}});var Ut=b(vn=>{\"use strict\";Object.defineProperty(vn,\"__esModule\",{value:!0});Object.defineProperty(vn,\"escapeStringRegexp\",{enumerable:!0,get:function(){return no}});var Ji=/[|\\\\{}()[\\]^$+*?.-]/,eo=/[|\\\\{}()[\\]^$+*?.-]/g;function no(n){return Ji.test(n)?n.replace(eo,\"\\\\$&\"):n}});var St=b(Wn=>{\"use strict\";Object.defineProperty(Wn,\"__esModule\",{value:!0});function to(n,e){for(var i in e)Object.defineProperty(n,i,{enumerable:!0,get:e[i]})}to(Wn,{getRouteRegex:function(){return Ot},getNamedRouteRegex:function(){return lo},getNamedMiddlewareRegex:function(){return so}});var zt=Wt(),Dn=Ut(),Et=an(),ro=\"nxtP\",io=\"nxtI\";function Pn(n){let e=n.startsWith(\"[\")&&n.endsWith(\"]\");e&&(n=n.slice(1,-1));let i=n.startsWith(\"...\");return i&&(n=n.slice(3)),{key:n,repeat:i,optional:e}}function wt(n){let e=(0,Et.removeTrailingSlash)(n).slice(1).split(\"/\"),i={},o=1;return{parameterizedRoute:e.map(l=>{let s=zt.INTERCEPTION_ROUTE_MARKERS.find(d=>l.startsWith(d)),m=l.match(/\\[((?:\\[.*\\])|.+)\\]/);if(s&&m){let{key:d,optional:u,repeat:f}=Pn(m[1]);return i[d]={pos:o++,repeat:f,optional:u},\"/\"+(0,Dn.escapeStringRegexp)(s)+\"([^/]+?)\"}else if(m){let{key:d,repeat:u,optional:f}=Pn(m[1]);return i[d]={pos:o++,repeat:u,optional:f},u?f?\"(?:/(.+?))?\":\"/(.+?)\":\"/([^/]+?)\"}else return\"/\"+(0,Dn.escapeStringRegexp)(l)}).join(\"\"),groups:i}}function Ot(n){let{parameterizedRoute:e,groups:i}=wt(n);return{re:new RegExp(\"^\"+e+\"(?:/)?$\"),groups:i}}function oo(){let n=0;return()=>{let e=\"\",i=++n;for(;i>0;)e+=String.fromCharCode(97+(i-1)%26),i=Math.floor((i-1)/26);return e}}function Rt(n){let{getSafeRouteKey:e,segment:i,routeKeys:o,keyPrefix:l}=n,{key:s,optional:m,repeat:d}=Pn(i),u=s.replace(/\\W/g,\"\");l&&(u=\"\"+l+u);let f=!1;return(u.length===0||u.length>30)&&(f=!0),isNaN(parseInt(u.slice(0,1)))||(f=!0),f&&(u=e()),l?o[u]=\"\"+l+s:o[u]=\"\"+s,d?m?\"(?:/(?<\"+u+\">.+?))?\":\"/(?<\"+u+\">.+?)\":\"/(?<\"+u+\">[^/]+?)\"}function Ct(n,e){let i=(0,Et.removeTrailingSlash)(n).slice(1).split(\"/\"),o=oo(),l={};return{namedParameterizedRoute:i.map(s=>{let m=zt.INTERCEPTION_ROUTE_MARKERS.some(u=>s.startsWith(u)),d=s.match(/\\[((?:\\[.*\\])|.+)\\]/);return m&&d?Rt({getSafeRouteKey:o,segment:d[1],routeKeys:l,keyPrefix:e?io:void 0}):d?Rt({getSafeRouteKey:o,segment:d[1],routeKeys:l,keyPrefix:e?ro:void 0}):\"/\"+(0,Dn.escapeStringRegexp)(s)}).join(\"\"),routeKeys:l}}function lo(n,e){let i=Ct(n,e);return{...Ot(n),namedRegex:\"^\"+i.namedParameterizedRoute+\"(?:/)?$\",routeKeys:i.routeKeys}}function so(n,e){let{parameterizedRoute:i}=wt(n),{catchAll:o=!0}=e;if(i===\"/\")return{namedRegex:\"^/\"+(o?\".*\":\"\")+\"$\"};let{namedParameterizedRoute:l}=Ct(n,!1),s=o?\"(?:(/.*)?)\":\"\";return{namedRegex:\"^\"+l+s+\"$\"}}});var At=b(Un=>{\"use strict\";Object.defineProperty(Un,\"__esModule\",{value:!0});Object.defineProperty(Un,\"interpolateAs\",{enumerable:!0,get:function(){return co}});var ao=Tt(),uo=St();function co(n,e,i){let o=\"\",l=(0,uo.getRouteRegex)(n),s=l.groups,m=(e!==n?(0,ao.getRouteMatcher)(l)(e):\"\")||i;o=n;let d=Object.keys(s);return d.every(u=>{let f=m[u]||\"\",{repeat:k,optional:E}=s[u],v=\"[\"+(k?\"...\":\"\")+u+\"]\";return E&&(v=(f?\"\":\"/\")+\"[\"+v+\"]\"),k&&!Array.isArray(f)&&(f=[f]),(E||u in m)&&(o=o.replace(v,k?f.map(A=>encodeURIComponent(A)).join(\"/\"):encodeURIComponent(f))||\"/\")})||(o=\"\"),{params:d,result:o}}});var Mt=b((V,jt)=>{\"use strict\";Object.defineProperty(V,\"__esModule\",{value:!0});Object.defineProperty(V,\"resolveHref\",{enumerable:!0,get:function(){return xo}});var mo=Ye(),It=Ze(),fo=ct(),po=be(),bo=he(),ho=fn(),_o=kt(),No=At();function xo(n,e,i){let o,l=typeof e==\"string\"?e:(0,It.formatWithValidation)(e),s=l.match(/^[a-zA-Z]{1,}:\\/\\//),m=s?l.slice(s[0].length):l;if((m.split(\"?\")[0]||\"\").match(/(\\/\\/|\\\\)/)){console.error(\"Invalid href '\"+l+\"' passed to next/router in page: '\"+n.pathname+\"'. Repeated forward-slashes (//) or backslashes \\\\ are not valid in the href.\");let u=(0,po.normalizeRepeatedSlashes)(m);l=(s?s[0]:\"\")+u}if(!(0,ho.isLocalURL)(l))return i?[l]:l;try{o=new URL(l.startsWith(\"#\")?n.asPath:n.pathname,\"http://n\")}catch{o=new URL(\"/\",\"http://n\")}try{let u=new URL(l,o);u.pathname=(0,bo.normalizePathTrailingSlash)(u.pathname);let f=\"\";if((0,_o.isDynamicRoute)(u.pathname)&&u.searchParams&&i){let E=(0,mo.searchParamsToUrlQuery)(u.searchParams),{result:v,params:A}=(0,No.interpolateAs)(u.pathname,u.pathname,E);v&&(f=(0,It.formatWithValidation)({pathname:v,hash:u.hash,query:(0,fo.omit)(E,A)}))}let k=u.origin===o.origin?u.href.slice(u.origin.length):u.href;return i?[k,f||k]:k}catch{return i?[l]:l}}(typeof V.default==\"function\"||typeof V.default==\"object\"&&V.default!==null)&&typeof V.default.__esModule>\"u\"&&(Object.defineProperty(V.default,\"__esModule\",{value:!0}),Object.assign(V.default,V),jt.exports=V.default)});var zn=b(Rn=>{\"use strict\";Object.defineProperty(Rn,\"__esModule\",{value:!0});Object.defineProperty(Rn,\"addPathPrefix\",{enumerable:!0,get:function(){return go}});var yo=Ee();function go(n,e){if(!n.startsWith(\"/\")||!e)return n;let{pathname:i,query:o,hash:l}=(0,yo.parsePath)(n);return\"\"+e+i+o+l}});var qt=b(En=>{\"use strict\";Object.defineProperty(En,\"__esModule\",{value:!0});Object.defineProperty(En,\"addLocale\",{enumerable:!0,get:function(){return To}});var ko=zn(),Lt=dn();function To(n,e,i,o){if(!e||e===i)return n;let l=n.toLowerCase();return!o&&((0,Lt.pathHasPrefix)(l,\"/api\")||(0,Lt.pathHasPrefix)(l,\"/\"+e.toLowerCase()))?n:(0,ko.addPathPrefix)(n,\"/\"+e)}});var Vt=b((B,Ft)=>{\"use strict\";Object.defineProperty(B,\"__esModule\",{value:!0});Object.defineProperty(B,\"addLocale\",{enumerable:!0,get:function(){return Do}});var vo=he(),Do=function(n){for(var e=arguments.length,i=new Array(e>1?e-1:0),o=1;o<e;o++)i[o-1]=arguments[o];return process.env.__NEXT_I18N_SUPPORT?(0,vo.normalizePathTrailingSlash)(qt().addLocale(n,...i)):n};(typeof B.default==\"function\"||typeof B.default==\"object\"&&B.default!==null)&&typeof B.default.__esModule>\"u\"&&(Object.defineProperty(B.default,\"__esModule\",{value:!0}),Object.assign(B.default,B),Ft.exports=B.default)});var Kt=b(wn=>{\"use strict\";Object.defineProperty(wn,\"__esModule\",{value:!0});Object.defineProperty(wn,\"RouterContext\",{enumerable:!0,get:function(){return Bt}});var Po=Re(),Wo=Po._(de()),Bt=Wo.default.createContext(null);Bt.displayName=\"RouterContext\"});var Xt=b(Cn=>{\"use client\";\"use strict\";Object.defineProperty(Cn,\"__esModule\",{value:!0});function Uo(n,e){for(var i in e)Object.defineProperty(n,i,{enumerable:!0,get:e[i]})}Uo(Cn,{CacheStates:function(){return On},AppRouterContext:function(){return $t},LayoutRouterContext:function(){return Ht},GlobalLayoutRouterContext:function(){return Gt},TemplateContext:function(){return Yt}});var Ro=Re(),we=Ro._(de()),On;(function(n){n.LAZY_INITIALIZED=\"LAZYINITIALIZED\",n.DATA_FETCH=\"DATAFETCH\",n.READY=\"READY\"})(On||(On={}));var $t=we.default.createContext(null),Ht=we.default.createContext(null),Gt=we.default.createContext(null),Yt=we.default.createContext(null);$t.displayName=\"AppRouterContext\",Ht.displayName=\"LayoutRouterContext\",Gt.displayName=\"GlobalLayoutRouterContext\",Yt.displayName=\"TemplateContext\"});var Zt=b((K,Qt)=>{\"use strict\";Object.defineProperty(K,\"__esModule\",{value:!0});function zo(n,e){for(var i in e)Object.defineProperty(n,i,{enumerable:!0,get:e[i]})}zo(K,{requestIdleCallback:function(){return Eo},cancelIdleCallback:function(){return wo}});var Eo=typeof self<\"u\"&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(n){let e=Date.now();return self.setTimeout(function(){n({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})},1)},wo=typeof self<\"u\"&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(n){return clearTimeout(n)};(typeof K.default==\"function\"||typeof K.default==\"object\"&&K.default!==null)&&typeof K.default.__esModule>\"u\"&&(Object.defineProperty(K.default,\"__esModule\",{value:!0}),Object.assign(K.default,K),Qt.exports=K.default)});var tr=b(($,nr)=>{\"use strict\";Object.defineProperty($,\"__esModule\",{value:!0});Object.defineProperty($,\"useIntersection\",{enumerable:!0,get:function(){return So}});var _e=de(),Jt=Zt(),er=typeof IntersectionObserver==\"function\",Sn=new Map,Oe=[];function Oo(n){let e={root:n.root||null,margin:n.rootMargin||\"\"},i=Oe.find(m=>m.root===e.root&&m.margin===e.margin),o;if(i&&(o=Sn.get(i),o))return o;let l=new Map,s=new IntersectionObserver(m=>{m.forEach(d=>{let u=l.get(d.target),f=d.isIntersecting||d.intersectionRatio>0;u&&f&&u(f)})},n);return o={id:e,observer:s,elements:l},Oe.push(e),Sn.set(e,o),o}function Co(n,e,i){let{id:o,observer:l,elements:s}=Oo(i);return s.set(n,e),l.observe(n),function(){if(s.delete(n),l.unobserve(n),s.size===0){l.disconnect(),Sn.delete(o);let d=Oe.findIndex(u=>u.root===o.root&&u.margin===o.margin);d>-1&&Oe.splice(d,1)}}}function So(n){let{rootRef:e,rootMargin:i,disabled:o}=n,l=o||!er,[s,m]=(0,_e.useState)(!1),d=(0,_e.useRef)(null),u=(0,_e.useCallback)(k=>{d.current=k},[]);(0,_e.useEffect)(()=>{if(er){if(l||s)return;let k=d.current;if(k&&k.tagName)return Co(k,v=>v&&m(v),{root:e?.current,rootMargin:i})}else if(!s){let k=(0,Jt.requestIdleCallback)(()=>m(!0));return()=>(0,Jt.cancelIdleCallback)(k)}},[l,i,e,s,d.current]);let f=(0,_e.useCallback)(()=>{m(!1)},[]);return[u,s,f]}(typeof $.default==\"function\"||typeof $.default==\"object\"&&$.default!==null)&&typeof $.default.__esModule>\"u\"&&(Object.defineProperty($.default,\"__esModule\",{value:!0}),Object.assign($.default,$),nr.exports=$.default)});var rr=b(An=>{\"use strict\";Object.defineProperty(An,\"__esModule\",{value:!0});Object.defineProperty(An,\"normalizeLocalePath\",{enumerable:!0,get:function(){return Ao}});function Ao(n,e){let i,o=n.split(\"/\");return(e||[]).some(l=>o[1]&&o[1].toLowerCase()===l.toLowerCase()?(i=l,o.splice(1,1),n=o.join(\"/\")||\"/\",!0):!1),{pathname:n,detectedLocale:i}}});var or=b((H,ir)=>{\"use strict\";Object.defineProperty(H,\"__esModule\",{value:!0});Object.defineProperty(H,\"normalizeLocalePath\",{enumerable:!0,get:function(){return Io}});var Io=(n,e)=>process.env.__NEXT_I18N_SUPPORT?rr().normalizeLocalePath(n,e):{pathname:n,detectedLocale:void 0};(typeof H.default==\"function\"||typeof H.default==\"object\"&&H.default!==null)&&typeof H.default.__esModule>\"u\"&&(Object.defineProperty(H.default,\"__esModule\",{value:!0}),Object.assign(H.default,H),ir.exports=H.default)});var lr=b(In=>{\"use strict\";Object.defineProperty(In,\"__esModule\",{value:!0});Object.defineProperty(In,\"detectDomainLocale\",{enumerable:!0,get:function(){return jo}});function jo(n,e,i){if(n){i&&(i=i.toLowerCase());for(let s of n){var o,l;let m=(o=s.domain)==null?void 0:o.split(\":\")[0].toLowerCase();if(e===m||i===s.defaultLocale.toLowerCase()||(l=s.locales)!=null&&l.some(d=>d.toLowerCase()===i))return s}}}});var ar=b((G,sr)=>{\"use strict\";Object.defineProperty(G,\"__esModule\",{value:!0});Object.defineProperty(G,\"detectDomainLocale\",{enumerable:!0,get:function(){return Mo}});var Mo=function(){for(var n=arguments.length,e=new Array(n),i=0;i<n;i++)e[i]=arguments[i];if(process.env.__NEXT_I18N_SUPPORT)return lr().detectDomainLocale(...e)};(typeof G.default==\"function\"||typeof G.default==\"object\"&&G.default!==null)&&typeof G.default.__esModule>\"u\"&&(Object.defineProperty(G.default,\"__esModule\",{value:!0}),Object.assign(G.default,G),sr.exports=G.default)});var cr=b((Y,ur)=>{\"use strict\";Object.defineProperty(Y,\"__esModule\",{value:!0});Object.defineProperty(Y,\"getDomainLocale\",{enumerable:!0,get:function(){return Fo}});var Lo=he(),qo=process.env.__NEXT_ROUTER_BASEPATH||\"\";function Fo(n,e,i,o){if(process.env.__NEXT_I18N_SUPPORT){let l=or().normalizeLocalePath,s=ar().detectDomainLocale,m=e||l(n,i).detectedLocale,d=s(o,void 0,m);if(d){let u=\"http\"+(d.http?\"\":\"s\")+\"://\",f=m===d.defaultLocale?\"\":\"/\"+m;return\"\"+u+d.domain+(0,Lo.normalizePathTrailingSlash)(\"\"+qo+f+n)}return!1}else return!1}(typeof Y.default==\"function\"||typeof Y.default==\"object\"&&Y.default!==null)&&typeof Y.default.__esModule>\"u\"&&(Object.defineProperty(Y.default,\"__esModule\",{value:!0}),Object.assign(Y.default,Y),ur.exports=Y.default)});var mr=b((X,dr)=>{\"use strict\";Object.defineProperty(X,\"__esModule\",{value:!0});Object.defineProperty(X,\"addBasePath\",{enumerable:!0,get:function(){return $o}});var Vo=zn(),Bo=he(),Ko=process.env.__NEXT_ROUTER_BASEPATH||\"\";function $o(n,e){return(0,Bo.normalizePathTrailingSlash)(process.env.__NEXT_MANUAL_CLIENT_BASE_PATH&&!e?n:(0,Vo.addPathPrefix)(n,Ko))}(typeof X.default==\"function\"||typeof X.default==\"object\"&&X.default!==null)&&typeof X.default.__esModule>\"u\"&&(Object.defineProperty(X.default,\"__esModule\",{value:!0}),Object.assign(X.default,X),dr.exports=X.default)});var pr=b((Q,fr)=>{\"use strict\";Object.defineProperty(Q,\"__esModule\",{value:!0});function Ho(n,e){for(var i in e)Object.defineProperty(n,i,{enumerable:!0,get:e[i]})}Ho(Q,{PrefetchKind:function(){return jn},ACTION_REFRESH:function(){return Go},ACTION_NAVIGATE:function(){return Yo},ACTION_RESTORE:function(){return Xo},ACTION_SERVER_PATCH:function(){return Qo},ACTION_PREFETCH:function(){return Zo},ACTION_FAST_REFRESH:function(){return Jo},ACTION_SERVER_ACTION:function(){return el}});var Go=\"refresh\",Yo=\"navigate\",Xo=\"restore\",Qo=\"server-patch\",Zo=\"prefetch\",Jo=\"fast-refresh\",el=\"server-action\",jn;(function(n){n.AUTO=\"auto\",n.FULL=\"full\",n.TEMPORARY=\"temporary\"})(jn||(jn={}));(typeof Q.default==\"function\"||typeof Q.default==\"object\"&&Q.default!==null)&&typeof Q.default.__esModule>\"u\"&&(Object.defineProperty(Q.default,\"__esModule\",{value:!0}),Object.assign(Q.default,Q),fr.exports=Q.default)});var gr=b((Z,yr)=>{\"use client\";\"use strict\";Object.defineProperty(Z,\"__esModule\",{value:!0});Object.defineProperty(Z,\"default\",{enumerable:!0,get:function(){return fl}});var nl=Re(),C=nl._(de()),br=Mt(),xr=fn(),tl=Ze(),rl=be(),il=Vt(),ol=Kt(),ll=Xt(),sl=tr(),al=cr(),ul=mr(),hr=pr(),_r=new Set;function Mn(n,e,i,o,l,s){if(typeof window>\"u\"||!s&&!(0,xr.isLocalURL)(e))return;if(!o.bypassPrefetchedCheck){let d=typeof o.locale<\"u\"?o.locale:\"locale\"in n?n.locale:void 0,u=e+\"%\"+i+\"%\"+d;if(_r.has(u))return;_r.add(u)}let m=s?n.prefetch(e,l):n.prefetch(e,i,o);Promise.resolve(m).catch(d=>{throw d})}function cl(n){let i=n.currentTarget.getAttribute(\"target\");return i&&i!==\"_self\"||n.metaKey||n.ctrlKey||n.shiftKey||n.altKey||n.nativeEvent&&n.nativeEvent.which===2}function dl(n,e,i,o,l,s,m,d,u,f){let{nodeName:k}=n.currentTarget;if(k.toUpperCase()===\"A\"&&(cl(n)||!u&&!(0,xr.isLocalURL)(i)))return;n.preventDefault();let v=()=>{let A=m??!0;\"beforePopState\"in e?e[l?\"replace\":\"push\"](i,o,{shallow:s,locale:d,scroll:A}):e[l?\"replace\":\"push\"](o||i,{forceOptimisticNavigation:!f,scroll:A})};u?C.default.startTransition(v):v()}function Nr(n){return typeof n==\"string\"?n:(0,tl.formatUrl)(n)}var ml=C.default.forwardRef(function(e,i){let o,{href:l,as:s,children:m,prefetch:d=null,passHref:u,replace:f,shallow:k,scroll:E,locale:v,onClick:A,onMouseEnter:se,onTouchStart:Ne,legacyBehavior:S=!1,...ne}=e;o=m,S&&(typeof o==\"string\"||typeof o==\"number\")&&(o=C.default.createElement(\"a\",null,o));let _=C.default.useContext(ol.RouterContext),Se=C.default.useContext(ll.AppRouterContext),j=_??Se,M=!_,ie=d!==!1,ae=d===null?hr.PrefetchKind.AUTO:hr.PrefetchKind.FULL;{let h=function(N){return new Error(\"Failed prop type: The prop `\"+N.key+\"` expects a \"+N.expected+\" in `<Link>`, but got `\"+N.actual+\"` instead.\"+(typeof window<\"u\"?`\nOpen your browser's console to view the Component stack trace.`:\"\"))};Object.keys({href:!0}).forEach(N=>{if(N===\"href\"){if(e[N]==null||typeof e[N]!=\"string\"&&typeof e[N]!=\"object\")throw h({key:N,expected:\"`string` or `object`\",actual:e[N]===null?\"null\":typeof e[N]})}else{let w=N}}),Object.keys({as:!0,replace:!0,scroll:!0,shallow:!0,passHref:!0,prefetch:!0,locale:!0,onClick:!0,onMouseEnter:!0,onTouchStart:!0,legacyBehavior:!0}).forEach(N=>{let w=typeof e[N];if(N===\"as\"){if(e[N]&&w!==\"string\"&&w!==\"object\")throw h({key:N,expected:\"`string` or `object`\",actual:w})}else if(N===\"locale\"){if(e[N]&&w!==\"string\")throw h({key:N,expected:\"`string`\",actual:w})}else if(N===\"onClick\"||N===\"onMouseEnter\"||N===\"onTouchStart\"){if(e[N]&&w!==\"function\")throw h({key:N,expected:\"`function`\",actual:w})}else if(N===\"replace\"||N===\"scroll\"||N===\"shallow\"||N===\"passHref\"||N===\"prefetch\"||N===\"legacyBehavior\"){if(e[N]!=null&&w!==\"boolean\")throw h({key:N,expected:\"`boolean`\",actual:w})}else{let Te=N}});let ke=C.default.useRef(!1);e.prefetch&&!ke.current&&!M&&(ke.current=!0,console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\"))}if(M&&!s){let h;if(typeof l==\"string\"?h=l:typeof l==\"object\"&&typeof l.pathname==\"string\"&&(h=l.pathname),h&&h.split(\"/\").some(ee=>ee.startsWith(\"[\")&&ee.endsWith(\"]\")))throw new Error(\"Dynamic href `\"+h+\"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\")}let{href:L,as:R}=C.default.useMemo(()=>{if(!_){let ee=Nr(l);return{href:ee,as:s?Nr(s):ee}}let[h,re]=(0,br.resolveHref)(_,l,!0);return{href:h,as:s?(0,br.resolveHref)(_,s):re||h}},[_,l,s]),xe=C.default.useRef(L),ye=C.default.useRef(R),W;if(S){A&&console.warn('\"onClick\" was passed to <Link> with `href` of `'+l+'` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link'),se&&console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `'+l+'` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');try{W=C.default.Children.only(o)}catch{throw o?new Error(\"Multiple children were passed to <Link> with `href` of `\"+l+\"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\"+(typeof window<\"u\"?` \nOpen your browser's console to view the Component stack trace.`:\"\")):new Error(\"No children were passed to <Link> with `href` of `\"+l+\"` but one child is required https://nextjs.org/docs/messages/link-no-children\")}}else if(o?.type===\"a\")throw new Error(`Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor`);let U=S?W&&typeof W==\"object\"&&W.ref:i,[J,te,me]=(0,sl.useIntersection)({rootMargin:\"200px\"}),ge=C.default.useCallback(h=>{(ye.current!==R||xe.current!==L)&&(me(),ye.current=R,xe.current=L),J(h),U&&(typeof U==\"function\"?U(h):typeof U==\"object\"&&(U.current=h))},[R,U,L,me,J]);C.default.useEffect(()=>{},[R,L,te,v,ie,_?.locale,j,M,ae]);let oe={ref:ge,onClick(h){if(!h)throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');!S&&typeof A==\"function\"&&A(h),S&&W.props&&typeof W.props.onClick==\"function\"&&W.props.onClick(h),j&&(h.defaultPrevented||dl(h,j,L,R,f,k,E,v,M,ie))},onMouseEnter(h){!S&&typeof se==\"function\"&&se(h),S&&W.props&&typeof W.props.onMouseEnter==\"function\"&&W.props.onMouseEnter(h),j&&((!ie||!0)&&M||Mn(j,L,R,{locale:v,priority:!0,bypassPrefetchedCheck:!0},{kind:ae},M))},onTouchStart(h){!S&&typeof Ne==\"function\"&&Ne(h),S&&W.props&&typeof W.props.onTouchStart==\"function\"&&W.props.onTouchStart(h),j&&(!ie&&M||Mn(j,L,R,{locale:v,priority:!0,bypassPrefetchedCheck:!0},{kind:ae},M))}};if((0,rl.isAbsoluteUrl)(R))oe.href=R;else if(!S||u||W.type===\"a\"&&!(\"href\"in W.props)){let h=typeof v<\"u\"?v:_?.locale,re=_?.isLocaleDomain&&(0,al.getDomainLocale)(R,h,_?.locales,_?.domainLocales);oe.href=re||(0,ul.addBasePath)((0,il.addLocale)(R,h,_?.defaultLocale))}return S?C.default.cloneElement(W,oe):C.default.createElement(\"a\",{...ne,...oe},o)}),fl=ml;(typeof Z.default==\"function\"||typeof Z.default==\"object\"&&Z.default!==null)&&typeof Z.default.__esModule>\"u\"&&(Object.defineProperty(Z.default,\"__esModule\",{value:!0}),Object.assign(Z.default,Z),yr.exports=Z.default)});var Tr=b((Ql,kr)=>{kr.exports=gr()});var Nl={};ei(Nl,{default:()=>_l,faqData:()=>bl,frontmatter:()=>pl});var t=et(it());function $e({children:n,quizSlug:e}){return React.createElement(\"article\",{className:\"max-w-4xl mx-auto text-gray-900\"},React.createElement(\"div\",{className:\"prose prose-lg max-w-none text-gray-900 [&>*]:text-gray-900\"},n))}var vr=et(Tr());function Ce({task:n,href:e}){let i=e||`/quiz/${n}`;return React.createElement(\"div\",{className:\"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\"},React.createElement(\"div\",{className:\"text-center\"},React.createElement(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-2\"},\"Not sure which AI fits your workflow?\"),React.createElement(\"p\",{className:\"text-gray-600 mb-4\"},\"Take our 30-second quiz to get a personalized recommendation\"),React.createElement(vr.default,{href:i,className:\"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\"},\"Take the \",n.charAt(0).toUpperCase()+n.slice(1),\" Quiz \\u2192\")))}var pl={title:\"AI Video Generators & Editors (2025) \\u2014 CapCut vs Runway, Pika & Descript\",description:\"Professional comparison of AI video tools for creators, marketers, and filmmakers. Auto-editors, prompt-to-video engines, and AI post-production solutions.\",slug:\"best-ai-video-generators-2025-capcut-vs-runway-pika-descript\",template:\"pillar\",cluster:\"video\",priority:\"Medium\",lastUpdated:\"2025-07-22\"},bl=[{q:\"Which AI makes the most realistic video?\",a:\"Runway Gen-3 Alpha currently leads on photorealism and camera control, producing cinema-quality footage that's often indistinguishable from traditional filming for short clips.\"},{q:\"What's the cheapest way to create lots of social media videos?\",a:\"CapCut's free plan offers the best value: no watermark, mobile-first design, auto-templates, and seamless social media integration. Perfect for high-volume content creation.\"},{q:\"Can I monetize AI-generated video footage?\",a:\"Yes, but check each tool's license. Runway and Veed allow commercial use on paid tiers. CapCut and Pika also permit commercial use. Always review terms for your specific use case.\"},{q:\"What resolution limits should I expect from AI video tools?\",a:\"Prompt-to-video tools are currently capped at HD (Runway 1280\\xD7768, Pika 1080p). Auto-editors like CapCut and Veed can export 4K. Expect 4K prompt-to-video by late 2025.\"},{q:\"Which tool is best for team collaboration on video projects?\",a:\"Veed.io excels at team collaboration with shared workspaces, brand kits, and commenting features. Descript also offers good collaboration for transcript-based editing workflows.\"},{q:\"How long does it take to generate AI video?\",a:\"Generation times vary: Pika (30-60 seconds for 3s clips), Runway Gen-3 (2-5 minutes for 4s clips). Auto-editors like CapCut render 15-second clips in 30-60 seconds.\"}];function Dr(n){let e=Object.assign({h2:\"h2\",p:\"p\",strong:\"strong\",ol:\"ol\",li:\"li\",hr:\"hr\",h3:\"h3\",ul:\"ul\",em:\"em\"},n.components);return(0,t.jsxDEV)($e,{quizSlug:\"video\",children:[(0,t.jsxDEV)(Ce,{task:\"video\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:16,columnNumber:1},this),(0,t.jsxDEV)(e.h2,{children:\"2025 Report \\u2014 The New AI-Video Stack\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:18,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[\"AI video is no longer a gimmick. Creators assemble \",(0,t.jsxDEV)(e.strong,{children:\"multi-tool workflows\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:20,columnNumber:52},this),\" that swap hours of manual work for minutes of prompt-driven generation. The landscape has evolved into three distinct layers, each serving different creative needs and skill levels.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:20,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:\"We compare tools across three critical layers:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:22,columnNumber:1},this),(0,t.jsxDEV)(e.ol,{children:[`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Auto-editors\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:24,columnNumber:4},this),\" (CapCut, Veed.io) for TikTok/Reels velocity\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:24,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Prompt-to-video engines\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:25,columnNumber:4},this),\" (Pika, Runway Gen-3) for original footage\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"AI post-production\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:26,columnNumber:4},this),\" (Descript, Captions.ai) for polish & localization\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:26,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:24,columnNumber:1},this),(0,t.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:28,columnNumber:1},this),(0,t.jsxDEV)(e.h2,{children:\"Layer 1 \\u2014 Auto-Editors for Social Media Velocity\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:30,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:\"CapCut Pro \\u2014 The Creator's Flywheel\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:32,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:(0,t.jsxDEV)(e.strong,{children:\"Perfect for solo creators who need dozens of clips per week.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:34,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:34,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[`| Metric | Value |\n|--------|-------|\n| `,(0,t.jsxDEV)(e.strong,{children:\"Max Resolution\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:38,columnNumber:3},this),` | 4K 60fps |\n| `,(0,t.jsxDEV)(e.strong,{children:\"Render Time (15s clip)\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:39,columnNumber:3},this),` | 30-60 seconds |\n| `,(0,t.jsxDEV)(e.strong,{children:\"Free Watermark\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:40,columnNumber:3},this),\" | \",(0,t.jsxDEV)(e.strong,{children:\"No\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:40,columnNumber:24},this),` |\n| `,(0,t.jsxDEV)(e.strong,{children:\"AI Features\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:41,columnNumber:3},this),` | Auto-captions, background removal, voice enhancement |\n| `,(0,t.jsxDEV)(e.strong,{children:\"Pricing\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:42,columnNumber:3},this),\" | Free tier available, Pro $9.99/month |\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:36,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"Strengths:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:44,columnNumber:1},this),` Mobile-first design, extensive template library, seamless TikTok integration, no watermark on free tier.\n`,(0,t.jsxDEV)(e.strong,{children:\"Weaknesses:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:45,columnNumber:1},this),\" Limited collaboration features, primarily mobile-focused workflow.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:44,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:\"Veed.io \\u2014 The Collaborative SaaS Solution\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:47,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:(0,t.jsxDEV)(e.strong,{children:\"Built-in Brand Kit + shared workspaces make it the marketer's choice.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:49,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:49,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[`| Metric | Value |\n|--------|-------|\n| `,(0,t.jsxDEV)(e.strong,{children:\"Max Resolution\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:53,columnNumber:3},this),` | 4K |\n| `,(0,t.jsxDEV)(e.strong,{children:\"Render Time (15s clip)\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:54,columnNumber:3},this),` | 45-75 seconds |\n| `,(0,t.jsxDEV)(e.strong,{children:\"Free Watermark\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:55,columnNumber:3},this),\" | \",(0,t.jsxDEV)(e.strong,{children:\"Yes\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:55,columnNumber:24},this),` |\n| `,(0,t.jsxDEV)(e.strong,{children:\"AI Features\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:56,columnNumber:3},this),` | Auto-subtitles, background removal, voice cloning |\n| `,(0,t.jsxDEV)(e.strong,{children:\"Pricing\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:57,columnNumber:3},this),\" | Free tier with watermark, Pro $24/month |\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:51,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"Strengths:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:59,columnNumber:1},this),` Team collaboration, brand consistency tools, web-based workflow, extensive format support.\n`,(0,t.jsxDEV)(e.strong,{children:\"Weaknesses:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:60,columnNumber:1},this),\" Watermark on free tier, higher pricing for advanced features.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:59,columnNumber:1},this),(0,t.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:62,columnNumber:1},this),(0,t.jsxDEV)(e.h2,{children:\"Layer 2 \\u2014 Prompt-to-Video Synthesis\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:64,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:\"The frontier of AI video generation, where text prompts become moving images.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:66,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[`| Tool | Strength | Weakness | Best For |\n|------|----------|----------|----------|\n| `,(0,t.jsxDEV)(e.strong,{children:\"Pika\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:70,columnNumber:3},this),` | Fast generation, surreal motion effects, Modify Region & Lip Sync | 1080p max resolution, credit-based pricing | Creative experiments, social content |\n| `,(0,t.jsxDEV)(e.strong,{children:\"Runway Gen-3 Alpha\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:71,columnNumber:3},this),\" | Cinematic realism, advanced keyframing, precise camera controls | 1280\\xD7768 resolution cap, queue times during peak | Professional filmmaking, commercials |\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:68,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:\"Pika \\u2014 The Creative Experimenter\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:73,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:(0,t.jsxDEV)(e.strong,{children:\"Rapid iteration meets surreal possibilities.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:75,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:75,columnNumber:1},this),(0,t.jsxDEV)(e.ul,{children:[`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Generation Speed:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:77,columnNumber:3},this),\" 30-60 seconds per 3-second clip\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:77,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Unique Features:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:78,columnNumber:3},this),\" Modify Region (edit specific areas), Lip Sync, Expand Canvas\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:78,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Pricing:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:79,columnNumber:3},this),\" Credit-based system, $10/month for 700 credits\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:79,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Resolution:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:80,columnNumber:3},this),\" Up to 1080p\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:80,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Best Use Cases:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:81,columnNumber:3},this),\" Social media content, creative experiments, rapid prototyping\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:81,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:77,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:\"Runway Gen-3 Alpha \\u2014 The Cinematic Powerhouse\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:83,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:(0,t.jsxDEV)(e.strong,{children:\"Industry-leading realism with professional controls.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:85,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:85,columnNumber:1},this),(0,t.jsxDEV)(e.ul,{children:[`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Generation Speed:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:87,columnNumber:3},this),\" 2-5 minutes per 4-second clip\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:87,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Unique Features:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:88,columnNumber:3},this),\" Camera controls, motion brush, keyframe animation\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:88,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Pricing:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:89,columnNumber:3},this),\" $15/month for 625 credits\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:89,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Resolution:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:90,columnNumber:3},this),\" Up to 1280\\xD7768\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:90,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Best Use Cases:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:91,columnNumber:3},this),\" Film production, commercials, high-end marketing content\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:91,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:87,columnNumber:1},this),(0,t.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:93,columnNumber:1},this),(0,t.jsxDEV)(e.h2,{children:\"Layer 3 \\u2014 AI Post-Production\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:95,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:\"Descript \\u2014 The Text-Based Editor\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:97,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:(0,t.jsxDEV)(e.strong,{children:\"Edit video by editing the transcript. Revolutionary for long-form content.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:99,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:99,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:(0,t.jsxDEV)(e.strong,{children:\"Key Features:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:101,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:101,columnNumber:1},this),(0,t.jsxDEV)(e.ul,{children:[`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Overdub:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:102,columnNumber:3},this),\" AI voice cloning for corrections\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:102,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Filler Word Removal:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:103,columnNumber:3},this),' Automatic \"um\" and \"uh\" elimination']},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:103,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Studio Sound:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:104,columnNumber:3},this),\" AI audio enhancement\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:104,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Transcription:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:105,columnNumber:3},this),\" Industry-leading accuracy\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:105,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Pricing:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:106,columnNumber:3},this),\" Free tier available, Creator $12/month\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:106,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:102,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"Ideal for:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:108,columnNumber:1},this),\" Podcasts, webinars, training videos, interview content.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:108,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:\"Captions.ai \\u2014 The Social Subtitle Engine\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:110,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:(0,t.jsxDEV)(e.strong,{children:\"Animated, mobile-style captions that drive engagement.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:112,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:112,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:(0,t.jsxDEV)(e.strong,{children:\"Key Features:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:114,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:114,columnNumber:1},this),(0,t.jsxDEV)(e.ul,{children:[`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"AI Dubbing:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:115,columnNumber:3},this),\" 29+ languages with voice matching\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:115,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Eye Contact Correction:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:116,columnNumber:3},this),\" AI-powered gaze adjustment\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:116,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Script Writer:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:117,columnNumber:3},this),\" AI-generated video scripts\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:117,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Animated Captions:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:118,columnNumber:3},this),\" TikTok-style subtitle animations\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:118,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Pricing:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:119,columnNumber:3},this),\" Free tier with unlimited exports, Pro $20/month\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:119,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:115,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"Ideal for:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:121,columnNumber:1},this),\" Social media content, international marketing, accessibility compliance.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:121,columnNumber:1},this),(0,t.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:123,columnNumber:1},this),(0,t.jsxDEV)(e.h2,{children:'Case Study: \"Bigfoot Boys\" \\u2014 Daily AI Vlogs at 80% Less Effort'},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:125,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"The Challenge:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:127,columnNumber:1},this),\" Creating daily vlog content without burning out.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:127,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:(0,t.jsxDEV)(e.strong,{children:\"The AI-Powered Solution:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:129,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:129,columnNumber:1},this),(0,t.jsxDEV)(e.ol,{children:[`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Script Generation:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:130,columnNumber:4},this),\" ChatGPT creates engaging daily topics and scripts\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:130,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Video Creation:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:131,columnNumber:4},this),\" Google Veo 3 generates talking-head footage\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:131,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Auto-Publishing:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:132,columnNumber:4},this),\" Zapier automatically posts to TikTok and Instagram\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:132,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:130,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:(0,t.jsxDEV)(e.strong,{children:\"Results:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:134,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:134,columnNumber:1},this),(0,t.jsxDEV)(e.ul,{children:[`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"3.1M followers\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:135,columnNumber:3},this),\" across platforms\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:135,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Active work per clip:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:136,columnNumber:3},this),\" ~10 minutes (vs. 3-5 hours traditional)\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:136,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Consistency:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:137,columnNumber:3},this),\" Daily uploads without creator fatigue\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:137,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Scalability:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:138,columnNumber:3},this),\" Multiple characters and storylines\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:138,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:135,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"Key Insight:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:140,columnNumber:1},this),\" The most successful AI video creators focus on storytelling and audience engagement, letting AI handle the technical production.\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:140,columnNumber:1},this),(0,t.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:142,columnNumber:1},this),(0,t.jsxDEV)(e.h2,{children:\"Strategic Recommendations by Creator Type\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:144,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:[\"\\u{1F3AC} \",(0,t.jsxDEV)(e.strong,{children:\"For the Social Media Manager\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:146,columnNumber:8},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:146,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"Recommended Stack:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:147,columnNumber:1},this),\" CapCut + Captions.ai\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:147,columnNumber:1},this),(0,t.jsxDEV)(e.ul,{children:[`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Workflow:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:148,columnNumber:3},this),\" Bulk content creation with consistent branding\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:148,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Budget:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:149,columnNumber:3},this),\" $30/month combined\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:149,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Output:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:150,columnNumber:3},this),\" 20-50 videos per month\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:150,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Key Benefit:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:151,columnNumber:3},this),\" Speed and consistency at scale\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:151,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:148,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:[\"\\u{1F3A5} \",(0,t.jsxDEV)(e.strong,{children:\"For the Indie Filmmaker\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:153,columnNumber:8},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:153,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"Recommended Stack:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:154,columnNumber:1},this),\" Runway Gen-3 + Descript\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:154,columnNumber:1},this),(0,t.jsxDEV)(e.ul,{children:[`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Workflow:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:155,columnNumber:3},this),\" High-quality concept videos with professional post-production\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:155,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Budget:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:156,columnNumber:3},this),\" $27/month combined\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:156,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Output:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:157,columnNumber:3},this),\" 5-10 high-quality videos per month\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:157,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Key Benefit:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:158,columnNumber:3},this),\" Cinematic quality without crew costs\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:158,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:155,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:[\"\\u{1F3E2} \",(0,t.jsxDEV)(e.strong,{children:\"For the Corporate Marketer\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:160,columnNumber:8},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:160,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"Recommended Stack:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:161,columnNumber:1},this),\" Veed.io + Descript\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:161,columnNumber:1},this),(0,t.jsxDEV)(e.ul,{children:[`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Workflow:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:162,columnNumber:3},this),\" Team collaboration with brand consistency\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:162,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Budget:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:163,columnNumber:3},this),\" $36/month combined\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:163,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Output:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:164,columnNumber:3},this),\" 10-20 branded videos per month\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:164,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Key Benefit:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:165,columnNumber:3},this),\" Team workflows with professional polish\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:165,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:162,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:[\"\\u{1F4F1} \",(0,t.jsxDEV)(e.strong,{children:\"For the Content Creator\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:167,columnNumber:8},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:167,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"Recommended Stack:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:168,columnNumber:1},this),\" Pika + Captions.ai\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:168,columnNumber:1},this),(0,t.jsxDEV)(e.ul,{children:[`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Workflow:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:169,columnNumber:3},this),\" Creative experimentation with viral potential\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:169,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Budget:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:170,columnNumber:3},this),\" $30/month combined\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:170,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Output:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:171,columnNumber:3},this),\" 30+ experimental videos per month\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:171,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Key Benefit:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:172,columnNumber:3},this),\" Unique content that stands out\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:172,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:169,columnNumber:1},this),(0,t.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:174,columnNumber:1},this),(0,t.jsxDEV)(e.h2,{children:\"The 2025 Video AI Landscape: What's Coming\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:176,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:(0,t.jsxDEV)(e.strong,{children:\"Emerging Trends:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:178,columnNumber:5},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:178,columnNumber:1},this),(0,t.jsxDEV)(e.ul,{children:[`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Higher Resolutions:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:179,columnNumber:3},this),\" 4K prompt-to-video expected by Q3 2025\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:179,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Longer Clips:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:180,columnNumber:3},this),\" 60-second generations becoming standard\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:180,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Real-Time Generation:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:181,columnNumber:3},this),\" Live streaming with AI backgrounds\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:181,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Voice-Video Sync:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:182,columnNumber:3},this),\" Perfect lip-sync across all tools\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:182,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:[(0,t.jsxDEV)(e.strong,{children:\"Multi-Modal Integration:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:183,columnNumber:3},this),\" Text, image, and audio prompts combined\"]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:183,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:179,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:(0,t.jsxDEV)(e.strong,{children:\"Market Consolidation:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:185,columnNumber:5},this)},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:185,columnNumber:1},this),(0,t.jsxDEV)(e.ul,{children:[`\n`,(0,t.jsxDEV)(e.li,{children:\"Major platforms acquiring specialized tools\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:186,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:\"Integration between editing and generation platforms\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:187,columnNumber:1},this),`\n`,(0,t.jsxDEV)(e.li,{children:\"API-first approaches for custom workflows\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:188,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:186,columnNumber:1},this),(0,t.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:190,columnNumber:1},this),(0,t.jsxDEV)(e.h2,{children:\"Deep-Dive Tool Profiles\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:192,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:[\"CapCut \\u2014 \",(0,t.jsxDEV)(e.em,{children:\"the mobile-first velocity engine\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:194,columnNumber:14},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:194,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"Strengths:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:196,columnNumber:1},this),` No watermark on free tier; extensive template library; seamless social media integration; mobile-optimized workflow.\n`,(0,t.jsxDEV)(e.strong,{children:\"Weaknesses:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:197,columnNumber:1},this),` Limited collaboration features; primarily mobile-focused; fewer advanced AI features.\n`,(0,t.jsxDEV)(e.em,{children:\"Perfect for: Solo creators, TikTok/Instagram content, high-volume social media posting.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:198,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:196,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:[\"Runway Gen-3 \\u2014 \",(0,t.jsxDEV)(e.em,{children:\"the cinematic realism leader\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:200,columnNumber:20},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:200,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"Strengths:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:202,columnNumber:1},this),` Industry-leading video quality; advanced camera controls; professional-grade output; extensive customization.\n`,(0,t.jsxDEV)(e.strong,{children:\"Weaknesses:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:203,columnNumber:1},this),` Higher cost per generation; longer processing times; resolution limitations.\n`,(0,t.jsxDEV)(e.em,{children:\"Perfect for: Film production, commercials, high-end marketing content, creative agencies.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:204,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:202,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:[\"Pika \\u2014 \",(0,t.jsxDEV)(e.em,{children:\"the creative experimenter's playground\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:206,columnNumber:12},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:206,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"Strengths:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:208,columnNumber:1},this),` Fast generation times; unique creative features; affordable pricing; innovative editing tools.\n`,(0,t.jsxDEV)(e.strong,{children:\"Weaknesses:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:209,columnNumber:1},this),` Resolution limitations; credit-based pricing can add up; less photorealistic than competitors.\n`,(0,t.jsxDEV)(e.em,{children:\"Perfect for: Creative experiments, social media content, rapid prototyping, artistic projects.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:210,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:208,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:[\"Veed.io \\u2014 \",(0,t.jsxDEV)(e.em,{children:\"the collaborative marketing platform\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:212,columnNumber:15},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:212,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"Strengths:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:214,columnNumber:1},this),` Team collaboration features; brand consistency tools; web-based workflow; extensive format support.\n`,(0,t.jsxDEV)(e.strong,{children:\"Weaknesses:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:215,columnNumber:1},this),` Watermark on free tier; higher pricing; less advanced AI features than specialized tools.\n`,(0,t.jsxDEV)(e.em,{children:\"Perfect for: Marketing teams, brand content, collaborative projects, corporate communications.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:216,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:214,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:[\"Descript \\u2014 \",(0,t.jsxDEV)(e.em,{children:\"the text-based editing revolution\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:218,columnNumber:16},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:218,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"Strengths:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:220,columnNumber:1},this),` Revolutionary transcript-based editing; excellent audio AI; voice cloning capabilities; intuitive workflow.\n`,(0,t.jsxDEV)(e.strong,{children:\"Weaknesses:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:221,columnNumber:1},this),` Learning curve for traditional editors; limited video effects; primarily suited for talking-head content.\n`,(0,t.jsxDEV)(e.em,{children:\"Perfect for: Podcasts, interviews, educational content, webinars, voice-over work.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:222,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:220,columnNumber:1},this),(0,t.jsxDEV)(e.h3,{children:[\"Captions.ai \\u2014 \",(0,t.jsxDEV)(e.em,{children:\"the social engagement optimizer\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:224,columnNumber:19},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:224,columnNumber:1},this),(0,t.jsxDEV)(e.p,{children:[(0,t.jsxDEV)(e.strong,{children:\"Strengths:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:226,columnNumber:1},this),` Unlimited free exports; no watermark; excellent subtitle animations; multi-language dubbing.\n`,(0,t.jsxDEV)(e.strong,{children:\"Weaknesses:\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:227,columnNumber:1},this),` Limited editing features; focused primarily on captions; fewer creative tools.\n`,(0,t.jsxDEV)(e.em,{children:\"Perfect for: Social media optimization, international content, accessibility compliance, engagement boosting.\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:228,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:226,columnNumber:1},this),(0,t.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:230,columnNumber:1},this),(0,t.jsxDEV)(Ce,{task:\"video\"},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:232,columnNumber:1},this),(0,t.jsxDEV)(e.hr,{},void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:234,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\",lineNumber:14,columnNumber:1},this)}function hl(n={}){let{wrapper:e}=n.components||{};return e?(0,t.jsxDEV)(e,Object.assign({},n,{children:(0,t.jsxDEV)(Dr,n,void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-574532a5-b807-47e5-8377-41a7567cdab7.mdx\"},this):Dr(n)}var _l=hl;return ni(Nl);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "pillars/best-ai-video-generators.mdx", "_raw": {"sourceFilePath": "pillars/best-ai-video-generators.mdx", "sourceFileName": "best-ai-video-generators.mdx", "sourceFileDir": "pillars", "contentType": "mdx", "flattenedPath": "pillars/best-ai-video-generators"}, "type": "<PERSON><PERSON>", "url": "/best-ai-video-generators-2025-capcut-vs-runway-pika-descript", "slugFromPath": "pillars/best-ai-video-generators"}, "documentHash": "1753205230905", "hasWarnings": false, "documentTypeName": "<PERSON><PERSON>"}}}