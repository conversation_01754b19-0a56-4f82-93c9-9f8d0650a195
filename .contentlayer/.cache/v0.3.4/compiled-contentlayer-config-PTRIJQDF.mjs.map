{"version": 3, "sources": ["../../../contentlayer.config.ts"], "sourcesContent": ["import { defineDocumentType, makeSource } from 'contentlayer/source-files'\nimport rehypeHighlight from 'rehype-highlight'\n\nexport const Pillar = defineDocumentType(() => ({\n  name: 'Pillar',\n  filePathPattern: `**/*.mdx`,\n  contentType: 'mdx',\n  fields: {\n    title: {\n      type: 'string',\n      description: 'The title of the page',\n      required: true,\n    },\n    description: {\n      type: 'string',\n      description: 'The description of the page',\n      required: true,\n    },\n    slug: {\n      type: 'string',\n      description: 'The slug for the page URL',\n      required: true,\n    },\n    cluster: {\n      type: 'string',\n      description: 'The parent cluster ID',\n      required: true,\n    },\n    template: {\n      type: 'string',\n      description: 'The template to use for rendering',\n      required: true,\n    },\n    priority: {\n      type: 'enum',\n      options: ['High', 'Medium', 'Low'],\n      description: 'The priority level',\n      required: false,\n    },\n    lastUpdated: {\n      type: 'string',\n      description: 'Last updated date',\n      required: false,\n    },\n  },\n  computedFields: {\n    url: {\n      type: 'string',\n      resolve: (doc) => `/${doc.slug}`,\n    },\n    slugFromPath: {\n      type: 'string',\n      resolve: (doc) => doc._raw.flattenedPath,\n    },\n  },\n}))\n\nexport default makeSource({\n  contentDirPath: './content',\n  documentTypes: [Pillar],\n  mdx: {\n    remarkPlugins: [],\n    rehypePlugins: [rehypeHighlight],\n  },\n})\n"], "mappings": ";AAAA,SAAS,oBAAoB,kBAAkB;AAC/C,OAAO,qBAAqB;AAErB,IAAM,SAAS,mBAAmB,OAAO;AAAA,EAC9C,MAAM;AAAA,EACN,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,QAAQ;AAAA,IACN,OAAO;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ,UAAU,KAAK;AAAA,MACjC,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,gBAAgB;AAAA,IACd,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ,IAAI,IAAI,IAAI;AAAA,IAChC;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ,IAAI,KAAK;AAAA,IAC7B;AAAA,EACF;AACF,EAAE;AAEF,IAAO,8BAAQ,WAAW;AAAA,EACxB,gBAAgB;AAAA,EAChB,eAAe,CAAC,MAAM;AAAA,EACtB,KAAK;AAAA,IACH,eAAe,CAAC;AAAA,IAChB,eAAe,CAAC,eAAe;AAAA,EACjC;AACF,CAAC;", "names": []}