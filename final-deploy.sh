#!/bin/bash
echo "🚀 FINAL DEPLOYMENT: Complete AI Pillar System to Vercel"
echo "=================================================="

# Navigate to project directory
cd "/Users/<USER>/Desktop/Webiste Tool"

# Show current status
echo "📋 Current git status:"
git status --porcelain

# Add all files (including any we might have missed)
echo "📁 Adding all files..."
git add .

# Show what we're about to commit
echo "📋 Files staged for commit:"
git status --porcelain

# Create comprehensive commit message
echo "💾 Creating commit..."
git commit -m "🚀 COMPLETE: AI Tools Platform with Writing & Coding Pillars

🎯 MAJOR DEPLOYMENT: Full-featured AI comparison platform

NEW FEATURES:
✅ Writing Pillar: Comprehensive AI writing tool comparison
✅ Coding Pillar: Developer-focused AI coding assistant guide
✅ Intelligent Quiz Systems: Personalized recommendations
✅ 7 Recommendation Pages: Detailed AI tool profiles
✅ Professional UI: Mobile-responsive design

PILLAR CONTENT:
📝 Writing Pillar (/best-ai-for-writing-2025-claude-3-5-vs-gpt-4o-gemini-more):
- User archetypes (Blogger, Student, Marketer)
- AI model comparison table with pricing
- Speed tests and deep-dive profiles
- Working quiz CTA

💻 Coding Pillar (/best-ai-for-coding-2025-gpt-4o-vs-claude-github-copilot-more):
- Developer specifications and context windows
- Code challenge examples with syntax highlighting
- Enterprise security checklist
- Technical comparison table
- Working quiz CTA

QUIZ SYSTEMS:
🎯 Writing Quiz (/quiz/writing):
- 5 strategic questions for writers
- Recommends Claude, GPT-4o, Gemini, Perplexity, Grok
- Beautiful blue-themed progressive UI

💻 Coding Quiz (/quiz/coding):
- 5 strategic questions for developers
- Recommends GPT-4o, Claude, GitHub Copilot, Replit
- Beautiful green-themed progressive UI

RECOMMENDATION ENGINE:
🎨 7 AI Tool Pages (/recommend/[model]):
- Claude 3.5 Sonnet, GPT-4o, Gemini Advanced
- Perplexity Pro, Grok, GitHub Copilot, Replit Ghostwriter
- Detailed strengths, weaknesses, pricing
- Direct CTAs to each platform

TECHNICAL FIXES:
🔧 Template literal escaping in code examples
🔧 Dynamic content rendering based on pillar type
🔧 Proper component architecture
🔧 SEO-optimized metadata and structured data

FILES INCLUDED:
- content/pillars/best-ai-for-writing.mdx
- content/pillars/best-ai-for-coding.mdx
- app/quiz/writing/page.tsx + quizConfig.ts
- app/quiz/coding/page.tsx + quizConfig.ts
- app/recommend/[model]/page.tsx (enhanced)
- app/[slug]/PillarClient.tsx (fixed)
- components/mdx-components.tsx (enhanced)

EXPECTED URLS:
- Writing: /best-ai-for-writing-2025-claude-3-5-vs-gpt-4o-gemini-more
- Coding: /best-ai-for-coding-2025-gpt-4o-vs-claude-github-copilot-more
- Quizzes: /quiz/writing, /quiz/coding
- Recommendations: /recommend/claude, /recommend/gpt, etc.

This creates a complete AI tools comparison platform ready for production!"

# Push to GitHub (which triggers Vercel)
echo "📤 Pushing to GitHub..."
git push origin main

# Check if push was successful
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ SUCCESS! Deployment pushed to GitHub"
    echo "🔄 Vercel will automatically redeploy"
    echo ""
    echo "🎯 NEXT STEPS:"
    echo "1. Check Vercel dashboard for deployment status"
    echo "2. Test live URLs once deployment completes"
    echo "3. Verify quiz systems work on production"
    echo ""
    echo "📊 LIVE URLS (once deployed):"
    echo "- Writing Pillar: https://your-domain.vercel.app/best-ai-for-writing-2025-claude-3-5-vs-gpt-4o-gemini-more"
    echo "- Coding Pillar: https://your-domain.vercel.app/best-ai-for-coding-2025-gpt-4o-vs-claude-github-copilot-more"
    echo "- Writing Quiz: https://your-domain.vercel.app/quiz/writing"
    echo "- Coding Quiz: https://your-domain.vercel.app/quiz/coding"
    echo ""
    echo "🎉 DEPLOYMENT COMPLETE!"
else
    echo "❌ Push failed. Check git status and try again."
    git status
fi
