import csv, re, yaml, pathlib, sys
from collections import defaultdict

# -------------------------------
# Config
# -------------------------------
CSV_PATH = pathlib.Path("/Users/<USER>/Desktop/Webiste Tool/AI-Recommender_clusters_MVP_100.csv")
YAML_PATH = pathlib.Path("/Users/<USER>/Desktop/Webiste Tool/taxonomy.yaml")

# Cluster display metadata (id -> (label, description))
CLUSTER_META = {
    "image":    ("Image AI",    "Editing, enhancement, textures, generation."),
    "video":    ("Video AI",    "Video editing, subtitles, sound effects, animation."),
    "voice":    ("Voice AI",    "TTS, voice cloning, narration."),
    "text":     ("Text AI",     "Writing, blogging, copy, long-form text."),
    "design":   ("Design AI",   "Logos, presentations, slide decks, marketing assets."),
    "coding":   ("Coding AI",   "Code generation, debugging, SQL."),
    "career":   ("Career AI",   "Resumes, cover letters, interview prep."),
    "legal":    ("Legal AI",    "Contracts, clauses, compliance."),
    "finance":  ("Finance AI",  "Budgeting, modeling, financial analysis."),
    "document": ("Document AI", "PDF summarizing, doc parsing, OCR."),
    "chat":     ("Chat AI",     "Chatbots, assistants, conversational interfaces."),
    "character":("Character AI","Roleplay, avatar chat, agents."),
    "other":    ("Other",       "Unclassified or multi-modal."),
}

# -------------------------------
# Helpers
# -------------------------------
_slug_re = re.compile(r'[^a-z0-9]+')

def slugify(text: str) -> str:
    s = text.lower().strip()
    s = _slug_re.sub('-', s)
    s = re.sub('-+', '-', s).strip('-')
    return s

def detect_modifier(keyword: str) -> str:
    kw = keyword.lower()
    if ' vs ' in kw or ' vs. ' in kw:
        return 'vs'
    if 'free ' in kw or kw.startswith('free ') or kw.endswith(' free'):
        return 'free'
    if kw.startswith('best ') or 'best ai' in kw or kw.startswith('top ') or 'top ai' in kw:
        return 'best'
    if 'alternative' in kw or 'alternatives' in kw:
        return 'alternative'
    if kw.startswith('how ') or kw.startswith('how to'):
        return 'howto'
    return 'usecase'

INTENT_MAP = {
    'free': 'cost',
    'best': 'best',
    'vs': 'comparative',
    'alternative': 'alternative',
    'howto': 'howto',
    'usecase': 'usecase',
}

TEMPLATE_MAP = {
    'cost': 'cost',
    'best': 'pillar',
    'comparative': 'compare',
    'alternative': 'compare',
    'howto': 'howto',
    'usecase': 'usecase',
}

# Sometimes cluster names in CSV differ; normalize
def normalize_cluster(raw: str) -> str:
    if not isinstance(raw, str) or raw.strip()=='':
        return 'other'
    r = raw.lower()
    if 'image' in r or 'photo' in r:
        return 'image'
    if 'video' in r:
        return 'video'
    if 'voice' in r or 'audio' in r:
        return 'voice'
    if 'text' in r or 'write' in r or 'content' in r:
        return 'text'
    if 'design' in r or 'slide' in r or 'presentation' in r:
        return 'design'
    if 'code' in r or 'coding' in r:
        return 'coding'
    if 'career' in r or 'resume' in r or 'cv' in r:
        return 'career'
    if 'legal' in r or 'law' in r:
        return 'legal'
    if 'finance' in r or 'budget' in r:
        return 'finance'
    if 'document' in r or 'pdf' in r:
        return 'document'
    if 'chat' in r:
        return 'chat'
    if 'character' in r:
        return 'character'
    return 'other'

# -------------------------------
# Load CSV
# -------------------------------
rows = []
with CSV_PATH.open() as f:
    reader = csv.DictReader(f)
    for r in reader:
        kw = r['Keyword'].strip()
        cluster = normalize_cluster(r.get('Cluster', ''))
        mod = detect_modifier(kw)
        intent = INTENT_MAP[mod]
        slug = slugify(kw)
        # Numeric conversions (defensive)
        try:
            vol = int(float(r.get('Volume',0)))
        except:
            vol = 0
        try:
            kd = float(r.get('Keyword Difficulty',0))
        except:
            kd = 0.0
        try:
            cpc = float(r.get('CPC (USD)',0) or 0)
        except:
            cpc = 0.0
        priority = r.get('Priority','Low')
        rows.append({
            'keyword': kw,
            'cluster': cluster,
            'slug': slug,
            'modifier': mod,
            'intent': intent,
            'priority': priority,
            'volume': vol,
            'kd': kd,
            'cpc': float(f"{cpc:.2f}"),
            'template': TEMPLATE_MAP[intent],
        })

# -------------------------------
# Bucket into clusters
# -------------------------------
clustered = defaultdict(list)
for r in rows:
    cid = r['cluster']
    clustered[cid].append(r)

# -------------------------------
# Build taxonomy dict and write YAML
import datetime

# Prepare base taxonomy structure
taxonomy = {
    'version': 1,
    'generated': datetime.date.today().isoformat(),
    'notes': 'Auto-generated from AI-Recommender_clusters_MVP_100.csv',
    'clusters': []
}

# Populate clusters
for cid, (label, description) in CLUSTER_META.items():
    pages = clustered.get(cid, [])
    # Convert page objects to dicts with only the needed fields
    page_entries = []
    for p in pages:
        entry = {
            'keyword': p['keyword'],
            'slug': p['slug'],
            'modifier': p['modifier'],
            'intent': p['intent'],
            'priority': p['priority'],
            'volume': p['volume'],
            'kd': p['kd'],
            'cpc': p['cpc'],
            'template': p['template'],
        }
        page_entries.append(entry)
    taxonomy['clusters'].append({
        'id': cid,
        'label': label,
        'description': description,
        'pages': page_entries
    })

# Write out to YAML file
with YAML_PATH.open('w') as yaml_file:
    yaml.dump(taxonomy, yaml_file, sort_keys=False)

print(f"taxonomy.yaml generated at: {YAML_PATH}")