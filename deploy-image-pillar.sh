#!/bin/bash
echo "🎨 DEPLOYING: Complete AI Image Generation Pillar System"
echo "======================================================="

# Navigate to project directory
cd "/Users/<USER>/Desktop/Webiste Tool"

# Show current directory and files
echo "📍 Current directory:"
pwd
echo ""

echo "📁 Adding all new image pillar files..."
git add .

echo "📋 Git status:"
git status

echo "💾 Creating comprehensive commit..."
git commit -m "🎨 COMPLETE: AI Image Generation Pillar System (Pillar 3)

✨ NEW PILLAR: Best AI Image & Texture Generators (2025)

📄 PILLAR CONTENT:
✅ content/pillars/best-ai-image-generators.mdx
- Comprehensive comparison of Midjourney, Stable Diffusion, Ideogram, DALL-E
- Unified prompt showdown with scoring matrix
- Cost analysis and commercial licensing breakdown
- Professional persona recommendations (Game Dev, Artist, Marketer)
- Deep-dive profiles with strengths/weaknesses

🎯 IMAGE QUIZ SYSTEM:
✅ app/quiz/image/page.tsx - Interactive quiz component
✅ app/quiz/image/quizConfig.ts - 5 strategic questions & scoring
- Use case analysis (game dev/artist/marketer)
- Text requirements (critical for Ideogram recommendation)
- Technical skill level (advanced → Stable Diffusion)
- Budget constraints (low budget → Stable Diffusion)
- Commercial needs assessment
- Smart override rules for critical requirements

🎨 RECOMMENDATION PAGES:
✅ Extended app/recommend/[model]/page.tsx
- Added Midjourney (aesthetic perfectionist)
- Added Stable Diffusion (technical powerhouse)  
- Added Ideogram (typography specialist)
- Added DALL-E (conversational creator)
- Complete with pricing, strengths, weaknesses, CTAs

🏠 HOMEPAGE INTEGRATION:
✅ Updated app/page.tsx
- Purple/pink gradient theme for image pillar
- Image generation quiz CTAs in hero section
- Enhanced pillar cards with 🎨 Image Generation badge
- Consistent styling with writing/coding pillars
- Triple quiz CTA in bottom section

🎨 PILLAR CLIENT UPDATES:
✅ Enhanced app/[slug]/PillarClient.tsx
- Added ImagePillarContent component
- Purple theme detection and styling
- Comprehensive image generation content
- Quiz CTAs with proper routing
- Professional tables and export links

TECHNICAL FEATURES:
✅ Cluster detection: pillar.cluster === 'image'
✅ Purple/pink gradient theming throughout
✅ Consistent component architecture
✅ Mobile-responsive design
✅ SEO-optimized content structure
✅ Professional scoring matrices and tables

QUIZ LOGIC:
- Text requirements → Ideogram (override)
- Budget conscious → Stable Diffusion (override)
- Advanced technical → Stable Diffusion (override)
- Game development → Midjourney + Stable Diffusion hybrid
- Digital art → Midjourney primary
- Marketing → Ideogram for text, DALL-E for rapid iteration

LIVE URLS (once deployed):
- Pillar: /best-ai-image-generators-2025-midjourney-vs-stable-diffusion-ideogram-dalle
- Quiz: /quiz/image
- Recommendations: /recommend/midjourney, /recommend/stable_diffusion, etc.

This completes the AI tools platform with 3 comprehensive pillars:
1. Writing AI (Claude, GPT-4o, Gemini, Perplexity, Grok)
2. Coding AI (GPT-4o, Claude, GitHub Copilot, Replit)
3. Image AI (Midjourney, Stable Diffusion, Ideogram, DALL-E)

Total: 3 pillar guides, 3 quiz systems, 11 recommendation pages!"

# Push to GitHub (triggers Vercel deployment)
echo "📤 Pushing to GitHub..."
git push origin main

# Check if push was successful
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ SUCCESS! Complete AI Image Generation Pillar deployed!"
    echo "🔄 Vercel will automatically redeploy with new pillar"
    echo ""
    echo "🎯 LIVE URLS (once deployed):"
    echo "- Homepage: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/"
    echo "- Image Pillar: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/best-ai-image-generators-2025-midjourney-vs-stable-diffusion-ideogram-dalle"
    echo "- Image Quiz: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/quiz/image"
    echo "- Midjourney: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/recommend/midjourney"
    echo "- Stable Diffusion: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/recommend/stable_diffusion"
    echo "- Ideogram: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/recommend/ideogram"
    echo "- DALL-E: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/recommend/dalle"
    echo ""
    echo "🎉 COMPLETE AI TOOLS PLATFORM NOW LIVE!"
    echo "📊 Total: 3 Pillars, 3 Quiz Systems, 11 AI Tool Recommendations"
else
    echo "❌ Push failed. Check git status and try again."
    git status
fi
