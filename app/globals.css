@tailwind base;
@tailwind components;
@tailwind utilities;

/* Removed CSS custom properties - using fixed light theme */

body {
  color: rgb(31, 41, 55) !important; /* Force dark gray text (gray-800) */
  background: white !important; /* Force white background */
}

/* Force text visibility for all elements */
* {
  color: inherit;
}

/* Ensure all text elements are visible */
h1, h2, h3, h4, h5, h6, p, div, span, li, td, th, a {
  color: rgb(31, 41, 55) !important;
}

/* Override any prose plugin defaults */
.prose * {
  color: rgb(31, 41, 55) !important;
}

/* MDX Styles */
.prose {
  max-width: none;
}

.prose h1 {
  @apply text-4xl font-bold mb-6;
  color: rgb(31, 41, 55) !important;
}

.prose h2 {
  @apply text-3xl font-semibold mb-4 mt-8;
  color: rgb(31, 41, 55) !important;
}

.prose h3 {
  @apply text-2xl font-semibold mb-3 mt-6;
  color: rgb(31, 41, 55) !important;
}

.prose p {
  @apply mb-4 leading-relaxed;
  color: rgb(31, 41, 55) !important;
}

.prose ul {
  @apply mb-4 pl-6;
  color: rgb(31, 41, 55) !important;
}

.prose li {
  @apply mb-2;
  color: rgb(31, 41, 55) !important;
}

.prose a {
  @apply underline;
  color: rgb(37, 99, 235) !important; /* blue-600 */
}

.prose a:hover {
  color: rgb(30, 64, 175) !important; /* blue-800 */
}

.prose code {
  @apply bg-gray-100 px-2 py-1 rounded text-sm;
  color: rgb(31, 41, 55) !important;
}

.prose pre {
  @apply bg-gray-900 p-4 rounded-lg overflow-x-auto mb-4;
  color: white !important;
}

.prose blockquote {
  @apply border-l-4 border-gray-300 pl-4 italic mb-4;
  color: rgb(75, 85, 99) !important; /* gray-600 */
}

/* Force visibility for table elements */
.prose table, .prose th, .prose td {
  color: rgb(31, 41, 55) !important;
}

/* Force visibility for strong and em elements */
.prose strong, .prose em, .prose b, .prose i {
  color: rgb(31, 41, 55) !important;
}
