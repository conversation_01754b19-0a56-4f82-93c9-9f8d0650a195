@tailwind base;
@tailwind components;
@tailwind utilities;

/* Removed CSS custom properties - using fixed light theme */

body {
  color: rgb(31, 41, 55); /* Force dark gray text (gray-800) */
  background: white; /* Force white background */
}

/* MDX Styles */
.prose {
  max-width: none;
}

.prose h1 {
  @apply text-4xl font-bold mb-6 text-gray-900;
}

.prose h2 {
  @apply text-3xl font-semibold mb-4 mt-8 text-gray-900;
}

.prose h3 {
  @apply text-2xl font-semibold mb-3 mt-6 text-gray-900;
}

.prose p {
  @apply mb-4 leading-relaxed text-gray-800;
}

.prose ul {
  @apply mb-4 pl-6;
}

.prose li {
  @apply mb-2;
}

.prose a {
  @apply text-blue-600 hover:text-blue-800 underline;
}

.prose code {
  @apply bg-gray-100 px-2 py-1 rounded text-sm;
}

.prose pre {
  @apply bg-gray-900 text-white p-4 rounded-lg overflow-x-auto mb-4;
}

.prose blockquote {
  @apply border-l-4 border-gray-300 pl-4 italic mb-4;
}
