'use client'

import { allPillars } from 'contentlayer/generated'
import { notFound } from 'next/navigation'
import { useMDXComponent } from 'next-contentlayer/hooks'

interface PageProps {
  params: {
    slug: string
  }
}

export async function generateStaticParams() {
  return allPillars.map((pillar) => ({
    slug: pillar.slug,
  }))
}

export default function PillarPage({ params }: PageProps) {
  const pillar = allPillars.find((pillar) => pillar.slug === params.slug)

  if (!pillar) {
    notFound()
  }

  const MDXContent = useMDXComponent(pillar.body.code)

  return (
    <article className="max-w-4xl mx-auto">
      <header className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
            {pillar.cluster}
          </span>
          {pillar.priority && (
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              pillar.priority === 'High' ? 'bg-red-100 text-red-800' :
              pillar.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
              'bg-green-100 text-green-800'
            }`}>
              {pillar.priority} Priority
            </span>
          )}
        </div>
        <h1 className="text-4xl font-bold mb-4">{pillar.title}</h1>
        <p className="text-xl text-gray-600 mb-4">{pillar.description}</p>
        {pillar.lastUpdated && (
          <p className="text-sm text-gray-500">
            Last updated: {pillar.lastUpdated}
          </p>
        )}
      </header>
      
      <div className="text-gray-900">
        <MDXContent />
      </div>
    </article>
  )
}
