import { allPillars } from 'contentlayer/generated'
import { notFound } from 'next/navigation'
import type { Metadata } from 'next'
import PillarClient from './PillarClient'

interface PageProps {
  params: {
    slug: string
  }
}

export async function generateStaticParams() {
  return allPillars.map((pillar) => ({
    slug: pillar.slug,
  }))
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const pillar = allPillars.find((pillar) => pillar.slug === params.slug)

  if (!pillar) {
    return {}
  }

  return {
    title: pillar.title,
    description: pillar.description,
  }
}

export default function PillarPage({ params }: PageProps) {
  const pillar = allPillars.find((pillar) => pillar.slug === params.slug)

  if (!pillar) {
    notFound()
  }

  return <PillarClient pillar={pillar} />
}
