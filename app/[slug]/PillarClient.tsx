'use client'

import type { Pillar } from 'contentlayer/generated'

interface PillarClientProps {
  pillar: Pillar
}

export default function PillarClient({ pillar }: PillarClientProps) {
  return (
    <article className="max-w-4xl mx-auto">
      <header className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
            pillar.cluster === 'text' ? 'bg-blue-100 text-blue-800' :
            pillar.cluster === 'code' ? 'bg-green-100 text-green-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {pillar.cluster === 'text' ? 'Writing' : pillar.cluster === 'code' ? 'Coding' : pillar.cluster}
          </span>
          {pillar.priority && (
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              pillar.priority === 'High' ? 'bg-red-100 text-red-800' :
              pillar.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
              'bg-green-100 text-green-800'
            }`}>
              {pillar.priority} Priority
            </span>
          )}
        </div>
        <h1 className="text-4xl font-bold mb-4 text-gray-900">{pillar.title}</h1>
        <p className="text-xl text-gray-600 mb-4">{pillar.description}</p>
        {pillar.lastUpdated && (
          <p className="text-sm text-gray-500">
            Last updated: {pillar.lastUpdated}
          </p>
        )}
      </header>

      {/* Render the actual MDX content */}
      <div
        className="prose prose-lg max-w-none text-gray-900"
        dangerouslySetInnerHTML={{ __html: pillar.body.html }}
      />
    </article>
  )
}
