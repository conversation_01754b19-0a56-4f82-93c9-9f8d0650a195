'use client'

import type { Pillar } from 'contentlayer/generated'

interface PillarClientProps {
  pillar: Pillar
}

export default function PillarClient({ pillar }: PillarClientProps) {

  return (
    <article className="max-w-4xl mx-auto">
      <header className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
            {pillar.cluster}
          </span>
          {pillar.priority && (
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              pillar.priority === 'High' ? 'bg-red-100 text-red-800' :
              pillar.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
              'bg-green-100 text-green-800'
            }`}>
              {pillar.priority} Priority
            </span>
          )}
        </div>
        <h1 className="text-4xl font-bold mb-4 text-gray-900">{pillar.title}</h1>
        <p className="text-xl text-gray-600 mb-4">{pillar.description}</p>
        {pillar.lastUpdated && (
          <p className="text-sm text-gray-500">
            Last updated: {pillar.lastUpdated}
          </p>
        )}
      </header>

      {/* Quiz CTA */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Not sure which AI fits your workflow?
          </h3>
          <p className="text-gray-600 mb-4">
            Take our 30-second quiz to get a personalized recommendation
          </p>
          <a
            href="/quiz/writing"
            className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors"
          >
            Take the Writing Quiz →
          </a>
        </div>
      </div>

      {/* Content */}
      <div className="text-gray-900 prose prose-lg max-w-none">
        <h2 className="text-3xl font-semibold mb-4 mt-8 text-gray-900">Who are you writing for?</h2>

        <div className="mb-6">
          <h3 className="text-xl font-semibold mb-2 text-gray-900">The Blogger</h3>
          <ul className="mb-4 pl-6 list-disc text-gray-800">
            <li><strong>Pain</strong> – needs original long-form content that won't feel robotic or earn an SEO penalty.</li>
            <li><strong>Ideal output</strong> – an AI blog generator that keeps a consistent tone.</li>
            <li><strong>Killer feature</strong> – a huge context window to track details across thousands of words.</li>
          </ul>
        </div>

        <div className="mb-6">
          <h3 className="text-xl font-semibold mb-2 text-gray-900">The Student</h3>
          <ul className="mb-4 pl-6 list-disc text-gray-800">
            <li><strong>Pain</strong> – must research, structure, and cite accurately while avoiding plagiarism.</li>
            <li><strong>Ideal output</strong> – an AI essay writer that returns verifiable facts with citations.</li>
            <li><strong>Killer feature</strong> – can ingest PDFs and analyse them directly.</li>
          </ul>
        </div>

        <div className="mb-8">
          <h3 className="text-xl font-semibold mb-2 text-gray-900">The Marketer</h3>
          <ul className="mb-4 pl-6 list-disc text-gray-800">
            <li><strong>Pain</strong> – high-volume, mixed-format content plus brand-voice consistency.</li>
            <li><strong>Ideal output</strong> – a tool that plugs into Google Workspace and accelerates campaigns.</li>
            <li><strong>Killer feature</strong> – analyses spreadsheet data and builds project plans.</li>
          </ul>
        </div>

        <hr className="my-8" />

        <h2 className="text-3xl font-semibold mb-4 text-gray-900">A market of specialists, not one "best" model</h2>
        <p className="mb-4 text-gray-800">
          Perplexity is an <strong>answer engine</strong>, Claude a <strong>creative prose specialist</strong>, and Gemini a <strong>productivity layer</strong> for Docs, Sheets, and Gmail. The takeaway: <em>choose by task</em>, not by raw IQ.
        </p>

        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-8">
          <h3 className="text-lg font-semibold mb-2 text-gray-900">⚠ Premium trap</h3>
          <p className="text-gray-800">
            The $20 Pro tiers are no longer the cutting edge. OpenAI, Anthropic, Perplexity, and xAI now sell $100–$300 "Max / Heavy" plans aimed at enterprises. For most writers the $20 tier remains the ROI sweet-spot, but know you're not buying the absolute top model.
          </p>
        </div>

        <h2 className="text-3xl font-semibold mb-4 text-gray-900">2025 AI-writer scorecard</h2>

        <div className="overflow-x-auto mb-8">
          <table className="min-w-full border border-gray-300">
            <thead className="bg-gray-50">
              <tr>
                <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Model</th>
                <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Best for</th>
                <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Stand-out feature</th>
                <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Context window</th>
                <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Free tier</th>
                <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Pro price</th>
                <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Key limitation</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>Claude 3.5 Sonnet</strong></td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Creative writing (Poet)</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">"Artifacts" live editor</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">200k tokens</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Yes (daily cap)</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">$20</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">No native real-time web search</td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>GPT-4o</strong></td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Generalist (Polymath)</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Multimodal + Custom GPTs</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">128k tokens</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Yes (cap)</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">$20</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Output can feel robotic</td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>Gemini Advanced</strong></td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Productivity (Producer)</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Deep Workspace integration</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">1M+ tokens</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Yes (std)</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">$19.99</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Creative flair weaker than Claude</td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>Perplexity Pro</strong></td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Research (Professor)</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Clickable citations, Deep Research</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">—</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Yes (cap)</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">$20</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Not a creative writer</td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>Grok</strong></td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Real-time insights (Provocateur)</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Live X / Twitter data</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">—</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Yes (cap)</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">$30</td>
                <td className="border border-gray-300 px-4 py-2 text-gray-800">Pricey; edgy tone not for all</td>
              </tr>
            </tbody>
          </table>
        </div>

        <p className="text-right text-sm mb-8">
          <a href="/export/scorecard.csv" className="text-blue-600 hover:text-blue-800 underline">Export to Sheets →</a>
        </p>

        <h2 className="text-3xl font-semibold mb-4 text-gray-900">Speed test ⚡</h2>
        <p className="mb-4 text-gray-600 italic">[Speed comparison GIF placeholder - GPT-4o vs Claude 3.5 vs Gemini]</p>
        <p className="mb-8 text-gray-800">
          GPT-4o starts generating sooner and streams tokens faster, a win for brainstorms and quick Q & A. Claude is ~2× faster than its predecessor Opus but still trails GPT-4o on sheer responsiveness.
        </p>

        <h2 className="text-3xl font-semibold mb-4 text-gray-900">Deep-dive profiles</h2>

        <h3 className="text-2xl font-semibold mb-3 text-gray-900">Claude 3.5 Sonnet — <em>the creative wordsmith</em></h3>
        <p className="mb-4 text-gray-800">
          <strong>Strengths.</strong> Thoughtful, expressive prose; 200k-token context; "Artifacts" side-panel for iterative editing.<br />
          <strong>Weaknesses.</strong> No built-in web browsing; free tier message cap.<br />
          <em>Read the full <a href="/claude-3-5-for-blogging-review" className="text-blue-600 hover:text-blue-800 underline">Claude 3.5 blogging review</a>.</em>
        </p>

        <h3 className="text-2xl font-semibold mb-3 text-gray-900">GPT-4o — <em>the versatile all-rounder</em></h3>
        <p className="mb-4 text-gray-800">
          Handles writing, code, data and images in one chat. Custom GPTs unlock niche workflows.<br />
          Criticisms: increasing verbosity, smaller window than Claude, privacy concerns.
        </p>

        <h3 className="text-2xl font-semibold mb-3 text-gray-900">Gemini Advanced — <em>the integrated productivity engine</em></h3>
        <p className="mb-4 text-gray-800">
          Native in Docs, Sheets, Gmail. Perfect for campaign tables, email summarising, Drive file look-ups.<br />
          Deep dive: <a href="/gemini-advanced-for-marketers-guide" className="text-blue-600 hover:text-blue-800 underline">Gemini for marketers</a>.
        </p>

        <h3 className="text-2xl font-semibold mb-3 text-gray-900">Perplexity Pro — <em>the research powerhouse</em></h3>
        <p className="mb-4 text-gray-800">
          Delivers answers with numbered citations; "Deep Research" builds exhaustive reports.<br />
          Guide: <a href="/how-to-use-perplexity-for-academic-research" className="text-blue-600 hover:text-blue-800 underline">How to use Perplexity for academic research</a>.
        </p>

        <h3 className="text-2xl font-semibold mb-3 text-gray-900">Grok — <em>the real-time provocateur</em></h3>
        <p className="mb-8 text-gray-800">
          Live social-media pulse plus a snarky attitude. Great for trend analysts, overkill for everyday writing.
        </p>

        {/* Quiz CTA */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8">
          <div className="text-center">
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Not sure which AI fits your workflow?
            </h3>
            <p className="text-gray-600 mb-4">
              Take our 30-second quiz to get a personalized recommendation
            </p>
            <a
              href="/quiz/writing"
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors"
            >
              Take the Writing Quiz →
            </a>
          </div>
        </div>
      </div>
    </article>
  )
}
