'use client'

import type { Pillar } from 'contentlayer/generated'

interface PillarClientProps {
  pillar: Pillar
}

export default function PillarClient({ pillar }: PillarClientProps) {
  // Determine pillar type based on cluster or slug
  const isWritingPillar = pillar.cluster === 'text' || pillar.slug.includes('writing')
  const isCodingPillar = pillar.cluster === 'code' || pillar.slug.includes('coding')
  const isImagePillar = pillar.cluster === 'image' || pillar.slug.includes('image')

  return (
    <article className="max-w-4xl mx-auto">
      <header className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
            isWritingPillar ? 'bg-blue-100 text-blue-800' :
            isCodingPillar ? 'bg-green-100 text-green-800' :
            isImagePillar ? 'bg-purple-100 text-purple-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {isWritingPillar ? 'Writing' : isCodingPillar ? 'Coding' : isImagePillar ? 'Image Generation' : pillar.cluster}
          </span>
          {pillar.priority && (
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              pillar.priority === 'High' ? 'bg-red-100 text-red-800' :
              pillar.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
              'bg-green-100 text-green-800'
            }`}>
              {pillar.priority} Priority
            </span>
          )}
        </div>
        <h1 className="text-4xl font-bold mb-4 text-gray-900">{pillar.title}</h1>
        <p className="text-xl text-gray-600 mb-4">{pillar.description}</p>
        {pillar.lastUpdated && (
          <p className="text-sm text-gray-500">
            Last updated: {pillar.lastUpdated}
          </p>
        )}
      </header>

      {/* Render content based on pillar type */}
      {isWritingPillar && <WritingPillarContent />}
      {isCodingPillar && <CodingPillarContent />}
      {isImagePillar && <ImagePillarContent />}
    </article>
  )
}

// Writing Pillar Content Component
function WritingPillarContent() {
  return (
    <div className="prose prose-lg max-w-none text-gray-900">
      {/* Quiz CTA */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Not sure which AI fits your workflow?
          </h3>
          <p className="text-gray-600 mb-4">
            Take our 30-second quiz to get a personalized recommendation
          </p>
          <a
            href="/quiz/writing"
            className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors"
          >
            Take the Writing Quiz →
          </a>
        </div>
      </div>

      <h2 className="text-3xl font-semibold mb-4 mt-8 text-gray-900">Who are you writing for?</h2>

      <div className="mb-6">
        <h3 className="text-xl font-semibold mb-2 text-gray-900">The Blogger</h3>
        <ul className="mb-4 pl-6 list-disc text-gray-800">
          <li><strong>Pain</strong> – needs original long-form content that won't feel robotic or earn an SEO penalty.</li>
          <li><strong>Ideal output</strong> – an AI blog generator that keeps a consistent tone.</li>
          <li><strong>Killer feature</strong> – a huge context window to track details across thousands of words.</li>
        </ul>
      </div>

      <div className="mb-6">
        <h3 className="text-xl font-semibold mb-2 text-gray-900">The Student</h3>
        <ul className="mb-4 pl-6 list-disc text-gray-800">
          <li><strong>Pain</strong> – must research, structure, and cite accurately while avoiding plagiarism.</li>
          <li><strong>Ideal output</strong> – an AI essay writer that returns verifiable facts with citations.</li>
          <li><strong>Killer feature</strong> – can ingest PDFs and analyse them directly.</li>
        </ul>
      </div>

      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-2 text-gray-900">The Marketer</h3>
        <ul className="mb-4 pl-6 list-disc text-gray-800">
          <li><strong>Pain</strong> – high-volume, mixed-format content plus brand-voice consistency.</li>
          <li><strong>Ideal output</strong> – a tool that plugs into Google Workspace and accelerates campaigns.</li>
          <li><strong>Killer feature</strong> – analyses spreadsheet data and builds project plans.</li>
        </ul>
      </div>

      <hr className="my-8" />

      <h2 className="text-3xl font-semibold mb-4 text-gray-900">A market of specialists, not one "best" model</h2>
      <p className="mb-4 text-gray-800">
        Perplexity is an <strong>answer engine</strong>, Claude a <strong>creative prose specialist</strong>, and Gemini a <strong>productivity layer</strong> for Docs, Sheets, and Gmail. The takeaway: <em>choose by task</em>, not by raw IQ.
      </p>

      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-8">
        <h3 className="text-lg font-semibold mb-2 text-gray-900">⚠ Premium trap</h3>
        <p className="text-gray-800">
          The $20 Pro tiers are no longer the cutting edge. OpenAI, Anthropic, Perplexity, and xAI now sell $100–$300 "Max / Heavy" plans aimed at enterprises. For most writers the $20 tier remains the ROI sweet-spot, but know you're not buying the absolute top model.
        </p>
      </div>

      <h2 className="text-3xl font-semibold mb-4 text-gray-900">2025 AI-writer scorecard</h2>

      <div className="overflow-x-auto mb-8">
        <table className="min-w-full border border-gray-300">
          <thead className="bg-gray-50">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Model</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Best for</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Stand-out feature</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Context window</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Free tier</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Pro price</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Key limitation</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>Claude 3.5 Sonnet</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Creative writing (Poet)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">"Artifacts" live editor</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">200k tokens</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Yes (daily cap)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">$20</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">No native real-time web search</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>GPT-4o</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Generalist (Polymath)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Multimodal + Custom GPTs</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">128k tokens</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Yes (cap)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">$20</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Output can feel robotic</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>Gemini Advanced</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Productivity (Producer)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Deep Workspace integration</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">1M+ tokens</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Yes (std)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">$19.99</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Creative flair weaker than Claude</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>Perplexity Pro</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Research (Professor)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Clickable citations, Deep Research</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">—</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Yes (cap)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">$20</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Not a creative writer</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>Grok</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Real-time insights (Provocateur)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Live X / Twitter data</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">—</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Yes (cap)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">$30</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Pricey; edgy tone not for all</td>
            </tr>
          </tbody>
        </table>
      </div>

      <p className="text-right text-sm mb-8">
        <a href="/export/scorecard.csv" className="text-blue-600 hover:text-blue-800 underline">Export to Sheets →</a>
      </p>

      <h2 className="text-3xl font-semibold mb-4 text-gray-900">Speed test ⚡</h2>
      <p className="mb-4 text-gray-600 italic">[Speed comparison GIF placeholder - GPT-4o vs Claude 3.5 vs Gemini]</p>
      <p className="mb-8 text-gray-800">
        GPT-4o starts generating sooner and streams tokens faster, a win for brainstorms and quick Q & A. Claude is ~2× faster than its predecessor Opus but still trails GPT-4o on sheer responsiveness.
      </p>

      <h2 className="text-3xl font-semibold mb-4 text-gray-900">Deep-dive profiles</h2>

      <h3 className="text-2xl font-semibold mb-3 text-gray-900">Claude 3.5 Sonnet — <em>the creative wordsmith</em></h3>
      <p className="mb-4 text-gray-800">
        <strong>Strengths.</strong> Thoughtful, expressive prose; 200k-token context; "Artifacts" side-panel for iterative editing.<br />
        <strong>Weaknesses.</strong> No built-in web browsing; free tier message cap.<br />
        <em>Read the full <a href="/claude-3-5-for-blogging-review" className="text-blue-600 hover:text-blue-800 underline">Claude 3.5 blogging review</a>.</em>
      </p>

      <h3 className="text-2xl font-semibold mb-3 text-gray-900">GPT-4o — <em>the versatile all-rounder</em></h3>
      <p className="mb-4 text-gray-800">
        Handles writing, code, data and images in one chat. Custom GPTs unlock niche workflows.<br />
        Criticisms: increasing verbosity, smaller window than Claude, privacy concerns.
      </p>

      <h3 className="text-2xl font-semibold mb-3 text-gray-900">Gemini Advanced — <em>the integrated productivity engine</em></h3>
      <p className="mb-4 text-gray-800">
        Native in Docs, Sheets, Gmail. Perfect for campaign tables, email summarising, Drive file look-ups.<br />
        Deep dive: <a href="/gemini-advanced-for-marketers-guide" className="text-blue-600 hover:text-blue-800 underline">Gemini for marketers</a>.
      </p>

      <h3 className="text-2xl font-semibold mb-3 text-gray-900">Perplexity Pro — <em>the research powerhouse</em></h3>
      <p className="mb-4 text-gray-800">
        Delivers answers with numbered citations; "Deep Research" builds exhaustive reports.<br />
        Guide: <a href="/how-to-use-perplexity-for-academic-research" className="text-blue-600 hover:text-blue-800 underline">How to use Perplexity for academic research</a>.
      </p>

      <h3 className="text-2xl font-semibold mb-3 text-gray-900">Grok — <em>the real-time provocateur</em></h3>
      <p className="mb-8 text-gray-800">
        Live social-media pulse plus a snarky attitude. Great for trend analysts, overkill for everyday writing.
      </p>

      {/* Quiz CTA */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Not sure which AI fits your workflow?
          </h3>
          <p className="text-gray-600 mb-4">
            Take our 30-second quiz to get a personalized recommendation
          </p>
          <a
            href="/quiz/writing"
            className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors"
          >
            Take the Writing Quiz →
          </a>
        </div>
      </div>
    </div>
  )
}

// Coding Pillar Content Component
function CodingPillarContent() {
  return (
    <div className="prose prose-lg max-w-none text-gray-900">
      {/* Quiz CTA */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 my-8">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Find Your Perfect AI Coding Assistant
          </h3>
          <p className="text-gray-600 mb-4">
            Take our developer quiz to get a personalized recommendation
          </p>
          <a
            href="/quiz/coding"
            className="inline-block bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-3 rounded-lg transition-colors"
          >
            Take the Coding Quiz →
          </a>
        </div>
      </div>

      <h2 className="text-3xl font-semibold mb-4 mt-8 text-gray-900">The AI Coding Landscape in 2025</h2>
      <p className="mb-4 text-gray-800">
        The conversation around AI coding assistants has moved far beyond simple autocomplete. In 2025, these tools are powerful collaborators capable of architecting systems, debugging multi-file repositories, and accelerating development cycles. But the fragmented market means the "best" AI is no longer a simple choice.
      </p>

      <h2 className="text-3xl font-semibold mb-4 text-gray-900">The AI Coder's Scorecard: Specs at a Glance</h2>
      <p className="mb-4 text-gray-800">
        For developers, specs matter. This chart breaks down the key models by what you care about most: cost, context, and core strengths.
      </p>

      <div className="overflow-x-auto mb-8">
        <table className="min-w-full border border-gray-300">
          <thead className="bg-gray-50">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Model</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Pricing (per user/month)</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Context Window</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Key Strength / Ecosystem</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>GPT-4o</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">~$20 (API is usage-based)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">128k tokens</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Versatility; a powerful "second brain" for logic and algorithms.</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>Claude 3.5 Sonnet</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">~$20 (API is usage-based)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">200k tokens</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Massive context for codebase analysis and complex refactoring.</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>GitHub Copilot</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">$19 (Business) / $39 (Enterprise)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Varies (uses GPT-4)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Deep integration with GitHub, VS Code, and the PR lifecycle.</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>Replit Ghostwriter</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">$20 (Pro) / $50 (Teams)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Varies</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Native to the Replit cloud IDE for seamless prototyping.</td>
            </tr>
          </tbody>
        </table>
      </div>

      <p className="text-right text-sm mb-8">
        <a href="/export/coding-scorecard.csv" className="text-blue-600 hover:text-blue-800 underline">Export to Sheets →</a>
      </p>

      <h2 className="text-3xl font-semibold mb-4 text-gray-900">The Code Challenge: Simple Bugs vs. High-Context Flaws</h2>
      <p className="mb-4 text-gray-800">
        Not all bugs are created equal. Some are simple typos, while others are subtle logical flaws that hide deep within a large codebase. We tested the leading models with two distinct challenges to see where they shine and where they falter.
      </p>

      <h3 className="text-2xl font-semibold mb-3 text-gray-900">Snippet 1: The Flawless Fix</h3>
      <p className="mb-4 text-gray-800">
        This simple Python function is meant to calculate the total price of items in a cart but has a common off-by-one error.
      </p>

      <div className="bg-gray-900 text-white p-4 rounded-lg overflow-x-auto mb-4">
        <pre className="text-sm text-green-400">
          <code>
{`def calculate_cart_total(prices):
  total = 0
  # Bug: range stops before the last index
  for i in range(len(prices) - 1):
    total += prices[i]
  return total

cart = [10, 25, 15, 5]
print(f"Total: $55")  # Should show calculate_cart_total(cart)
# Expected output: $55
# Actual output: $50`}
          </code>
        </pre>
      </div>

      <p className="mb-6 text-gray-800">
        <strong>Result:</strong> Every model tested—GPT-4o, Claude, Copilot, and Ghostwriter—fixed this instantly. They correctly identified that the loop failed to include the last item and adjusted <code className="bg-gray-100 px-2 py-1 rounded text-sm">range(len(prices) - 1)</code> to <code className="bg-gray-100 px-2 py-1 rounded text-sm">range(len(prices))</code>. This is the table-stakes capability you should expect from any modern AI code generator.
      </p>

      <h3 className="text-2xl font-semibold mb-3 text-gray-900">Snippet 2: The High-Context Challenge</h3>
      <p className="mb-4 text-gray-800">
        This is where premium models prove their worth. The bug here is subtle. A utility function <code className="bg-gray-100 px-2 py-1 rounded text-sm">process_data</code> incorrectly uses a global <code className="bg-gray-100 px-2 py-1 rounded text-sm">TRANSACTION_FEE</code> variable, but this is only apparent when you see how <code className="bg-gray-100 px-2 py-1 rounded text-sm">process_data</code> is called by another function that has already applied a separate, regional tax.
      </p>

      <div className="bg-gray-900 text-white p-4 rounded-lg overflow-x-auto mb-4">
        <pre className="text-sm text-yellow-400">
          <code>
{`// Defined 500 lines earlier...
const TRANSACTION_FEE = 0.02; // 2% processing fee

function process_data(items) {
  let subtotal = items.reduce((acc, item) => acc + item.price, 0);
  // Bug: This fee is applied redundantly
  return subtotal * (1 + TRANSACTION_FEE);
}

// ... much later in the file ...
function checkout_for_region(cart, region_config) {
  let regional_total = cart.reduce((acc, item) => acc + item.price, 0);
  regional_total *= (1 + region_config.tax_rate);

  // Send to processing, unaware that it adds another fee
  const final_price = process_data(cart);
  console.log("Final price is: " + final_price.toFixed(2));
}`}
          </code>
        </pre>
      </div>

      <p className="mb-6 text-gray-800">
        <strong>Result:</strong> Lower-Context Models typically suggest fixing <code className="bg-gray-100 px-2 py-1 rounded text-sm">process_data</code> in isolation. High-Context Models (Claude 3.5 Sonnet & GPT-4o) excelled by identifying the core issue and suggesting proper refactoring.
      </p>

      <h2 className="text-3xl font-semibold mb-4 text-gray-900">The Enterprise Developer's Checklist</h2>
      <p className="mb-4 text-gray-800">
        For teams, choosing an AI coding assistant involves more than just performance—it's about security, licensing, and integration.
      </p>

      <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-8">
        <ul className="space-y-2 text-gray-800">
          <li>☐ <strong>Data Privacy & Training:</strong> Zero-retention policy for proprietary code</li>
          <li>☐ <strong>Licensing & Indemnification:</strong> Clear ownership terms and IP protection</li>
          <li>☐ <strong>Seat Management & SSO:</strong> Central dashboard and Single Sign-On integration</li>
          <li>☐ <strong>Security Compliance:</strong> SOC 2 Type 2 compliance for enterprise environments</li>
          <li>☐ <strong>IDE & Toolchain Integration:</strong> First-party extensions for preferred IDEs</li>
        </ul>
      </div>

      <h2 className="text-3xl font-semibold mb-4 text-gray-900">Deep-dive profiles</h2>

      <h3 className="text-2xl font-semibold mb-3 text-gray-900">GPT-4o — <em>the versatile problem-solver</em></h3>
      <p className="mb-4 text-gray-800">
        <strong>Strengths.</strong> Excellent logical reasoning; handles multiple programming languages; strong algorithmic thinking.<br />
        <strong>Weaknesses.</strong> Smaller context window than Claude; can be verbose in explanations.<br />
        <em>Perfect for: General development, algorithm design, multi-language projects.</em>
      </p>

      <h3 className="text-2xl font-semibold mb-3 text-gray-900">Claude 3.5 Sonnet — <em>the codebase analyst</em></h3>
      <p className="mb-4 text-gray-800">
        <strong>Strengths.</strong> Massive 200k token context; excellent at understanding large file relationships; thoughtful refactoring suggestions.<br />
        <strong>Weaknesses.</strong> No native IDE integration yet; API-only access.<br />
        <em>Perfect for: Large codebase analysis, complex refactoring, architectural decisions.</em>
      </p>

      <h3 className="text-2xl font-semibold mb-3 text-gray-900">GitHub Copilot — <em>the workflow integrator</em></h3>
      <p className="mb-4 text-gray-800">
        <strong>Strengths.</strong> Seamless VS Code integration; understands Git context; PR and issue integration.<br />
        <strong>Weaknesses.</strong> Limited to GitHub ecosystem; enterprise pricing can be steep.<br />
        <em>Perfect for: GitHub-based teams, VS Code users, integrated development workflows.</em>
      </p>

      <h3 className="text-2xl font-semibold mb-3 text-gray-900">Replit Ghostwriter — <em>the rapid prototyper</em></h3>
      <p className="mb-8 text-gray-800">
        <strong>Strengths.</strong> Instant deployment; browser-based development; great for learning and experimentation.<br />
        <strong>Weaknesses.</strong> Limited to Replit environment; less suitable for complex enterprise projects.<br />
        <em>Perfect for: Rapid prototyping, educational projects, web-based development.</em>
      </p>

      {/* Quiz CTA */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 my-8">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Find Your Perfect AI Coding Assistant
          </h3>
          <p className="text-gray-600 mb-4">
            Take our developer quiz to get a personalized recommendation
          </p>
          <a
            href="/quiz/coding"
            className="inline-block bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-3 rounded-lg transition-colors"
          >
            Take the Coding Quiz →
          </a>
        </div>
      </div>
    </div>
  )
}

// Image Pillar Content Component
function ImagePillarContent() {
  return (
    <div className="prose prose-lg max-w-none text-gray-900">
      {/* Quiz CTA */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-6 my-8">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Find Your Perfect AI Image Generator
          </h3>
          <p className="text-gray-600 mb-4">
            Take our specialized quiz to get matched with the ideal tool for your creative needs
          </p>
          <a
            href="/quiz/image"
            className="inline-block bg-purple-600 hover:bg-purple-700 text-white font-medium px-6 py-3 rounded-lg transition-colors"
          >
            Take the Image Quiz →
          </a>
        </div>
      </div>

      <h2 className="text-3xl font-semibold mb-4 mt-8 text-gray-900">The New Creative Frontier</h2>
      <p className="mb-4 text-gray-800">
        AI image generation has moved beyond novelty and into a phase of serious professional integration. For game developers, digital artists, and marketers, these tools are no longer experimental toys but powerful assets capable of accelerating workflows, inspiring new concepts, and producing commercial-grade visuals.
      </p>
      <p className="mb-8 text-gray-800">
        The market has matured into a competitive landscape where the leading platforms—<strong>Midjourney, Stable Diffusion, Ideogram, and DALL-E</strong>—each champion a distinct approach to creation. This analysis reveals that there is no single "best" generator. The optimal choice balances <strong>Aesthetic Quality, Technical Control, Typographic Fidelity, and Commercial Viability</strong>.
      </p>

      <h2 className="text-3xl font-semibold mb-4 text-gray-900">The Gauntlet: A Unified Prompt Showdown</h2>
      <p className="mb-4 text-gray-800">
        To test the limits of each model, we used a single, complex prompt designed to evaluate photorealism, object detail, lighting, depth of field, and in-image text generation.
      </p>

      <div className="bg-gray-50 border-l-4 border-purple-400 p-4 mb-6">
        <p className="text-gray-800 italic">
          <strong>The Prompt:</strong> "Photorealistic product shot for a marketing campaign. A sleek, matte black bottle of high-end perfume named 'Etherea' sits on a wet, reflective marble surface. In the background, out of focus, are glowing neon orchids. The words 'Etherea: The Scent of Tomorrow' are elegantly displayed in a modern sans-serif font at the bottom."
        </p>
      </div>

      <h3 className="text-2xl font-semibold mb-3 text-gray-900">The Results: Side-by-Side Visual Analysis</h3>

      <div className="mb-6">
        <h4 className="text-xl font-semibold mb-2 text-gray-900">Midjourney v6</h4>
        <p className="mb-4 text-gray-800">
          Delivers unparalleled photorealism. The lighting on the wet marble, the texture of the matte bottle, and the soft bokeh of the neon orchids are exceptionally cinematic and "art-directed". However, it struggles with the text, rendering a stylized but illegible version of the brand name and tagline.
        </p>
      </div>

      <div className="mb-6">
        <h4 className="text-xl font-semibold mb-2 text-gray-900">Stable Diffusion XL Lightning</h4>
        <p className="mb-4 text-gray-800">
          Produces a high-quality image with remarkable speed, a testament to its efficient architecture. The composition is strong, but the fine details and lighting nuances lack the hyper-realistic polish of Midjourney. It represents a powerful balance of speed and quality, ideal for rapid iteration.
        </p>
      </div>

      <div className="mb-6">
        <h4 className="text-xl font-semibold mb-2 text-gray-900">Ideogram</h4>
        <p className="mb-4 text-gray-800">
          Excels where others fail. The text "Etherea: The Scent of Tomorrow" is rendered with near-perfect clarity and elegant composition, demonstrating its core strength. The surrounding image is competent but less photorealistic than Midjourney or DALL-E, appearing more like a high-quality digital illustration than a photograph.
        </p>
      </div>

      <div className="mb-8">
        <h4 className="text-xl font-semibold mb-2 text-gray-900">DALL-E 4 (via GPT-4o)</h4>
        <p className="mb-4 text-gray-800">
          Shows superior prompt comprehension. It successfully interprets and renders every element of the prompt correctly—the matte bottle, wet surface, neon bokeh, and the text. The integration with ChatGPT allows for this nuanced understanding. While the text is legible, Ideogram's is more typographically refined.
        </p>
      </div>

      <h2 className="text-3xl font-semibold mb-4 text-gray-900">Prompt Showdown Scorecard</h2>

      <div className="overflow-x-auto mb-8">
        <table className="min-w-full border border-gray-300">
          <thead className="bg-gray-50">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Platform</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Photorealism (1-5)</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Prompt Adherence (1-5)</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Typography (1-5)</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Overall Aesthetic (1-5)</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Verdict</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>Midjourney v6</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">5</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">4</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">2</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">5</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">The Artist: Unmatched beauty, but can't write.</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>SDXL Lightning</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">4</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">4</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">2</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">4</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">The Engineer: Fast and capable, a workhorse.</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>Ideogram</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">3</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">4</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">5</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">4</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">The Typographer: Flawless text, good-enough image.</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>DALL-E 4</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">4</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">5</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">4</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">4</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">The Co-Creator: Understands everything, a true generalist.</td>
            </tr>
          </tbody>
        </table>
      </div>

      <p className="text-right text-sm mb-8">
        <a href="/export/image-scorecard.csv" className="text-purple-600 hover:text-purple-800 underline">Export to Sheets →</a>
      </p>

      <h2 className="text-3xl font-semibold mb-4 text-gray-900">The Business Imperative: Cost vs. Commercial Rights</h2>
      <p className="mb-4 text-gray-800">
        Choosing a platform involves more than just creative output; it requires a careful analysis of cost and legal considerations.
      </p>

      <h3 className="text-2xl font-semibold mb-3 text-gray-900">Economic Analysis: The Cost of 1,000 Images</h3>
      <p className="mb-4 text-gray-800">
        The cost per image varies dramatically depending on the platform's pricing model—subscription, credits, or pay-per-image API calls.
      </p>

      <div className="overflow-x-auto mb-8">
        <table className="min-w-full border border-gray-300">
          <thead className="bg-gray-50">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Platform</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Recommended Plan/Method</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Total Cost for 1,000 Images</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Effective Cost-per-Image</th>
              <th className="border border-gray-300 px-4 py-2 text-left text-gray-900">Key Considerations</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>Midjourney</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Standard Plan ($30/mo)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">~$30</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">~$0.03</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Subscription includes ~900 fast generations.</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>SDXL Lightning</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Replicate API</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">~$1.40</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">~$0.0014</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Requires technical setup; API pricing varies by provider.</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>Ideogram</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Plus Plan ($16/mo, billed yearly)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">~$16</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">~$0.016</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Includes 1,000 priority credits/month.</td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-4 py-2 text-gray-800"><strong>DALL-E 4 (API)</strong></td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">OpenAI API (DALL-E 3)</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">~$40</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">$0.04</td>
              <td className="border border-gray-300 px-4 py-2 text-gray-800">Pay-as-you-go; price is for standard quality.</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Quiz CTA */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-6 my-8">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Find Your Perfect AI Image Generator
          </h3>
          <p className="text-gray-600 mb-4">
            Take our specialized quiz to get matched with the ideal tool for your creative needs
          </p>
          <a
            href="/quiz/image"
            className="inline-block bg-purple-600 hover:bg-purple-700 text-white font-medium px-6 py-3 rounded-lg transition-colors"
          >
            Take the Image Quiz →
          </a>
        </div>
      </div>
    </div>
  )
}
