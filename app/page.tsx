import { allPillars } from 'contentlayer/generated'
import Link from 'next/link'

export default function Home() {
  return (
    <div className="max-w-6xl mx-auto px-4 py-12">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className="text-5xl font-bold mb-4 text-gray-900">
          Find Your Perfect AI Tool
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
          Comprehensive comparisons and personalized recommendations for AI writing assistants, coding tools, and more.
          Take our smart quizzes to get matched with the perfect AI for your workflow.
        </p>

        {/* Quick Quiz CTAs */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 justify-center mb-12">
          <Link
            href="/quiz/writing"
            className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors text-center justify-center"
          >
            📝 Writing Quiz
          </Link>
          <Link
            href="/quiz/coding"
            className="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors text-center justify-center"
          >
            💻 Coding Quiz
          </Link>
          <Link
            href="/quiz/image"
            className="inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors text-center justify-center"
          >
            🎨 Image Quiz
          </Link>
          <Link
            href="/quiz/video"
            className="inline-flex items-center px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors text-center justify-center"
          >
            🎬 Video Quiz
          </Link>
        </div>
      </div>

      {/* Pillar Guides Section */}
      <div className="mb-8">
        <h2 className="text-3xl font-bold mb-4 text-gray-900">Comprehensive AI Tool Guides</h2>
        <p className="text-lg text-gray-600 mb-8">
          Deep-dive comparisons with real-world testing, pricing analysis, and expert recommendations.
        </p>
      </div>
      
      <div className="grid gap-8 md:grid-cols-2">
        {allPillars.map((pillar) => {
          const isWriting = pillar.cluster === 'text' || pillar.slug.includes('writing')
          const isCoding = pillar.cluster === 'code' || pillar.slug.includes('coding')
          const isImage = pillar.cluster === 'image' || pillar.slug.includes('image')
          const isVideo = pillar.cluster === 'video' || pillar.slug.includes('video')

          return (
            <div
              key={pillar.slug}
              className="bg-white border border-gray-200 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
            >
              {/* Header with gradient */}
              <div className={`p-6 ${
                isWriting ? 'bg-gradient-to-r from-blue-50 to-indigo-50' :
                isCoding ? 'bg-gradient-to-r from-green-50 to-blue-50' :
                isImage ? 'bg-gradient-to-r from-purple-50 to-pink-50' :
                isVideo ? 'bg-gradient-to-r from-red-50 to-orange-50' :
                'bg-gradient-to-r from-gray-50 to-slate-50'
              }`}>
                <div className="flex items-center justify-between mb-3">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    isWriting ? 'bg-blue-100 text-blue-800' :
                    isCoding ? 'bg-green-100 text-green-800' :
                    isImage ? 'bg-purple-100 text-purple-800' :
                    isVideo ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {isWriting ? '📝 Writing' : isCoding ? '💻 Coding' : isImage ? '🎨 Image Generation' : isVideo ? '🎬 Video Generation' : pillar.cluster}
                  </span>
                  {pillar.priority && (
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      pillar.priority === 'High' ? 'bg-red-100 text-red-800' :
                      pillar.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {pillar.priority} Priority
                    </span>
                  )}
                </div>
                <h2 className="text-2xl font-bold mb-3 text-gray-900">{pillar.title}</h2>
                <p className="text-gray-600 mb-4 leading-relaxed">{pillar.description}</p>
              </div>

              {/* Action buttons */}
              <div className="p-6 bg-white">
                <div className="flex flex-col sm:flex-row gap-3">
                  <Link
                    href={pillar.url}
                    className={`flex-1 text-center px-4 py-2 rounded-lg font-medium transition-colors ${
                      isWriting ? 'bg-blue-600 hover:bg-blue-700 text-white' :
                      isCoding ? 'bg-green-600 hover:bg-green-700 text-white' :
                      isImage ? 'bg-purple-600 hover:bg-purple-700 text-white' :
                      isVideo ? 'bg-red-600 hover:bg-red-700 text-white' :
                      'bg-gray-600 hover:bg-gray-700 text-white'
                    }`}
                  >
                    Read Full Guide →
                  </Link>
                  <Link
                    href={isWriting ? '/quiz/writing' : isCoding ? '/quiz/coding' : isImage ? '/quiz/image' : isVideo ? '/quiz/video' : '#'}
                    className={`flex-1 text-center px-4 py-2 rounded-lg font-medium border-2 transition-colors ${
                      isWriting ? 'border-blue-600 text-blue-600 hover:bg-blue-50' :
                      isCoding ? 'border-green-600 text-green-600 hover:bg-green-50' :
                      isImage ? 'border-purple-600 text-purple-600 hover:bg-purple-50' :
                      isVideo ? 'border-red-600 text-red-600 hover:bg-red-50' :
                      'border-gray-600 text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    Take Quiz 🎯
                  </Link>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Bottom CTA Section */}
      <div className="mt-16 text-center bg-gradient-to-r from-blue-50 to-green-50 rounded-xl p-8">
        <h3 className="text-2xl font-bold mb-4 text-gray-900">
          Not Sure Where to Start?
        </h3>
        <p className="text-lg text-gray-600 mb-6">
          Our intelligent quizzes analyze your specific needs and recommend the perfect AI tool in under 30 seconds.
        </p>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 justify-center">
          <Link
            href="/quiz/writing"
            className="inline-flex items-center px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-bold rounded-lg transition-colors text-lg justify-center"
          >
            📝 Writing AI
          </Link>
          <Link
            href="/quiz/coding"
            className="inline-flex items-center px-8 py-4 bg-green-600 hover:bg-green-700 text-white font-bold rounded-lg transition-colors text-lg justify-center"
          >
            💻 Coding AI
          </Link>
          <Link
            href="/quiz/image"
            className="inline-flex items-center px-8 py-4 bg-purple-600 hover:bg-purple-700 text-white font-bold rounded-lg transition-colors text-lg justify-center"
          >
            🎨 Image AI
          </Link>
          <Link
            href="/quiz/video"
            className="inline-flex items-center px-8 py-4 bg-red-600 hover:bg-red-700 text-white font-bold rounded-lg transition-colors text-lg justify-center"
          >
            🎬 Video AI
          </Link>
        </div>
      </div>
    </div>
  )
}
