import { notFound } from 'next/navigation'
import Link from 'next/link'

interface RecommendPageProps {
  params: {
    model: string
  }
}

// Model configurations
const modelConfigs = {
  claude: {
    name: 'Claude 3.5 Sonnet',
    tagline: 'The Creative Wordsmith',
    description: 'Perfect for bloggers and creative writers who need human-like, nuanced prose with a huge context window.',
    strengths: [
      'Thoughtful, expressive writing style',
      '200k token context window',
      '"Artifacts" live editor for iterative editing',
      'Excellent for long-form content'
    ],
    weaknesses: [
      'No built-in web browsing',
      'Free tier has daily message caps'
    ],
    pricing: '$20/month (Pro)',
    freeOption: 'Yes (with daily limits)',
    bestFor: 'Blog posts, creative writing, long-form articles',
    ctaUrl: 'https://claude.ai',
    ctaText: 'Try Claude 3.5 Sonnet',
    color: 'orange'
  },
  gpt: {
    name: 'GPT-4o',
    tagline: 'The Versatile All-Rounder',
    description: 'The most versatile AI that handles writing, code, data, and images in one chat. Custom GPTs unlock specialized workflows.',
    strengths: [
      'Multimodal capabilities (text, images, code)',
      'Custom GPTs for specialized tasks',
      'Fast response times',
      'Broad knowledge base'
    ],
    weaknesses: [
      'Can feel robotic at times',
      'Smaller context window than Claude',
      'Privacy concerns for some users'
    ],
    pricing: '$20/month (Plus)',
    freeOption: 'Yes (with usage limits)',
    bestFor: 'General writing, technical content, mixed-format projects',
    ctaUrl: 'https://chat.openai.com',
    ctaText: 'Try GPT-4o',
    color: 'green'
  },
  gemini: {
    name: 'Gemini Advanced',
    tagline: 'The Integrated Productivity Engine',
    description: 'Native integration with Google Workspace makes it perfect for marketers who need seamless workflow integration.',
    strengths: [
      'Deep Google Workspace integration',
      'Massive 1M+ token context window',
      'Perfect for campaign planning',
      'Email and document analysis'
    ],
    weaknesses: [
      'Creative flair weaker than Claude',
      'Less specialized for pure writing'
    ],
    pricing: '$19.99/month (Advanced)',
    freeOption: 'Yes (standard version)',
    bestFor: 'Marketing campaigns, Google Workspace users, data analysis',
    ctaUrl: 'https://gemini.google.com',
    ctaText: 'Try Gemini Advanced',
    color: 'blue'
  },
  perplexity: {
    name: 'Perplexity Pro',
    tagline: 'The Research Powerhouse',
    description: 'The ultimate research assistant that delivers answers with clickable citations and builds comprehensive reports.',
    strengths: [
      'Clickable citations and sources',
      'Deep Research feature',
      'Real-time web access',
      'Academic-grade accuracy'
    ],
    weaknesses: [
      'Not optimized for creative writing',
      'Limited style customization'
    ],
    pricing: '$20/month (Pro)',
    freeOption: 'Yes (with search limits)',
    bestFor: 'Research papers, fact-checking, academic writing',
    ctaUrl: 'https://perplexity.ai',
    ctaText: 'Try Perplexity Pro',
    color: 'purple'
  },
  grok: {
    name: 'Grok',
    tagline: 'The Real-Time Provocateur',
    description: 'Access to live X/Twitter data with a snarky attitude. Great for trend analysis and real-time insights.',
    strengths: [
      'Live social media data access',
      'Real-time trend analysis',
      'Unique personality and tone',
      'Current events awareness'
    ],
    weaknesses: [
      'More expensive than alternatives',
      'Edgy tone not suitable for all contexts',
      'Limited creative writing capabilities'
    ],
    pricing: '$30/month (Premium)',
    freeOption: 'Yes (with limits)',
    bestFor: 'Social media analysis, trend research, current events',
    ctaUrl: 'https://x.ai',
    ctaText: 'Try Grok',
    color: 'gray'
  },
  copilot: {
    name: 'GitHub Copilot',
    tagline: 'The Workflow Integrator',
    description: 'Seamlessly integrated AI coding assistant that understands your GitHub workflow, repositories, and development context.',
    strengths: [
      'Deep VS Code and JetBrains integration',
      'Understands Git context and history',
      'PR and issue integration',
      'Enterprise-grade security and compliance',
      'Supports 30+ programming languages'
    ],
    weaknesses: [
      'Limited to GitHub ecosystem',
      'Enterprise pricing can be steep',
      'Requires GitHub account and repositories'
    ],
    pricing: '$19/month (Business) / $39/month (Enterprise)',
    freeOption: 'Yes (for students and open-source)',
    bestFor: 'GitHub-based teams, VS Code users, enterprise development',
    ctaUrl: 'https://github.com/features/copilot',
    ctaText: 'Try GitHub Copilot',
    color: 'indigo'
  },
  replit: {
    name: 'Replit Ghostwriter',
    tagline: 'The Rapid Prototyper',
    description: 'AI coding assistant built into the Replit cloud IDE for instant development, deployment, and collaboration.',
    strengths: [
      'Instant deployment and hosting',
      'Browser-based development environment',
      'Great for learning and experimentation',
      'Real-time collaboration features',
      'No local setup required'
    ],
    weaknesses: [
      'Limited to Replit environment',
      'Less suitable for complex enterprise projects',
      'Smaller context window than dedicated models'
    ],
    pricing: '$20/month (Pro) / $50/month (Teams)',
    freeOption: 'Yes (with usage limits)',
    bestFor: 'Rapid prototyping, educational projects, web development',
    ctaUrl: 'https://replit.com/ai',
    ctaText: 'Try Replit Ghostwriter',
    color: 'teal'
  }
}

export async function generateStaticParams() {
  return Object.keys(modelConfigs).map((model) => ({
    model,
  }))
}

export default function RecommendPage({ params }: RecommendPageProps) {
  const config = modelConfigs[params.model as keyof typeof modelConfigs]

  if (!config) {
    notFound()
  }

  const colorClasses = {
    orange: 'from-orange-500 to-red-500',
    green: 'from-green-500 to-emerald-500',
    blue: 'from-blue-500 to-indigo-500',
    purple: 'from-purple-500 to-pink-500',
    gray: 'from-gray-500 to-slate-500',
    indigo: 'from-indigo-500 to-purple-500',
    teal: 'from-teal-500 to-cyan-500'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-4xl mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="mb-6">
            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${colorClasses[config.color as keyof typeof colorClasses]} text-white text-2xl font-bold mb-4`}>
              {config.name.charAt(0)}
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Perfect Match: {config.name}
          </h1>
          <p className="text-xl text-gray-600 mb-2">{config.tagline}</p>
          <p className="text-lg text-gray-700 max-w-2xl mx-auto">
            {config.description}
          </p>
        </div>

        {/* Main Content */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {/* Strengths */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4 flex items-center">
              <span className="text-green-500 mr-2">✓</span>
              Strengths
            </h2>
            <ul className="space-y-2">
              {config.strengths.map((strength, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-green-500 mr-2 mt-1">•</span>
                  <span className="text-gray-700">{strength}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Considerations */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4 flex items-center">
              <span className="text-yellow-500 mr-2">⚠</span>
              Considerations
            </h2>
            <ul className="space-y-2">
              {config.weaknesses.map((weakness, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-yellow-500 mr-2 mt-1">•</span>
                  <span className="text-gray-700">{weakness}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Pricing & Details */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <div className="grid md:grid-cols-3 gap-6 text-center">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Pricing</h3>
              <p className="text-2xl font-bold text-blue-600">{config.pricing}</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Free Option</h3>
              <p className="text-lg text-gray-700">{config.freeOption}</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Best For</h3>
              <p className="text-lg text-gray-700">{config.bestFor}</p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mb-8">
          <a
            href={config.ctaUrl}
            target="_blank"
            rel="noopener noreferrer"
            className={`inline-block bg-gradient-to-r ${colorClasses[config.color as keyof typeof colorClasses]} text-white font-bold py-4 px-8 rounded-lg text-lg hover:shadow-lg transition-all duration-200 transform hover:scale-105`}
          >
            {config.ctaText} →
          </a>
          <p className="text-sm text-gray-600 mt-2">
            Start with the free version, upgrade when ready
          </p>
        </div>

        {/* Back to Guides */}
        <div className="text-center space-y-2">
          <div>
            <Link
              href="/best-ai-for-writing-2025-claude-3-5-vs-gpt-4o-gemini-more"
              className="text-blue-600 hover:text-blue-800 font-medium mr-6"
            >
              ← AI Writing Guide
            </Link>
            <Link
              href="/best-ai-for-coding-2025-gpt-4o-vs-claude-github-copilot-more"
              className="text-green-600 hover:text-green-800 font-medium"
            >
              ← AI Coding Guide
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
