'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { questions, scoreMatrix, overrides, modelNames, type AnswerKey } from './quizConfig'

export default function VideoQuizPage() {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState<Record<string, AnswerKey>>({})
  const [isComplete, setIsComplete] = useState(false)
  const router = useRouter()

  const handleAnswer = (answerKey: AnswerKey) => {
    const questionId = questions[currentQuestion].id
    const newAnswers = { ...answers, [questionId]: answerKey }
    setAnswers(newAnswers)

    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
    } else {
      // Quiz complete, calculate result
      const result = calculateResult(newAnswers)
      router.push(`/recommend/${result}`)
    }
  }

  const calculateResult = (userAnswers: Record<string, AnswerKey>): string => {
    // Check for override conditions first
    for (const override of overrides) {
      if (userAnswers[override.questionId] === override.answerKey) {
        return override.result
      }
    }

    // Calculate scores for each model
    const scores: Record<string, number> = {
      capcut: 0,
      veed: 0,
      runway: 0,
      pika: 0,
      descript: 0,
      captions: 0,
    }

    Object.entries(userAnswers).forEach(([questionId, answerKey]) => {
      const questionScores = scoreMatrix[answerKey]
      Object.entries(questionScores).forEach(([model, points]) => {
        scores[model] += points
      })
    })

    // Return the model with the highest score
    return Object.entries(scores).reduce((a, b) => (scores[a[0]] > scores[b[0]] ? a : b))[0]
  }

  const goBack = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1)
    }
  }

  const progress = ((currentQuestion + 1) / questions.length) * 100

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50">
      <div className="max-w-2xl mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent">
            AI Video Generator Quiz
          </h1>
          <p className="text-lg text-gray-600">
            Find your perfect AI video creation tool in 5 questions
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between text-sm text-gray-500 mb-2">
            <span>Question {currentQuestion + 1} of {questions.length}</span>
            <span>{Math.round(progress)}% complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-red-500 to-orange-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Question Card */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-6">
          <h2 className="text-2xl font-semibold mb-6 text-gray-900">
            {questions[currentQuestion].text}
          </h2>
          
          <div className="space-y-4">
            {questions[currentQuestion].answers.map((answer) => (
              <button
                key={answer.key}
                onClick={() => handleAnswer(answer.key)}
                className="w-full text-left p-4 rounded-lg border-2 border-gray-200 hover:border-red-300 hover:bg-red-50 transition-all duration-200 group"
              >
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full border-2 border-gray-300 group-hover:border-red-500 flex items-center justify-center mr-4 transition-colors">
                    <span className="text-sm font-medium text-gray-500 group-hover:text-red-600">
                      {answer.key}
                    </span>
                  </div>
                  <span className="text-gray-700 group-hover:text-gray-900 font-medium">
                    {answer.label}
                  </span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <button
            onClick={goBack}
            disabled={currentQuestion === 0}
            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
              currentQuestion === 0
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            ← Previous
          </button>
          
          <div className="text-sm text-gray-500 flex items-center">
            Click an answer to continue
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-12 text-sm text-gray-500">
          <p>
            This quiz analyzes your content type, output frequency, collaboration needs, 
            and technical skills to recommend the best AI video tool for your workflow.
          </p>
        </div>
      </div>
    </div>
  )
}
