export type AnswerKey = 'A' | 'B' | 'C' | 'D'

export interface Question {
  id: string
  text: string
  answers: { key: Answer<PERSON>ey; label: string }[]
}

// Video generation quiz questions
export const questions: Question[] = [
  {
    id: 'content_type',
    text: 'What type of video content do you primarily create?',
    answers: [
      { key: 'A', label: 'Social media content (TikTok, Instagram Reels, YouTube Shorts)' },
      { key: 'B', label: 'Professional/corporate videos (marketing, training, presentations)' },
      { key: 'C', label: 'Creative/artistic projects (films, music videos, experiments)' },
      { key: 'D', label: 'Educational content (tutorials, podcasts, webinars)' },
    ],
  },
  {
    id: 'output_frequency',
    text: 'How often do you need to create video content?',
    answers: [
      { key: 'A', label: 'Daily or multiple times per day (high-volume social media)' },
      { key: 'B', label: 'Weekly (regular content schedule)' },
      { key: 'C', label: 'Monthly or project-based (quality over quantity)' },
      { key: 'D', label: 'Occasionally (as needed for specific campaigns)' },
    ],
  },
  {
    id: 'collaboration_needs',
    text: 'Do you work with a team on video projects?',
    answers: [
      { key: 'A', label: 'Yes, I need strong collaboration and brand consistency tools' },
      { key: 'B', label: 'Sometimes, basic sharing and feedback features are helpful' },
      { key: 'C', label: 'No, I work solo and prefer simple, fast tools' },
    ],
  },
  {
    id: 'technical_skill',
    text: 'What is your video editing experience level?',
    answers: [
      { key: 'A', label: 'Beginner - I want automated, template-based solutions' },
      { key: 'B', label: 'Intermediate - I can handle some complexity for better results' },
      { key: 'C', label: 'Advanced - I want maximum control and professional features' },
    ],
  },
  {
    id: 'budget_constraints',
    text: 'What is your budget for AI video tools?',
    answers: [
      { key: 'A', label: 'Free or very low cost (under $15/month)' },
      { key: 'B', label: 'Moderate budget ($15-40/month)' },
      { key: 'C', label: 'Professional budget ($40+ or pay-per-use)' },
    ],
  },
]

// Scoring matrix for video generation AI recommendations
export const scoreMatrix: Record<AnswerKey, { [model: string]: number }> = {
  A: { capcut: 2, veed: 1, runway: 0, pika: 1, descript: 0, captions: 2 },
  B: { capcut: 1, veed: 2, runway: 1, pika: 0, descript: 2, captions: 1 },
  C: { capcut: 0, veed: 1, runway: 2, pika: 2, descript: 1, captions: 0 },
  D: { capcut: 0, veed: 1, runway: 0, pika: 0, descript: 2, captions: 1 },
}

// Override rules for critical requirements
export const overrides = [
  {
    questionId: 'collaboration_needs',
    answerKey: 'A' as AnswerKey,
    result: 'veed', // Strong collaboration needs → Veed.io
  },
  {
    questionId: 'content_type',
    answerKey: 'D' as AnswerKey,
    result: 'descript', // Educational/podcast content → Descript
  },
  {
    questionId: 'output_frequency',
    answerKey: 'A' as AnswerKey,
    result: 'capcut', // Daily high-volume → CapCut
  },
  {
    questionId: 'technical_skill',
    answerKey: 'C' as AnswerKey,
    result: 'runway', // Advanced users → Runway Gen-3
  },
]

// Model display names for video generation tools
export const modelNames: Record<string, string> = {
  capcut: 'CapCut',
  veed: 'Veed.io',
  runway: 'Runway Gen-3',
  pika: 'Pika',
  descript: 'Descript',
  captions: 'Captions.ai',
}
