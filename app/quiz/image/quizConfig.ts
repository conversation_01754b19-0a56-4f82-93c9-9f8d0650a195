export type AnswerKey = 'A' | 'B' | 'C' | 'D'

export interface Question {
  id: string
  text: string
  answers: { key: Answer<PERSON>ey; label: string }[]
}

// Image generation quiz questions
export const questions: Question[] = [
  {
    id: 'use_case',
    text: 'What is your primary use case for AI image generation?',
    answers: [
      { key: 'A', label: 'Game development (concept art, textures, characters)' },
      { key: 'B', label: 'Digital art and illustration' },
      { key: 'C', label: 'Marketing and social media content' },
      { key: 'D', label: 'General creative projects and experimentation' },
    ],
  },
  {
    id: 'text_requirements',
    text: 'How important is accurate text rendering in your images?',
    answers: [
      { key: 'A', label: 'Essential - I need logos, signs, and readable text' },
      { key: 'B', label: 'Occasionally useful for certain projects' },
      { key: 'C', label: 'Not important - I rarely need text in images' },
    ],
  },
  {
    id: 'technical_skill',
    text: 'What is your technical comfort level?',
    answers: [
      { key: 'A', label: 'Begin<PERSON> - I want simple, user-friendly tools' },
      { key: 'B', label: 'Intermediate - I can handle some technical setup' },
      { key: 'C', label: 'Advanced - I want maximum control and customization' },
    ],
  },
  {
    id: 'budget_constraints',
    text: 'What is your budget for AI image generation?',
    answers: [
      { key: 'A', label: 'Free or very low cost (under $10/month)' },
      { key: 'B', label: 'Moderate budget ($10-30/month)' },
      { key: 'C', label: 'Professional budget ($30+ or pay-per-use)' },
    ],
  },
  {
    id: 'commercial_needs',
    text: 'Do you need to use generated images commercially?',
    answers: [
      { key: 'A', label: 'Yes, for business/client work with high revenue' },
      { key: 'B', label: 'Yes, for small business or personal projects' },
      { key: 'C', label: 'No, just for personal use and experimentation' },
    ],
  },
]

// Scoring matrix for image generation AI recommendations
export const scoreMatrix: Record<AnswerKey, { [model: string]: number }> = {
  A: { midjourney: 2, stable_diffusion: 1, ideogram: 1, dalle: 1 },
  B: { midjourney: 1, stable_diffusion: 1, ideogram: 1, dalle: 2 },
  C: { midjourney: 1, stable_diffusion: 2, ideogram: 2, dalle: 1 },
  D: { midjourney: 1, stable_diffusion: 0, ideogram: 0, dalle: 2 },
}

// Override rules for critical requirements
export const overrides = [
  {
    questionId: 'text_requirements',
    answerKey: 'A' as AnswerKey,
    result: 'ideogram', // Essential text needs → Ideogram
  },
  {
    questionId: 'budget_constraints',
    answerKey: 'A' as AnswerKey,
    result: 'stable_diffusion', // Very low budget → Stable Diffusion (free/open source)
  },
  {
    questionId: 'technical_skill',
    answerKey: 'C' as AnswerKey,
    result: 'stable_diffusion', // Advanced users → Stable Diffusion (maximum control)
  },
]

// Model display names for image generation tools
export const modelNames: Record<string, string> = {
  midjourney: 'Midjourney',
  stable_diffusion: 'Stable Diffusion',
  ideogram: 'Ideogram',
  dalle: 'DALL-E',
}
