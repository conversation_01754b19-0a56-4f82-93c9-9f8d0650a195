export type AnswerKey = 'A' | 'B' | 'C' | 'D'

export interface Question {
  id: string
  text: string
  answers: { key: Answer<PERSON>ey; label: string }[]
}

// 1 ⬇ Questions
export const questions: Question[] = [
  {
    id: 'objective',
    text: 'What is your primary writing task today?',
    answers: [
      { key: 'A', label: 'Blog / long-form article' },
      { key: 'B', label: 'Academic / research paper' },
      { key: 'C', label: 'Social-media copy' },
      { key: 'D', label: 'Email / outreach' },
    ],
  },
  {
    id: 'importance_citations',
    text: 'How important are verifiable sources & citations?',
    answers: [
      { key: 'A', label: 'Absolutely essential' },
      { key: 'B', label: 'Nice to have' },
      { key: 'C', label: 'Not important' },
    ],
  },
  {
    id: 'writing_style',
    text: 'Which best describes your ideal writing style?',
    answers: [
      { key: 'A', label: 'Human-like, nuanced, creative' },
      { key: 'B', label: 'Factual, direct, academic' },
      { key: 'C', label: 'Clear, concise, professional' },
    ],
  },
  {
    id: 'budget',
    text: 'Budget?',
    answers: [
      { key: 'A', label: 'Free only' },
      { key: 'B', label: '≈ $20/mo is fine' },
    ],
  },
  {
    id: 'need_integration',
    text: 'Do you need direct integration with Google Docs / Gmail?',
    answers: [
      { key: 'A', label: 'Yes' },
      { key: 'B', label: 'No' },
    ],
  },
]

// 2 ⬇ Scoring matrix
export const scoreMatrix: Record<
  AnswerKey,
  { [model: string]: number }
> = {
  A: { claude: 2, perplexity: 1, gemini: 0, gpt: 1, grok: 0 },
  B: { claude: 0, perplexity: 3, gemini: 1, gpt: 1, grok: 0 },
  C: { claude: 1, perplexity: 0, gemini: 2, gpt: 1, grok: 0 },
  D: { claude: 0, perplexity: 0, gemini: 1, gpt: 2, grok: 0 },
}

// Optional: hard rules override – e.g., if citations absolutely essential:
export const overrides = [
  {
    questionId: 'importance_citations',
    answerKey: 'A' as AnswerKey,
    result: 'perplexity',
  },
  {
    questionId: 'need_integration',
    answerKey: 'A' as AnswerKey,
    result: 'gemini',
  },
]

// Model display names for results
export const modelNames: Record<string, string> = {
  claude: 'Claude 3.5 Sonnet',
  gpt: 'GPT-4o',
  gemini: 'Gemini Advanced',
  perplexity: 'Perplexity Pro',
  grok: 'Grok',
}
