export type AnswerKey = 'A' | 'B' | 'C' | 'D'

export interface Question {
  id: string
  text: string
  answers: { key: Answer<PERSON>ey; label: string }[]
}

// Coding-specific quiz questions
export const questions: Question[] = [
  {
    id: 'primary_language',
    text: 'What is your primary programming language?',
    answers: [
      { key: 'A', label: 'Python (data science, web, automation)' },
      { key: 'B', label: 'JavaScript/TypeScript (web development)' },
      { key: 'C', label: 'Java/C# (enterprise, backend)' },
      { key: 'D', label: 'Multiple languages / polyglot' },
    ],
  },
  {
    id: 'project_type',
    text: 'What type of projects do you primarily work on?',
    answers: [
      { key: 'A', label: 'Large enterprise applications' },
      { key: 'B', label: 'Web apps and APIs' },
      { key: 'C', label: 'Scripts and automation' },
      { key: 'D', label: 'Rapid prototypes and experiments' },
    ],
  },
  {
    id: 'team_size',
    text: 'What best describes your development environment?',
    answers: [
      { key: 'A', label: 'Solo developer / freelancer' },
      { key: 'B', label: 'Small team (2-10 developers)' },
      { key: 'C', label: 'Large enterprise team (10+ developers)' },
    ],
  },
  {
    id: 'ide_preference',
    text: 'What is your preferred development environment?',
    answers: [
      { key: 'A', label: 'VS Code' },
      { key: 'B', label: 'JetBrains IDEs (IntelliJ, PyCharm, etc.)' },
      { key: 'C', label: 'Browser-based / cloud IDEs' },
      { key: 'D', label: 'Terminal / Vim / Emacs' },
    ],
  },
  {
    id: 'codebase_complexity',
    text: 'How would you describe your typical codebase?',
    answers: [
      { key: 'A', label: 'Simple scripts and small projects' },
      { key: 'B', label: 'Medium-sized applications (1000s of lines)' },
      { key: 'C', label: 'Large, complex codebases (10k+ lines, multiple files)' },
    ],
  },
]

// Scoring matrix for coding AI recommendations
export const scoreMatrix: Record<AnswerKey, { [model: string]: number }> = {
  A: { gpt: 1, claude: 2, copilot: 1, replit: 0 },
  B: { gpt: 2, claude: 1, copilot: 2, replit: 1 },
  C: { gpt: 1, claude: 1, copilot: 3, replit: 0 },
  D: { gpt: 2, claude: 1, copilot: 1, replit: 2 },
}

// Override rules for critical requirements
export const overrides = [
  {
    questionId: 'team_size',
    answerKey: 'C' as AnswerKey,
    result: 'copilot', // Enterprise teams → GitHub Copilot
  },
  {
    questionId: 'ide_preference',
    answerKey: 'C' as AnswerKey,
    result: 'replit', // Browser-based → Replit Ghostwriter
  },
  {
    questionId: 'codebase_complexity',
    answerKey: 'C' as AnswerKey,
    result: 'claude', // Large codebases → Claude 3.5 (large context)
  },
]

// Model display names for coding tools
export const modelNames: Record<string, string> = {
  gpt: 'GPT-4o',
  claude: 'Claude 3.5 Sonnet',
  copilot: 'GitHub Copilot',
  replit: 'Replit Ghostwriter',
}
