'use client'

import { useState } from 'react'
import { questions, scoreMatrix, overrides, modelNames, type AnswerKey } from './quizConfig'
import { useRouter } from 'next/navigation'

export default function CodingQuiz() {
  const [step, setStep] = useState(0)
  const [answers, setAnswers] = useState<Record<string, AnswerKey>>({})
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const currentQ = questions[step]
  const progress = ((step + 1) / questions.length) * 100

  function next(answer: AnswerKey) {
    setIsLoading(true)
    const newAnswers = { ...answers, [currentQ.id]: answer }

    // Check override rules
    const hit = overrides.find(
      (ov) => ov.questionId === currentQ.id && ov.answerKey === answer,
    )
    if (hit) {
      router.push(`/recommend/${hit.result}`)
      return
    }

    setAnswers(newAnswers)

    if (step + 1 === questions.length) {
      // Calculate score
      const score: Record<string, number> = {}
      Object.values(newAnswers).forEach((key) => {
        Object.entries(scoreMatrix[key]).forEach(([model, pts]) => {
          score[model] = (score[model] || 0) + pts
        })
      })
      const winner = Object.entries(score).sort((a, b) => b[1] - a[1])[0][0]
      router.push(`/recommend/${winner}`)
    } else {
      setStep(step + 1)
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-100 py-12">
      <div className="max-w-2xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Find Your Perfect AI Coding Assistant
          </h1>
          <p className="text-gray-600">
            Answer 5 quick questions to get a personalized recommendation
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Question {step + 1} of {questions.length}</span>
            <span>{Math.round(progress)}% complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-green-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Question Card */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">
            {currentQ.text}
          </h2>
          
          <div className="space-y-3">
            {currentQ.answers.map(({ key, label }) => (
              <button
                key={key}
                onClick={() => next(key)}
                disabled={isLoading}
                className="w-full text-left border-2 border-gray-200 rounded-lg py-4 px-6 hover:border-green-500 hover:bg-green-50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <div className="flex items-center">
                  <div className="w-6 h-6 border-2 border-gray-300 rounded-full mr-4 flex items-center justify-center">
                    <div className="w-2 h-2 bg-green-600 rounded-full opacity-0 group-hover:opacity-100" />
                  </div>
                  <span className="text-gray-800 font-medium">{label}</span>
                </div>
              </button>
            ))}
          </div>

          {isLoading && (
            <div className="mt-6 text-center">
              <div className="inline-flex items-center text-green-600">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Analyzing your development needs...
              </div>
            </div>
          )}
        </div>

        {/* Back Button */}
        {step > 0 && !isLoading && (
          <div className="mt-6 text-center">
            <button
              onClick={() => setStep(step - 1)}
              className="text-gray-600 hover:text-gray-800 font-medium"
            >
              ← Back to previous question
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
