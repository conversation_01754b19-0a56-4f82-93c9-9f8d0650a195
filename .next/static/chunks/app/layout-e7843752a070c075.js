(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{6310:function(e,s,o){Promise.resolve().then(o.t.bind(o,2853,23)),Promise.resolve().then(o.t.bind(o,3925,23))},2853:function(){throw Error("Module build failed (from ./node_modules/next/dist/compiled/mini-css-extract-plugin/loader.js):\nHookWebpackError: Module build failed (from ./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js):\nError: It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.\n    at We (/Users/<USER>/Desktop/Webiste Tool/node_modules/tailwindcss/dist/lib.js:35:2121)\n    at LazyResult.runOnRoot (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/node_modules/postcss/lib/lazy-result.js:329:16)\n    at LazyResult.runAsync (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/node_modules/postcss/lib/lazy-result.js:258:26)\n    at LazyResult.async (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/node_modules/postcss/lib/lazy-result.js:160:30)\n    at LazyResult.then (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/node_modules/postcss/lib/lazy-result.js:404:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at tryRunOrWebpackError (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/webpack/bundle5.js:28:311563)\n    at __webpack_require_module__ (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/webpack/bundle5.js:28:131195)\n    at __nested_webpack_require_153754__ (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/webpack/bundle5.js:28:130634)\n    at /Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/webpack/bundle5.js:28:131487\n    at symbolIterator (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/neo-async/async.js:1:14444)\n    at done (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/neo-async/async.js:1:14824)\n    at Hook.eval [as callAsync] (eval at create (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/webpack/bundle5.js:13:28867), <anonymous>:15:1)\n    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/webpack/bundle5.js:13:26021)\n    at /Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/webpack/bundle5.js:28:130354\n    at symbolIterator (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/neo-async/async.js:1:14402)\n-- inner error --\nError: Module build failed (from ./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js):\nError: It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.\n    at We (/Users/<USER>/Desktop/Webiste Tool/node_modules/tailwindcss/dist/lib.js:35:2121)\n    at LazyResult.runOnRoot (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/node_modules/postcss/lib/lazy-result.js:329:16)\n    at LazyResult.runAsync (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/node_modules/postcss/lib/lazy-result.js:258:26)\n    at LazyResult.async (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/node_modules/postcss/lib/lazy-result.js:160:30)\n    at LazyResult.then (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/node_modules/postcss/lib/lazy-result.js:404:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[2]!/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[3]!/Users/<USER>/Desktop/Webiste Tool/app/globals.css:1:7)\n    at /Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/webpack/bundle5.js:28:915116\n    at Hook.eval (eval at create (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/webpack/bundle5.js:13:28645), <anonymous>:7:1)\n    at Hook.CALL_DELEGATE [as _call] (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/webpack/bundle5.js:13:25915)\n    at /Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/webpack/bundle5.js:28:131228\n    at tryRunOrWebpackError (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/webpack/bundle5.js:28:311517)\n    at __webpack_require_module__ (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/webpack/bundle5.js:28:131195)\n    at __nested_webpack_require_153754__ (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/webpack/bundle5.js:28:130634)\n    at /Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/webpack/bundle5.js:28:131487\n    at symbolIterator (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/compiled/neo-async/async.js:1:14444)\n\nGenerated code for /Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[2]!/Users/<USER>/Desktop/Webiste Tool/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[3]!/Users/<USER>/Desktop/Webiste Tool/app/globals.css\n1 | throw new Error(\"Module build failed (from ./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js):\\nError: It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.\\n    at We (/Users/<USER>/Desktop/Webiste Tool/node_modules/tailwindcss/dist/lib.js:35:2121)\\n    at LazyResult.runOnRoot (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/node_modules/postcss/lib/lazy-result.js:329:16)\\n    at LazyResult.runAsync (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/node_modules/postcss/lib/lazy-result.js:258:26)\\n    at LazyResult.async (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/node_modules/postcss/lib/lazy-result.js:160:30)\\n    at LazyResult.then (/Users/<USER>/Desktop/Webiste Tool/node_modules/next/node_modules/postcss/lib/lazy-result.js:404:17)\\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\");")},3925:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}}},function(e){e.O(0,[971,472,744],function(){return e(e.s=6310)}),_N_E=e.O()}]);