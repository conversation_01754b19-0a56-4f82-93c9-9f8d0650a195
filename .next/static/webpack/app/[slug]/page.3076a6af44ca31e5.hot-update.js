"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./app/[slug]/PillarClient.tsx":
/*!*************************************!*\
  !*** ./app/[slug]/PillarClient.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PillarClient; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction PillarClient(param) {\n    let { pillar } = param;\n    // Determine if this is a writing or coding pillar\n    const isWritingPillar = pillar.cluster === \"text\" || pillar.slug.includes(\"writing\");\n    const isCodingPillar = pillar.cluster === \"code\" || pillar.slug.includes(\"coding\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(isWritingPillar ? \"bg-blue-100 text-blue-800\" : isCodingPillar ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"),\n                                children: isWritingPillar ? \"Writing\" : isCodingPillar ? \"Coding\" : pillar.cluster\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 11\n                            }, this),\n                            pillar.priority && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(pillar.priority === \"High\" ? \"bg-red-100 text-red-800\" : pillar.priority === \"Medium\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-green-100 text-green-800\"),\n                                children: [\n                                    pillar.priority,\n                                    \" Priority\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold mb-4 text-gray-900\",\n                        children: pillar.title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600 mb-4\",\n                        children: pillar.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    pillar.lastUpdated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"Last updated: \",\n                            pillar.lastUpdated\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            isWritingPillar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WritingPillarContent, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 45,\n                columnNumber: 27\n            }, this),\n            isCodingPillar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodingPillarContent, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 46,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_c = PillarClient;\n// Writing Pillar Content Component\nfunction WritingPillarContent() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"prose prose-lg max-w-none text-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"Not sure which AI fits your workflow?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Take our 30-second quiz to get a personalized recommendation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/quiz/writing\",\n                            className: \"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\",\n                            children: \"Take the Writing Quiz →\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 mt-8 text-gray-900\",\n                children: \"Who are you writing for?\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold mb-2 text-gray-900\",\n                        children: \"The Blogger\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"mb-4 pl-6 list-disc text-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Pain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – needs original long-form content that won't feel robotic or earn an SEO penalty.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Ideal output\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – an AI blog generator that keeps a consistent tone.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Killer feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – a huge context window to track details across thousands of words.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold mb-2 text-gray-900\",\n                        children: \"The Student\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"mb-4 pl-6 list-disc text-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Pain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – must research, structure, and cite accurately while avoiding plagiarism.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Ideal output\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – an AI essay writer that returns verifiable facts with citations.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Killer feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – can ingest PDFs and analyse them directly.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold mb-2 text-gray-900\",\n                        children: \"The Marketer\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"mb-4 pl-6 list-disc text-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Pain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – high-volume, mixed-format content plus brand-voice consistency.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Ideal output\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – a tool that plugs into Google Workspace and accelerates campaigns.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Killer feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – analyses spreadsheet data and builds project plans.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                className: \"my-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: 'A market of specialists, not one \"best\" model'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    \"Perplexity is an \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"answer engine\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 26\n                    }, this),\n                    \", Claude a \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"creative prose specialist\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 67\n                    }, this),\n                    \", and Gemini a \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"productivity layer\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 124\n                    }, this),\n                    \" for Docs, Sheets, and Gmail. The takeaway: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"choose by task\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 203\n                    }, this),\n                    \", not by raw IQ.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-2 text-gray-900\",\n                        children: \"⚠ Premium trap\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-800\",\n                        children: 'The $20 Pro tiers are no longer the cutting edge. OpenAI, Anthropic, Perplexity, and xAI now sell $100–$300 \"Max / Heavy\" plans aimed at enterprises. For most writers the $20 tier remains the ROI sweet-spot, but know you\\'re not buying the absolute top model.'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"2025 AI-writer scorecard\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full border border-gray-300\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Model\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Best for\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Stand-out feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Context window\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Free tier\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Pro price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Key limitation\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Claude 3.5 Sonnet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Creative writing (Poet)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: '\"Artifacts\" live editor'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"200k tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Yes (daily cap)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"No native real-time web search\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"GPT-4o\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Generalist (Polymath)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Multimodal + Custom GPTs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"128k tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Yes (cap)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Output can feel robotic\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Gemini Advanced\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Productivity (Producer)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Deep Workspace integration\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"1M+ tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Yes (std)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$19.99\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Creative flair weaker than Claude\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Perplexity Pro\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Research (Professor)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Clickable citations, Deep Research\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"—\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Yes (cap)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Not a creative writer\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Grok\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Real-time insights (Provocateur)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Live X / Twitter data\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"—\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Yes (cap)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$30\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Pricey; edgy tone not for all\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-right text-sm mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: \"/export/scorecard.csv\",\n                    className: \"text-blue-600 hover:text-blue-800 underline\",\n                    children: \"Export to Sheets →\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"Speed test ⚡\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-600 italic\",\n                children: \"[Speed comparison GIF placeholder - GPT-4o vs Claude 3.5 vs Gemini]\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-8 text-gray-800\",\n                children: \"GPT-4o starts generating sooner and streams tokens faster, a win for brainstorms and quick Q & A. Claude is ~2\\xd7 faster than its predecessor Opus but still trails GPT-4o on sheer responsiveness.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"Deep-dive profiles\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Claude 3.5 Sonnet — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the creative wordsmith\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 85\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Strengths.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    ' Thoughtful, expressive prose; 200k-token context; \"Artifacts\" side-panel for iterative editing.',\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 132\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Weaknesses.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    \" No built-in web browsing; free tier message cap.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 86\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: [\n                            \"Read the full \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/claude-3-5-for-blogging-review\",\n                                className: \"text-blue-600 hover:text-blue-800 underline\",\n                                children: \"Claude 3.5 blogging review\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 27\n                            }, this),\n                            \".\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"GPT-4o — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the versatile all-rounder\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 74\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    \"Handles writing, code, data and images in one chat. Custom GPTs unlock niche workflows.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 96\n                    }, this),\n                    \"Criticisms: increasing verbosity, smaller window than Claude, privacy concerns.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Gemini Advanced — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the integrated productivity engine\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 83\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    \"Native in Docs, Sheets, Gmail. Perfect for campaign tables, email summarising, Drive file look-ups.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 108\n                    }, this),\n                    \"Deep dive: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/gemini-advanced-for-marketers-guide\",\n                        className: \"text-blue-600 hover:text-blue-800 underline\",\n                        children: \"Gemini for marketers\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 20\n                    }, this),\n                    \".\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Perplexity Pro — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the research powerhouse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 82\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    'Delivers answers with numbered citations; \"Deep Research\" builds exhaustive reports.',\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 93\n                    }, this),\n                    \"Guide: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/how-to-use-perplexity-for-academic-research\",\n                        className: \"text-blue-600 hover:text-blue-800 underline\",\n                        children: \"How to use Perplexity for academic research\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 16\n                    }, this),\n                    \".\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Grok — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the real-time provocateur\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 72\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-8 text-gray-800\",\n                children: \"Live social-media pulse plus a snarky attitude. Great for trend analysts, overkill for everyday writing.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"Not sure which AI fits your workflow?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Take our 30-second quiz to get a personalized recommendation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/quiz/writing\",\n                            className: \"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\",\n                            children: \"Take the Writing Quiz →\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_c1 = WritingPillarContent;\n// Coding Pillar Content Component\nfunction CodingPillarContent() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"prose prose-lg max-w-none text-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 my-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"Find Your Perfect AI Coding Assistant\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Take our developer quiz to get a personalized recommendation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/quiz/coding\",\n                            className: \"inline-block bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\",\n                            children: \"Take the Coding Quiz →\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 mt-8 text-gray-900\",\n                children: \"The AI Coding Landscape in 2025\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: 'The conversation around AI coding assistants has moved far beyond simple autocomplete. In 2025, these tools are powerful collaborators capable of architecting systems, debugging multi-file repositories, and accelerating development cycles. But the fragmented market means the \"best\" AI is no longer a simple choice.'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"The AI Coder's Scorecard: Specs at a Glance\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: \"For developers, specs matter. This chart breaks down the key models by what you care about most: cost, context, and core strengths.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full border border-gray-300\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Model\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Pricing (per user/month)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Context Window\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Key Strength / Ecosystem\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"GPT-4o\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"~$20 (API is usage-based)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"128k tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: 'Versatility; a powerful \"second brain\" for logic and algorithms.'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Claude 3.5 Sonnet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"~$20 (API is usage-based)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"200k tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Massive context for codebase analysis and complex refactoring.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"GitHub Copilot\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$19 (Business) / $39 (Enterprise)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Varies (uses GPT-4)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Deep integration with GitHub, VS Code, and the PR lifecycle.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Replit Ghostwriter\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$20 (Pro) / $50 (Teams)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Varies\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Native to the Replit cloud IDE for seamless prototyping.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-right text-sm mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: \"/export/coding-scorecard.csv\",\n                    className: \"text-blue-600 hover:text-blue-800 underline\",\n                    children: \"Export to Sheets →\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"The Code Challenge: Simple Bugs vs. High-Context Flaws\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: \"Not all bugs are created equal. Some are simple typos, while others are subtle logical flaws that hide deep within a large codebase. We tested the leading models with two distinct challenges to see where they shine and where they falter.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: \"Snippet 1: The Flawless Fix\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: \"This simple Python function is meant to calculate the total price of items in a cart but has a common off-by-one error.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900 text-white p-4 rounded-lg overflow-x-auto mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    className: \"text-sm text-green-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        children: [\n                            'def calculate_cart_total(prices):\\n  total = 0\\n  # Bug: range stops before the last index\\n  for i in range(len(prices) - 1):\\n    total += prices[i]\\n  return total\\n\\ncart = [10, 25, 15, 5]\\nprint(f\"Total: $',\n                            '{calculate_cart_total(cart)}\")',\n                            \"\\n# Expected output: $55\\n# Actual output: $50\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-6 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Result:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 9\n                    }, this),\n                    \" Every model tested—GPT-4o, Claude, Copilot, and Ghostwriter—fixed this instantly. They correctly identified that the loop failed to include the last item and adjusted \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-sm\",\n                        children: \"range(len(prices) - 1)\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 201\n                    }, this),\n                    \" to \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-sm\",\n                        children: \"range(len(prices))\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 290\n                    }, this),\n                    \". This is the table-stakes capability you should expect from any modern AI code generator.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 345,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: \"Snippet 2: The High-Context Challenge\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    \"This is where premium models prove their worth. The bug here is subtle. A utility function \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-sm\",\n                        children: \"process_data\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 100\n                    }, this),\n                    \" incorrectly uses a global \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-sm\",\n                        children: \"TRANSACTION_FEE\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 202\n                    }, this),\n                    \" variable, but this is only apparent when you see how \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-sm\",\n                        children: \"process_data\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 334\n                    }, this),\n                    \" is called by another function that has already applied a separate, regional tax.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900 text-white p-4 rounded-lg overflow-x-auto mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    className: \"text-sm\",\n                    children: \"// Defined 500 lines earlier...\\nconst TRANSACTION_FEE = 0.02; // 2% processing fee\\n\\nfunction process_data(items) {\\n  let subtotal = items.reduce((acc, item) => acc + item.price, 0);\\n  // Bug: This fee is applied redundantly\\n  return subtotal * (1 + TRANSACTION_FEE);\\n}\\n\\n// ... much later in the file ...\\nfunction checkout_for_region(cart, region_config) {\\n  let regional_total = cart.reduce((acc, item) => acc + item.price, 0);\\n  regional_total *= (1 + region_config.tax_rate);\\n\\n  // Send to processing, unaware that it adds another fee\\n  const final_price = process_data(cart);\\n  console.log(`Final price is: \" + \"${final_price.toFixed(2)}`);\\n}\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-6 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Result:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 9\n                    }, this),\n                    \" Lower-Context Models typically suggest fixing \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-sm\",\n                        children: \"process_data\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 80\n                    }, this),\n                    \" in isolation. High-Context Models (Claude 3.5 Sonnet & GPT-4o) excelled by identifying the core issue and suggesting proper refactoring.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"The Enterprise Developer's Checklist\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: \"For teams, choosing an AI coding assistant involves more than just performance—it's about security, licensing, and integration.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 382,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border-l-4 border-blue-400 p-4 mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"space-y-2 text-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                \"☐ \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Data Privacy & Training:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 17\n                                }, this),\n                                \" Zero-retention policy for proprietary code\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                \"☐ \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Licensing & Indemnification:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 17\n                                }, this),\n                                \" Clear ownership terms and IP protection\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                \"☐ \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Seat Management & SSO:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 17\n                                }, this),\n                                \" Central dashboard and Single Sign-On integration\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                \"☐ \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Security Compliance:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 17\n                                }, this),\n                                \" SOC 2 Type 2 compliance for enterprise environments\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                \"☐ \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"IDE & Toolchain Integration:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 17\n                                }, this),\n                                \" First-party extensions for preferred IDEs\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"Deep-dive profiles\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"GPT-4o — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the versatile problem-solver\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 74\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 398,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Strengths.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this),\n                    \" Excellent logical reasoning; handles multiple programming languages; strong algorithmic thinking.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 134\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Weaknesses.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this),\n                    \" Smaller context window than Claude; can be verbose in explanations.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 105\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"Perfect for: General development, algorithm design, multi-language projects.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Claude 3.5 Sonnet — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the codebase analyst\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 85\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 405,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Strengths.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 9\n                    }, this),\n                    \" Massive 200k token context; excellent at understanding large file relationships; thoughtful refactoring suggestions.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 153\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Weaknesses.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this),\n                    \" No native IDE integration yet; API-only access.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 85\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"Perfect for: Large codebase analysis, complex refactoring, architectural decisions.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 406,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"GitHub Copilot — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the workflow integrator\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 82\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 412,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Strengths.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 9\n                    }, this),\n                    \" Seamless VS Code integration; understands Git context; PR and issue integration.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 117\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Weaknesses.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 9\n                    }, this),\n                    \" Limited to GitHub ecosystem; enterprise pricing can be steep.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 99\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"Perfect for: GitHub-based teams, VS Code users, integrated development workflows.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 413,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Replit Ghostwriter — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the rapid prototyper\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 86\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-8 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Strengths.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 9\n                    }, this),\n                    \" Instant deployment; browser-based development; great for learning and experimentation.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 123\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Weaknesses.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 9\n                    }, this),\n                    \" Limited to Replit environment; less suitable for complex enterprise projects.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 115\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"Perfect for: Rapid prototyping, educational projects, web-based development.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 420,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 my-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"Find Your Perfect AI Coding Assistant\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Take our developer quiz to get a personalized recommendation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/quiz/coding\",\n                            className: \"inline-block bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\",\n                            children: \"Take the Coding Quiz →\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 428,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 427,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_c2 = CodingPillarContent;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PillarClient\");\n$RefreshReg$(_c1, \"WritingPillarContent\");\n$RefreshReg$(_c2, \"CodingPillarContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[slug]/PillarClient.tsx\n"));

/***/ })

});