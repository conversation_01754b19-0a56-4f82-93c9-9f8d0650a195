"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./app/[slug]/PillarClient.tsx":
/*!*************************************!*\
  !*** ./app/[slug]/PillarClient.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PillarClient; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction PillarClient(param) {\n    let { pillar } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(pillar.cluster === \"text\" ? \"bg-blue-100 text-blue-800\" : pillar.cluster === \"code\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"),\n                                children: pillar.cluster === \"text\" ? \"Writing\" : pillar.cluster === \"code\" ? \"Coding\" : pillar.cluster\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 11\n                            }, this),\n                            pillar.priority && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(pillar.priority === \"High\" ? \"bg-red-100 text-red-800\" : pillar.priority === \"Medium\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-green-100 text-green-800\"),\n                                children: [\n                                    pillar.priority,\n                                    \" Priority\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold mb-4 text-gray-900\",\n                        children: pillar.title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600 mb-4\",\n                        children: pillar.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    pillar.lastUpdated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"Last updated: \",\n                            pillar.lastUpdated\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"prose prose-lg max-w-none text-gray-900\",\n                dangerouslySetInnerHTML: {\n                    __html: pillar.body.html\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"Not sure which AI fits your workflow?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Take our 30-second quiz to get a personalized recommendation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/quiz/writing\",\n                            className: \"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\",\n                            children: \"Take the Writing Quiz →\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-900 prose prose-lg max-w-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-semibold mb-4 mt-8 text-gray-900\",\n                        children: \"Who are you writing for?\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2 text-gray-900\",\n                                children: \"The Blogger\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"mb-4 pl-6 list-disc text-gray-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Pain\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" – needs original long-form content that won't feel robotic or earn an SEO penalty.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Ideal output\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" – an AI blog generator that keeps a consistent tone.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Killer feature\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" – a huge context window to track details across thousands of words.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2 text-gray-900\",\n                                children: \"The Student\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"mb-4 pl-6 list-disc text-gray-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Pain\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" – must research, structure, and cite accurately while avoiding plagiarism.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Ideal output\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" – an AI essay writer that returns verifiable facts with citations.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Killer feature\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" – can ingest PDFs and analyse them directly.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2 text-gray-900\",\n                                children: \"The Marketer\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"mb-4 pl-6 list-disc text-gray-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Pain\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" – high-volume, mixed-format content plus brand-voice consistency.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Ideal output\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" – a tool that plugs into Google Workspace and accelerates campaigns.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Killer feature\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" – analyses spreadsheet data and builds project plans.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"my-8\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                        children: 'A market of specialists, not one \"best\" model'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4 text-gray-800\",\n                        children: [\n                            \"Perplexity is an \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"answer engine\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 28\n                            }, this),\n                            \", Claude a \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"creative prose specialist\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 69\n                            }, this),\n                            \", and Gemini a \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"productivity layer\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 126\n                            }, this),\n                            \" for Docs, Sheets, and Gmail. The takeaway: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                children: \"choose by task\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 205\n                            }, this),\n                            \", not by raw IQ.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2 text-gray-900\",\n                                children: \"⚠ Premium trap\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-800\",\n                                children: 'The $20 Pro tiers are no longer the cutting edge. OpenAI, Anthropic, Perplexity, and xAI now sell $100–$300 \"Max / Heavy\" plans aimed at enterprises. For most writers the $20 tier remains the ROI sweet-spot, but know you\\'re not buying the absolute top model.'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                        children: \"2025 AI-writer scorecard\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full border border-gray-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                                children: \"Model\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                                children: \"Best for\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                                children: \"Stand-out feature\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                                children: \"Context window\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                                children: \"Free tier\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                                children: \"Pro price\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                                children: \"Key limitation\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Claude 3.5 Sonnet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 80\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Creative writing (Poet)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: '\"Artifacts\" live editor'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"200k tokens\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Yes (daily cap)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"$20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"No native real-time web search\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"GPT-4o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 80\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Generalist (Polymath)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Multimodal + Custom GPTs\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"128k tokens\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Yes (cap)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"$20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Output can feel robotic\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Gemini Advanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 80\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Productivity (Producer)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Deep Workspace integration\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"1M+ tokens\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Yes (std)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"$19.99\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Creative flair weaker than Claude\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Perplexity Pro\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 80\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Research (Professor)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Clickable citations, Deep Research\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"—\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Yes (cap)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"$20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Not a creative writer\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Grok\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 80\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Real-time insights (Provocateur)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Live X / Twitter data\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"—\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Yes (cap)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"$30\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                                    children: \"Pricey; edgy tone not for all\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-right text-sm mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/export/scorecard.csv\",\n                            className: \"text-blue-600 hover:text-blue-800 underline\",\n                            children: \"Export to Sheets →\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                        children: \"Speed test ⚡\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4 text-gray-600 italic\",\n                        children: \"[Speed comparison GIF placeholder - GPT-4o vs Claude 3.5 vs Gemini]\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-8 text-gray-800\",\n                        children: \"GPT-4o starts generating sooner and streams tokens faster, a win for brainstorms and quick Q & A. Claude is ~2\\xd7 faster than its predecessor Opus but still trails GPT-4o on sheer responsiveness.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                        children: \"Deep-dive profiles\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                        children: [\n                            \"Claude 3.5 Sonnet — \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                children: \"the creative wordsmith\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 87\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4 text-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Strengths.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            ' Thoughtful, expressive prose; 200k-token context; \"Artifacts\" side-panel for iterative editing.',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 134\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Weaknesses.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            \" No built-in web browsing; free tier message cap.\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 88\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                children: [\n                                    \"Read the full \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/claude-3-5-for-blogging-review\",\n                                        className: \"text-blue-600 hover:text-blue-800 underline\",\n                                        children: \"Claude 3.5 blogging review\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 29\n                                    }, this),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                        children: [\n                            \"GPT-4o — \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                children: \"the versatile all-rounder\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 76\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4 text-gray-800\",\n                        children: [\n                            \"Handles writing, code, data and images in one chat. Custom GPTs unlock niche workflows.\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 98\n                            }, this),\n                            \"Criticisms: increasing verbosity, smaller window than Claude, privacy concerns.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                        children: [\n                            \"Gemini Advanced — \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                children: \"the integrated productivity engine\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 85\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4 text-gray-800\",\n                        children: [\n                            \"Native in Docs, Sheets, Gmail. Perfect for campaign tables, email summarising, Drive file look-ups.\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 110\n                            }, this),\n                            \"Deep dive: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/gemini-advanced-for-marketers-guide\",\n                                className: \"text-blue-600 hover:text-blue-800 underline\",\n                                children: \"Gemini for marketers\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 22\n                            }, this),\n                            \".\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                        children: [\n                            \"Perplexity Pro — \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                children: \"the research powerhouse\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 84\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4 text-gray-800\",\n                        children: [\n                            'Delivers answers with numbered citations; \"Deep Research\" builds exhaustive reports.',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 95\n                            }, this),\n                            \"Guide: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/how-to-use-perplexity-for-academic-research\",\n                                className: \"text-blue-600 hover:text-blue-800 underline\",\n                                children: \"How to use Perplexity for academic research\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 18\n                            }, this),\n                            \".\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                        children: [\n                            \"Grok — \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                children: \"the real-time provocateur\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 74\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-8 text-gray-800\",\n                        children: \"Live social-media pulse plus a snarky attitude. Great for trend analysts, overkill for everyday writing.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                    children: \"Not sure which AI fits your workflow?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Take our 30-second quiz to get a personalized recommendation\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/quiz/writing\",\n                                    className: \"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\",\n                                    children: \"Take the Writing Quiz →\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_c = PillarClient;\nvar _c;\n$RefreshReg$(_c, \"PillarClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[slug]/PillarClient.tsx\n"));

/***/ })

});