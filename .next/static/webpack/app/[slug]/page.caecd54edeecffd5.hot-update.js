"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[slug]/page",{

/***/ "(app-pages-browser)/./app/[slug]/PillarClient.tsx":
/*!*************************************!*\
  !*** ./app/[slug]/PillarClient.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PillarClient; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction PillarClient(param) {\n    let { pillar } = param;\n    // Determine if this is a writing or coding pillar\n    const isWritingPillar = pillar.cluster === \"text\" || pillar.slug.includes(\"writing\");\n    const isCodingPillar = pillar.cluster === \"code\" || pillar.slug.includes(\"coding\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(isWritingPillar ? \"bg-blue-100 text-blue-800\" : isCodingPillar ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"),\n                                children: isWritingPillar ? \"Writing\" : isCodingPillar ? \"Coding\" : pillar.cluster\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 11\n                            }, this),\n                            pillar.priority && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(pillar.priority === \"High\" ? \"bg-red-100 text-red-800\" : pillar.priority === \"Medium\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-green-100 text-green-800\"),\n                                children: [\n                                    pillar.priority,\n                                    \" Priority\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold mb-4 text-gray-900\",\n                        children: pillar.title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600 mb-4\",\n                        children: pillar.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    pillar.lastUpdated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"Last updated: \",\n                            pillar.lastUpdated\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            isWritingPillar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WritingPillarContent, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 45,\n                columnNumber: 27\n            }, this),\n            isCodingPillar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodingPillarContent, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 46,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_c = PillarClient;\n// Writing Pillar Content Component\nfunction WritingPillarContent() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"prose prose-lg max-w-none text-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"Not sure which AI fits your workflow?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Take our 30-second quiz to get a personalized recommendation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/quiz/writing\",\n                            className: \"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\",\n                            children: \"Take the Writing Quiz →\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 mt-8 text-gray-900\",\n                children: \"Who are you writing for?\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold mb-2 text-gray-900\",\n                        children: \"The Blogger\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"mb-4 pl-6 list-disc text-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Pain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – needs original long-form content that won't feel robotic or earn an SEO penalty.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Ideal output\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – an AI blog generator that keeps a consistent tone.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Killer feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – a huge context window to track details across thousands of words.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold mb-2 text-gray-900\",\n                        children: \"The Student\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"mb-4 pl-6 list-disc text-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Pain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – must research, structure, and cite accurately while avoiding plagiarism.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Ideal output\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – an AI essay writer that returns verifiable facts with citations.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Killer feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – can ingest PDFs and analyse them directly.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold mb-2 text-gray-900\",\n                        children: \"The Marketer\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"mb-4 pl-6 list-disc text-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Pain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – high-volume, mixed-format content plus brand-voice consistency.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Ideal output\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – a tool that plugs into Google Workspace and accelerates campaigns.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Killer feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – analyses spreadsheet data and builds project plans.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                className: \"my-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: 'A market of specialists, not one \"best\" model'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    \"Perplexity is an \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"answer engine\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 26\n                    }, this),\n                    \", Claude a \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"creative prose specialist\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 67\n                    }, this),\n                    \", and Gemini a \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"productivity layer\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 124\n                    }, this),\n                    \" for Docs, Sheets, and Gmail. The takeaway: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"choose by task\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 203\n                    }, this),\n                    \", not by raw IQ.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-2 text-gray-900\",\n                        children: \"⚠ Premium trap\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-800\",\n                        children: 'The $20 Pro tiers are no longer the cutting edge. OpenAI, Anthropic, Perplexity, and xAI now sell $100–$300 \"Max / Heavy\" plans aimed at enterprises. For most writers the $20 tier remains the ROI sweet-spot, but know you\\'re not buying the absolute top model.'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"2025 AI-writer scorecard\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full border border-gray-300\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Model\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Best for\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Stand-out feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Context window\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Free tier\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Pro price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Key limitation\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Claude 3.5 Sonnet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Creative writing (Poet)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: '\"Artifacts\" live editor'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"200k tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Yes (daily cap)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"No native real-time web search\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"GPT-4o\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Generalist (Polymath)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Multimodal + Custom GPTs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"128k tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Yes (cap)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Output can feel robotic\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Gemini Advanced\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Productivity (Producer)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Deep Workspace integration\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"1M+ tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Yes (std)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$19.99\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Creative flair weaker than Claude\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Perplexity Pro\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Research (Professor)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Clickable citations, Deep Research\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"—\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Yes (cap)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Not a creative writer\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Grok\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Real-time insights (Provocateur)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Live X / Twitter data\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"—\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Yes (cap)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$30\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Pricey; edgy tone not for all\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-right text-sm mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: \"/export/scorecard.csv\",\n                    className: \"text-blue-600 hover:text-blue-800 underline\",\n                    children: \"Export to Sheets →\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"Speed test ⚡\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-600 italic\",\n                children: \"[Speed comparison GIF placeholder - GPT-4o vs Claude 3.5 vs Gemini]\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-8 text-gray-800\",\n                children: \"GPT-4o starts generating sooner and streams tokens faster, a win for brainstorms and quick Q & A. Claude is ~2\\xd7 faster than its predecessor Opus but still trails GPT-4o on sheer responsiveness.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"Deep-dive profiles\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Claude 3.5 Sonnet — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the creative wordsmith\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 85\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Strengths.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    ' Thoughtful, expressive prose; 200k-token context; \"Artifacts\" side-panel for iterative editing.',\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 132\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Weaknesses.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    \" No built-in web browsing; free tier message cap.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 86\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: [\n                            \"Read the full \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/claude-3-5-for-blogging-review\",\n                                className: \"text-blue-600 hover:text-blue-800 underline\",\n                                children: \"Claude 3.5 blogging review\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 27\n                            }, this),\n                            \".\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"GPT-4o — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the versatile all-rounder\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 74\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    \"Handles writing, code, data and images in one chat. Custom GPTs unlock niche workflows.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 96\n                    }, this),\n                    \"Criticisms: increasing verbosity, smaller window than Claude, privacy concerns.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Gemini Advanced — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the integrated productivity engine\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 83\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    \"Native in Docs, Sheets, Gmail. Perfect for campaign tables, email summarising, Drive file look-ups.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 108\n                    }, this),\n                    \"Deep dive: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/gemini-advanced-for-marketers-guide\",\n                        className: \"text-blue-600 hover:text-blue-800 underline\",\n                        children: \"Gemini for marketers\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 20\n                    }, this),\n                    \".\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Perplexity Pro — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the research powerhouse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 82\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    'Delivers answers with numbered citations; \"Deep Research\" builds exhaustive reports.',\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 93\n                    }, this),\n                    \"Guide: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/how-to-use-perplexity-for-academic-research\",\n                        className: \"text-blue-600 hover:text-blue-800 underline\",\n                        children: \"How to use Perplexity for academic research\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 16\n                    }, this),\n                    \".\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Grok — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the real-time provocateur\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 72\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-8 text-gray-800\",\n                children: \"Live social-media pulse plus a snarky attitude. Great for trend analysts, overkill for everyday writing.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"Not sure which AI fits your workflow?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Take our 30-second quiz to get a personalized recommendation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/quiz/writing\",\n                            className: \"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\",\n                            children: \"Take the Writing Quiz →\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_c1 = WritingPillarContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"PillarClient\");\n$RefreshReg$(_c1, \"WritingPillarContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[slug]/PillarClient.tsx\n"));

/***/ })

});