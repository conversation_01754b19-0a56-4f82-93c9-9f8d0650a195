{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/best-ai-for-writing": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/[slug]", "dataRoute": "/best-ai-for-writing.rsc"}}, "dynamicRoutes": {"/[slug]": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "routeRegex": "^/([^/]+?)(?:/)?$", "dataRoute": "/[slug].rsc", "fallback": null, "dataRouteRegex": "^/([^/]+?)\\.rsc$"}}, "notFoundRoutes": [], "preview": {"previewModeId": "9cada8d069c3d1dfdf204f763aa7a495", "previewModeSigningKey": "22d4f5167edb4e90b02ce59ea3f506545c68260e54ac3b519035c70c829b78bb", "previewModeEncryptionKey": "c5c5bb56aeff0426f36d488ad5a4511936d316435a71989dc8b53bdec68dc08b"}}