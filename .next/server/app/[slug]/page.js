(()=>{var e={};e.id=42,e.ids=[42],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2820:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>d,pages:()=>c,routeModule:()=>m,tree:()=>p});var r=s(7096),a=s(6132),n=s(7284),i=s.n(n),l=s(2564),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let p=["",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9051)),"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5345)),"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4293)),"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx"]}],c=["/Users/<USER>/Desktop/Webiste Tool/app/[slug]/page.tsx"],d="/[slug]/page",u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[slug]/page",pathname:"/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},4488:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,114,23)),Promise.resolve().then(s.t.bind(s,6800,23))},9051:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>PillarPage,generateMetadata:()=>generateMetadata,generateStaticParams:()=>generateStaticParams});var r=s(4656),a=s(5899),n=s(1412),i=s(6002);async function getPillarFromParams(e){let t=a.JZ.find(t=>t.slug===e.slug);return t}async function generateMetadata({params:e}){let t=await getPillarFromParams(e);return t?{title:t.title,description:t.description}:{}}async function generateStaticParams(){return a.JZ.map(e=>({slug:e.slug}))}async function PillarPage({params:e}){let t=await getPillarFromParams(e);t||(0,n.notFound)();let s=(0,i.z)(t.body.code);return(0,r.jsxs)("article",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("header",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[r.jsx("span",{className:"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium",children:t.cluster}),t.priority&&(0,r.jsxs)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${"High"===t.priority?"bg-red-100 text-red-800":"Medium"===t.priority?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"}`,children:[t.priority," Priority"]})]}),r.jsx("h1",{className:"text-4xl font-bold mb-4",children:t.title}),r.jsx("p",{className:"text-xl text-gray-600 mb-4",children:t.description}),t.lastUpdated&&(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Last updated: ",t.lastUpdated]})]}),r.jsx("div",{className:"prose prose-lg max-w-none",children:r.jsx(s,{})})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[380,824,97,899],()=>__webpack_exec__(2820));module.exports=s})();