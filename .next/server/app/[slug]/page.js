/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[slug]/page";
exports.ids = ["app/[slug]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[slug]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[slug]/page.tsx */ \"(rsc)/./app/[slug]/page.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[slug]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[slug]/page\",\n        pathname: \"/[slug]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2F%5Bslug%5D%2FPillarClient.tsx&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fshared%2Flib%2Flazy-dynamic%2Fdynamic-no-ssr.js&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2F%5Bslug%5D%2FPillarClient.tsx&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fshared%2Flib%2Flazy-dynamic%2Fdynamic-no-ssr.js&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[slug]/PillarClient.tsx */ \"(ssr)/./app/[slug]/PillarClient.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-no-ssr.js */ \"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-no-ssr.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZhcHAlMkYlNUJzbHVnJTVEJTJGUGlsbGFyQ2xpZW50LnRzeCZtb2R1bGVzPSUyRlVzZXJzJTJGcGhpbG1ja2VuemllJTJGRGVza3RvcCUyRldlYmlzdGUlMjBUb29sJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZzaGFyZWQlMkZsaWIlMkZsYXp5LWR5bmFtaWMlMkZkeW5hbWljLW5vLXNzci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0tBQXlHO0FBQ3pHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8/NDA0YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9waGlsbWNrZW56aWUvRGVza3RvcC9XZWJpc3RlIFRvb2wvYXBwL1tzbHVnXS9QaWxsYXJDbGllbnQudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcGhpbG1ja2VuemllL0Rlc2t0b3AvV2ViaXN0ZSBUb29sL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvZHluYW1pYy1uby1zc3IuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2F%5Bslug%5D%2FPillarClient.tsx&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fshared%2Flib%2Flazy-dynamic%2Fdynamic-no-ssr.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!*****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmxpbmsuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8/MGQ2ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9waGlsbWNrZW56aWUvRGVza3RvcC9XZWJpc3RlIFRvb2wvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvbGluay5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/[slug]/PillarClient.tsx":
/*!*************************************!*\
  !*** ./app/[slug]/PillarClient.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PillarClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction PillarClient({ pillar }) {\n    // Determine if this is a writing or coding pillar\n    const isWritingPillar = pillar.cluster === \"text\" || pillar.slug.includes(\"writing\");\n    const isCodingPillar = pillar.cluster === \"code\" || pillar.slug.includes(\"coding\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `px-3 py-1 rounded-full text-sm font-medium ${isWritingPillar ? \"bg-blue-100 text-blue-800\" : isCodingPillar ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"}`,\n                                children: isWritingPillar ? \"Writing\" : isCodingPillar ? \"Coding\" : pillar.cluster\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 11\n                            }, this),\n                            pillar.priority && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `px-3 py-1 rounded-full text-sm font-medium ${pillar.priority === \"High\" ? \"bg-red-100 text-red-800\" : pillar.priority === \"Medium\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-green-100 text-green-800\"}`,\n                                children: [\n                                    pillar.priority,\n                                    \" Priority\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold mb-4 text-gray-900\",\n                        children: pillar.title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600 mb-4\",\n                        children: pillar.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    pillar.lastUpdated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"Last updated: \",\n                            pillar.lastUpdated\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            isWritingPillar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WritingPillarContent, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 45,\n                columnNumber: 27\n            }, this),\n            isCodingPillar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodingPillarContent, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 46,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n// Writing Pillar Content Component\nfunction WritingPillarContent() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"prose prose-lg max-w-none text-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"Not sure which AI fits your workflow?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Take our 30-second quiz to get a personalized recommendation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/quiz/writing\",\n                            className: \"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\",\n                            children: \"Take the Writing Quiz →\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 mt-8 text-gray-900\",\n                children: \"Who are you writing for?\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold mb-2 text-gray-900\",\n                        children: \"The Blogger\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"mb-4 pl-6 list-disc text-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Pain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – needs original long-form content that won't feel robotic or earn an SEO penalty.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Ideal output\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – an AI blog generator that keeps a consistent tone.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Killer feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – a huge context window to track details across thousands of words.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold mb-2 text-gray-900\",\n                        children: \"The Student\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"mb-4 pl-6 list-disc text-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Pain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – must research, structure, and cite accurately while avoiding plagiarism.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Ideal output\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – an AI essay writer that returns verifiable facts with citations.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Killer feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – can ingest PDFs and analyse them directly.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold mb-2 text-gray-900\",\n                        children: \"The Marketer\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"mb-4 pl-6 list-disc text-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Pain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – high-volume, mixed-format content plus brand-voice consistency.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Ideal output\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – a tool that plugs into Google Workspace and accelerates campaigns.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Killer feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" – analyses spreadsheet data and builds project plans.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                className: \"my-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: 'A market of specialists, not one \"best\" model'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    \"Perplexity is an \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"answer engine\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 26\n                    }, this),\n                    \", Claude a \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"creative prose specialist\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 67\n                    }, this),\n                    \", and Gemini a \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"productivity layer\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 124\n                    }, this),\n                    \" for Docs, Sheets, and Gmail. The takeaway: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"choose by task\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 203\n                    }, this),\n                    \", not by raw IQ.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-2 text-gray-900\",\n                        children: \"⚠ Premium trap\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-800\",\n                        children: 'The $20 Pro tiers are no longer the cutting edge. OpenAI, Anthropic, Perplexity, and xAI now sell $100–$300 \"Max / Heavy\" plans aimed at enterprises. For most writers the $20 tier remains the ROI sweet-spot, but know you\\'re not buying the absolute top model.'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"2025 AI-writer scorecard\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full border border-gray-300\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Model\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Best for\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Stand-out feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Context window\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Free tier\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Pro price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Key limitation\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Claude 3.5 Sonnet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Creative writing (Poet)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: '\"Artifacts\" live editor'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"200k tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Yes (daily cap)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"No native real-time web search\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"GPT-4o\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Generalist (Polymath)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Multimodal + Custom GPTs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"128k tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Yes (cap)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Output can feel robotic\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Gemini Advanced\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Productivity (Producer)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Deep Workspace integration\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"1M+ tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Yes (std)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$19.99\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Creative flair weaker than Claude\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Perplexity Pro\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Research (Professor)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Clickable citations, Deep Research\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"—\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Yes (cap)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Not a creative writer\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Grok\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Real-time insights (Provocateur)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Live X / Twitter data\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"—\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Yes (cap)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$30\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Pricey; edgy tone not for all\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-right text-sm mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: \"/export/scorecard.csv\",\n                    className: \"text-blue-600 hover:text-blue-800 underline\",\n                    children: \"Export to Sheets →\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"Speed test ⚡\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-600 italic\",\n                children: \"[Speed comparison GIF placeholder - GPT-4o vs Claude 3.5 vs Gemini]\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-8 text-gray-800\",\n                children: \"GPT-4o starts generating sooner and streams tokens faster, a win for brainstorms and quick Q & A. Claude is ~2\\xd7 faster than its predecessor Opus but still trails GPT-4o on sheer responsiveness.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"Deep-dive profiles\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Claude 3.5 Sonnet — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the creative wordsmith\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 85\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Strengths.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    ' Thoughtful, expressive prose; 200k-token context; \"Artifacts\" side-panel for iterative editing.',\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 132\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Weaknesses.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    \" No built-in web browsing; free tier message cap.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 86\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: [\n                            \"Read the full \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/claude-3-5-for-blogging-review\",\n                                className: \"text-blue-600 hover:text-blue-800 underline\",\n                                children: \"Claude 3.5 blogging review\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 27\n                            }, this),\n                            \".\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"GPT-4o — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the versatile all-rounder\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 74\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    \"Handles writing, code, data and images in one chat. Custom GPTs unlock niche workflows.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 96\n                    }, this),\n                    \"Criticisms: increasing verbosity, smaller window than Claude, privacy concerns.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Gemini Advanced — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the integrated productivity engine\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 83\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    \"Native in Docs, Sheets, Gmail. Perfect for campaign tables, email summarising, Drive file look-ups.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 108\n                    }, this),\n                    \"Deep dive: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/gemini-advanced-for-marketers-guide\",\n                        className: \"text-blue-600 hover:text-blue-800 underline\",\n                        children: \"Gemini for marketers\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 20\n                    }, this),\n                    \".\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Perplexity Pro — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the research powerhouse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 82\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    'Delivers answers with numbered citations; \"Deep Research\" builds exhaustive reports.',\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 93\n                    }, this),\n                    \"Guide: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/how-to-use-perplexity-for-academic-research\",\n                        className: \"text-blue-600 hover:text-blue-800 underline\",\n                        children: \"How to use Perplexity for academic research\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 16\n                    }, this),\n                    \".\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Grok — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the real-time provocateur\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 72\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-8 text-gray-800\",\n                children: \"Live social-media pulse plus a snarky attitude. Great for trend analysts, overkill for everyday writing.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"Not sure which AI fits your workflow?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Take our 30-second quiz to get a personalized recommendation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/quiz/writing\",\n                            className: \"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\",\n                            children: \"Take the Writing Quiz →\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n// Coding Pillar Content Component\nfunction CodingPillarContent() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"prose prose-lg max-w-none text-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 my-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"Find Your Perfect AI Coding Assistant\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Take our developer quiz to get a personalized recommendation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/quiz/coding\",\n                            className: \"inline-block bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\",\n                            children: \"Take the Coding Quiz →\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 mt-8 text-gray-900\",\n                children: \"The AI Coding Landscape in 2025\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: 'The conversation around AI coding assistants has moved far beyond simple autocomplete. In 2025, these tools are powerful collaborators capable of architecting systems, debugging multi-file repositories, and accelerating development cycles. But the fragmented market means the \"best\" AI is no longer a simple choice.'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"The AI Coder's Scorecard: Specs at a Glance\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: \"For developers, specs matter. This chart breaks down the key models by what you care about most: cost, context, and core strengths.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full border border-gray-300\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Model\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Pricing (per user/month)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Context Window\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"border border-gray-300 px-4 py-2 text-left text-gray-900\",\n                                        children: \"Key Strength / Ecosystem\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"GPT-4o\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"~$20 (API is usage-based)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"128k tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: 'Versatility; a powerful \"second brain\" for logic and algorithms.'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Claude 3.5 Sonnet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"~$20 (API is usage-based)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"200k tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Massive context for codebase analysis and complex refactoring.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"GitHub Copilot\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$19 (Business) / $39 (Enterprise)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Varies (uses GPT-4)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Deep integration with GitHub, VS Code, and the PR lifecycle.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Replit Ghostwriter\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 78\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"$20 (Pro) / $50 (Teams)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Varies\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"border border-gray-300 px-4 py-2 text-gray-800\",\n                                            children: \"Native to the Replit cloud IDE for seamless prototyping.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-right text-sm mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: \"/export/coding-scorecard.csv\",\n                    className: \"text-blue-600 hover:text-blue-800 underline\",\n                    children: \"Export to Sheets →\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"The Code Challenge: Simple Bugs vs. High-Context Flaws\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: \"Not all bugs are created equal. Some are simple typos, while others are subtle logical flaws that hide deep within a large codebase. We tested the leading models with two distinct challenges to see where they shine and where they falter.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: \"Snippet 1: The Flawless Fix\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: \"This simple Python function is meant to calculate the total price of items in a cart but has a common off-by-one error.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900 text-white p-4 rounded-lg overflow-x-auto mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    className: \"text-sm\",\n                    children: `def calculate_cart_total(prices):\n  total = 0\n  # Bug: range stops before the last index\n  for i in range(len(prices) - 1):\n    total += prices[i]\n  return total\n\ncart = [10, 25, 15, 5]\nprint(f\"Total: ${calculate_cart_total(cart)}\")\n# Expected output: $55\n# Actual output: $50`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-6 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Result:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 9\n                    }, this),\n                    \" Every model tested—GPT-4o, Claude, Copilot, and Ghostwriter—fixed this instantly. They correctly identified that the loop failed to include the last item and adjusted \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-sm\",\n                        children: \"range(len(prices) - 1)\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 201\n                    }, this),\n                    \" to \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-sm\",\n                        children: \"range(len(prices))\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 290\n                    }, this),\n                    \". This is the table-stakes capability you should expect from any modern AI code generator.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 345,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: \"Snippet 2: The High-Context Challenge\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    \"This is where premium models prove their worth. The bug here is subtle. A utility function \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-sm\",\n                        children: \"process_data\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 100\n                    }, this),\n                    \" incorrectly uses a global \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-sm\",\n                        children: \"TRANSACTION_FEE\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 202\n                    }, this),\n                    \" variable, but this is only apparent when you see how \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-sm\",\n                        children: \"process_data\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 334\n                    }, this),\n                    \" is called by another function that has already applied a separate, regional tax.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900 text-white p-4 rounded-lg overflow-x-auto mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    className: \"text-sm\",\n                    children: `// Defined 500 lines earlier...\nconst TRANSACTION_FEE = 0.02; // 2% processing fee\n\nfunction process_data(items) {\n  let subtotal = items.reduce((acc, item) => acc + item.price, 0);\n  // Bug: This fee is applied redundantly\n  return subtotal * (1 + TRANSACTION_FEE);\n}\n\n// ... much later in the file ...\nfunction checkout_for_region(cart, region_config) {\n  let regional_total = cart.reduce((acc, item) => acc + item.price, 0);\n  regional_total *= (1 + region_config.tax_rate);\n\n  // Send to processing, unaware that it adds another fee\n  const final_price = process_data(cart);\n  console.log(\\`Final price is: \\${final_price.toFixed(2)}\\`);\n}`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-6 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Result:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 9\n                    }, this),\n                    \" Lower-Context Models typically suggest fixing \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-100 px-2 py-1 rounded text-sm\",\n                        children: \"process_data\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 80\n                    }, this),\n                    \" in isolation. High-Context Models (Claude 3.5 Sonnet & GPT-4o) excelled by identifying the core issue and suggesting proper refactoring.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"The Enterprise Developer's Checklist\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: \"For teams, choosing an AI coding assistant involves more than just performance—it's about security, licensing, and integration.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 382,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border-l-4 border-blue-400 p-4 mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"space-y-2 text-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                \"☐ \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Data Privacy & Training:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 17\n                                }, this),\n                                \" Zero-retention policy for proprietary code\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                \"☐ \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Licensing & Indemnification:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 17\n                                }, this),\n                                \" Clear ownership terms and IP protection\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                \"☐ \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Seat Management & SSO:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 17\n                                }, this),\n                                \" Central dashboard and Single Sign-On integration\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                \"☐ \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Security Compliance:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 17\n                                }, this),\n                                \" SOC 2 Type 2 compliance for enterprise environments\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                \"☐ \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"IDE & Toolchain Integration:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 17\n                                }, this),\n                                \" First-party extensions for preferred IDEs\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-semibold mb-4 text-gray-900\",\n                children: \"Deep-dive profiles\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"GPT-4o — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the versatile problem-solver\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 74\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 398,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Strengths.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this),\n                    \" Excellent logical reasoning; handles multiple programming languages; strong algorithmic thinking.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 134\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Weaknesses.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this),\n                    \" Smaller context window than Claude; can be verbose in explanations.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 105\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"Perfect for: General development, algorithm design, multi-language projects.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Claude 3.5 Sonnet — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the codebase analyst\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 85\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 405,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Strengths.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 9\n                    }, this),\n                    \" Massive 200k token context; excellent at understanding large file relationships; thoughtful refactoring suggestions.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 153\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Weaknesses.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this),\n                    \" No native IDE integration yet; API-only access.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 85\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"Perfect for: Large codebase analysis, complex refactoring, architectural decisions.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 406,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"GitHub Copilot — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the workflow integrator\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 82\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 412,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Strengths.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 9\n                    }, this),\n                    \" Seamless VS Code integration; understands Git context; PR and issue integration.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 117\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Weaknesses.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 9\n                    }, this),\n                    \" Limited to GitHub ecosystem; enterprise pricing can be steep.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 99\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"Perfect for: GitHub-based teams, VS Code users, integrated development workflows.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 413,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-3 text-gray-900\",\n                children: [\n                    \"Replit Ghostwriter — \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"the rapid prototyper\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 86\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-8 text-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Strengths.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 9\n                    }, this),\n                    \" Instant deployment; browser-based development; great for learning and experimentation.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 123\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Weaknesses.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 9\n                    }, this),\n                    \" Limited to Replit environment; less suitable for complex enterprise projects.\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 115\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        children: \"Perfect for: Rapid prototyping, educational projects, web-based development.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 420,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 my-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"Find Your Perfect AI Coding Assistant\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Take our developer quiz to get a personalized recommendation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/quiz/coding\",\n                            className: \"inline-block bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\",\n                            children: \"Take the Coding Quiz →\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                    lineNumber: 428,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n                lineNumber: 427,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[slug]/PillarClient.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"92c3bf8256c7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS10b29sLy4vYXBwL2dsb2JhbHMuY3NzP2U3YmYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5MmMzYmY4MjU2YzdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./.contentlayer/generated/Pillar/_index.mjs":
/*!***************************************************!*\
  !*** ./.contentlayer/generated/Pillar/_index.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allPillars: () => (/* binding */ allPillars)\n/* harmony export */ });\n/* harmony import */ var _pillars_best_ai_for_writing_mdx_json__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pillars__best-ai-for-writing.mdx.json */ \"(rsc)/./.contentlayer/generated/Pillar/pillars__best-ai-for-writing.mdx.json\");\n/* harmony import */ var _pillars_best_ai_for_coding_mdx_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pillars__best-ai-for-coding.mdx.json */ \"(rsc)/./.contentlayer/generated/Pillar/pillars__best-ai-for-coding.mdx.json\");\n// NOTE This file is auto-generated by Contentlayer\n\n\nconst allPillars = [\n    _pillars_best_ai_for_writing_mdx_json__WEBPACK_IMPORTED_MODULE_0__,\n    _pillars_best_ai_for_coding_mdx_json__WEBPACK_IMPORTED_MODULE_1__\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi8uY29udGVudGxheWVyL2dlbmVyYXRlZC9QaWxsYXIvX2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSxtREFBbUQ7QUFFdUQ7QUFDRjtBQUVqRyxNQUFNRyxhQUFhO0lBQUNILGtFQUE0QkE7SUFBRUUsaUVBQTJCQTtDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8uLy5jb250ZW50bGF5ZXIvZ2VuZXJhdGVkL1BpbGxhci9faW5kZXgubWpzPzQ0MjUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gTk9URSBUaGlzIGZpbGUgaXMgYXV0by1nZW5lcmF0ZWQgYnkgQ29udGVudGxheWVyXG5cbmltcG9ydCBwaWxsYXJzX19iZXN0QWlGb3JXcml0aW5nTWR4IGZyb20gJy4vcGlsbGFyc19fYmVzdC1haS1mb3Itd3JpdGluZy5tZHguanNvbicgYXNzZXJ0IHsgdHlwZTogJ2pzb24nIH1cbmltcG9ydCBwaWxsYXJzX19iZXN0QWlGb3JDb2RpbmdNZHggZnJvbSAnLi9waWxsYXJzX19iZXN0LWFpLWZvci1jb2RpbmcubWR4Lmpzb24nIGFzc2VydCB7IHR5cGU6ICdqc29uJyB9XG5cbmV4cG9ydCBjb25zdCBhbGxQaWxsYXJzID0gW3BpbGxhcnNfX2Jlc3RBaUZvcldyaXRpbmdNZHgsIHBpbGxhcnNfX2Jlc3RBaUZvckNvZGluZ01keF1cbiJdLCJuYW1lcyI6WyJwaWxsYXJzX19iZXN0QWlGb3JXcml0aW5nTWR4IiwidHlwZSIsInBpbGxhcnNfX2Jlc3RBaUZvckNvZGluZ01keCIsImFsbFBpbGxhcnMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./.contentlayer/generated/Pillar/_index.mjs\n");

/***/ }),

/***/ "(rsc)/./.contentlayer/generated/index.mjs":
/*!*******************************************!*\
  !*** ./.contentlayer/generated/index.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allDocuments: () => (/* binding */ allDocuments),\n/* harmony export */   allPillars: () => (/* reexport safe */ _Pillar_index_mjs__WEBPACK_IMPORTED_MODULE_1__.allPillars),\n/* harmony export */   isType: () => (/* reexport safe */ contentlayer_client__WEBPACK_IMPORTED_MODULE_0__.isType)\n/* harmony export */ });\n/* harmony import */ var contentlayer_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! contentlayer/client */ \"(rsc)/./node_modules/contentlayer/dist/client/index.js\");\n/* harmony import */ var _Pillar_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Pillar/_index.mjs */ \"(rsc)/./.contentlayer/generated/Pillar/_index.mjs\");\n// NOTE This file is auto-generated by Contentlayer\n\n// NOTE During development Contentlayer imports from `.mjs` files to improve HMR speeds.\n// During (production) builds Contentlayer it imports from `.json` files to improve build performance.\n\n\nconst allDocuments = [\n    ..._Pillar_index_mjs__WEBPACK_IMPORTED_MODULE_1__.allPillars\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi8uY29udGVudGxheWVyL2dlbmVyYXRlZC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQSxtREFBbUQ7QUFFUDtBQUU1Qyx3RkFBd0Y7QUFDeEYsc0dBQXNHO0FBQ3REO0FBRTNCO0FBRWQsTUFBTUUsZUFBZTtPQUFJRCx5REFBVUE7Q0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXRvb2wvLi8uY29udGVudGxheWVyL2dlbmVyYXRlZC9pbmRleC5tanM/OTVkZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBOT1RFIFRoaXMgZmlsZSBpcyBhdXRvLWdlbmVyYXRlZCBieSBDb250ZW50bGF5ZXJcblxuZXhwb3J0IHsgaXNUeXBlIH0gZnJvbSAnY29udGVudGxheWVyL2NsaWVudCdcblxuLy8gTk9URSBEdXJpbmcgZGV2ZWxvcG1lbnQgQ29udGVudGxheWVyIGltcG9ydHMgZnJvbSBgLm1qc2AgZmlsZXMgdG8gaW1wcm92ZSBITVIgc3BlZWRzLlxuLy8gRHVyaW5nIChwcm9kdWN0aW9uKSBidWlsZHMgQ29udGVudGxheWVyIGl0IGltcG9ydHMgZnJvbSBgLmpzb25gIGZpbGVzIHRvIGltcHJvdmUgYnVpbGQgcGVyZm9ybWFuY2UuXG5pbXBvcnQgeyBhbGxQaWxsYXJzIH0gZnJvbSAnLi9QaWxsYXIvX2luZGV4Lm1qcydcblxuZXhwb3J0IHsgYWxsUGlsbGFycyB9XG5cbmV4cG9ydCBjb25zdCBhbGxEb2N1bWVudHMgPSBbLi4uYWxsUGlsbGFyc11cblxuXG4iXSwibmFtZXMiOlsiaXNUeXBlIiwiYWxsUGlsbGFycyIsImFsbERvY3VtZW50cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./.contentlayer/generated/index.mjs\n");

/***/ }),

/***/ "(rsc)/./app/[slug]/PillarClient.tsx":
/*!*************************************!*\
  !*** ./app/[slug]/PillarClient.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/Webiste Tool/app/[slug]/PillarClient.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/[slug]/page.tsx":
/*!*****************************!*\
  !*** ./app/[slug]/page.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PillarPage),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var contentlayer_generated__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! contentlayer/generated */ \"(rsc)/./.contentlayer/generated/index.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _PillarClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PillarClient */ \"(rsc)/./app/[slug]/PillarClient.tsx\");\n\n\n\n\nasync function generateStaticParams() {\n    return contentlayer_generated__WEBPACK_IMPORTED_MODULE_1__.allPillars.map((pillar)=>({\n            slug: pillar.slug\n        }));\n}\nasync function generateMetadata({ params }) {\n    const pillar = contentlayer_generated__WEBPACK_IMPORTED_MODULE_1__.allPillars.find((pillar)=>pillar.slug === params.slug);\n    if (!pillar) {\n        return {};\n    }\n    return {\n        title: pillar.title,\n        description: pillar.description\n    };\n}\nfunction PillarPage({ params }) {\n    const pillar = contentlayer_generated__WEBPACK_IMPORTED_MODULE_1__.allPillars.find((pillar)=>pillar.slug === params.slug);\n    if (!pillar) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.notFound)();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PillarClient__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        pillar: pillar\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/[slug]/page.tsx\",\n        lineNumber: 38,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[slug]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"AI Recommender\",\n    description: \"Find the best AI tools for your needs\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"AI Recommender\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"container mx-auto px-4 py-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-2xl mx-auto text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-4\",\n                children: \"404 - Page Not Found\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-gray-600 mb-8\",\n                children: \"The page you're looking for doesn't exist.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/\",\n                className: \"inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                children: \"Go Home\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEI7QUFFYixTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUEwQjs7Ozs7OzBCQUN4Qyw4REFBQ0U7Z0JBQUVGLFdBQVU7MEJBQTZCOzs7Ozs7MEJBRzFDLDhEQUFDSCxrREFBSUE7Z0JBQ0hNLE1BQUs7Z0JBQ0xILFdBQVU7MEJBQ1g7Ozs7Ozs7Ozs7OztBQUtQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8uL2FwcC9ub3QtZm91bmQudHN4PzVjODAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RGb3VuZCgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTJ4bCBteC1hdXRvIHRleHQtY2VudGVyXCI+XG4gICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIG1iLTRcIj40MDQgLSBQYWdlIE5vdCBGb3VuZDwvaDE+XG4gICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS02MDAgbWItOFwiPlxuICAgICAgICBUaGUgcGFnZSB5b3UncmUgbG9va2luZyBmb3IgZG9lc24ndCBleGlzdC5cbiAgICAgIDwvcD5cbiAgICAgIDxMaW5rIFxuICAgICAgICBocmVmPVwiL1wiXG4gICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1ibG9jayBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHB4LTYgcHktMyByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtNzAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgID5cbiAgICAgICAgR28gSG9tZVxuICAgICAgPC9MaW5rPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiTGluayIsIk5vdEZvdW5kIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwiaHJlZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./.contentlayer/generated/Pillar/pillars__best-ai-for-coding.mdx.json":
/*!*****************************************************************************!*\
  !*** ./.contentlayer/generated/Pillar/pillars__best-ai-for-coding.mdx.json ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"title":"Best AI for Coding & Debugging (2025) — GPT-4o vs Claude 3.5, GitHub Copilot & more","description":"Developer-focused comparison of GPT-4o, Claude 3.5 Sonnet, GitHub Copilot, and Replit Ghostwriter. Find your perfect AI coding assistant.","slug":"best-ai-for-coding-2025-gpt-4o-vs-claude-github-copilot-more","cluster":"code","template":"pillar","priority":"High","lastUpdated":"2025-07-22","body":{"raw":"\\nimport PillarLayout from \\"@/templates/PillarLayout\\"\\nimport QuizCta from \\"@/components/QuizCta\\"\\n\\n<PillarLayout quizSlug=\\"coding\\">\\n\\n<QuizCta task=\\"coding\\" />\\n\\n## The AI Coding Landscape in 2025\\n\\nThe conversation around AI coding assistants has moved far beyond simple autocomplete. In 2025, these tools are powerful collaborators capable of architecting systems, debugging multi-file repositories, and accelerating development cycles. But the fragmented market means the \\"best\\" AI is no longer a simple choice. The right tool depends entirely on your specific needs: low latency to maintain flow state, a massive context window for complex codebases, a deep plug-in ecosystem for your existing workflow, and robust licensing for enterprise security.\\n\\nThis guide provides a developer-focused comparison of the top contenders—GPT-4o, Claude 3.5 Sonnet, GitHub Copilot, and Replit Ghostwriter—to help you select the right AI co-pilot for your next project.\\n\\n---\\n\\n## The AI Coder\'s Scorecard: Specs at a Glance\\n\\nFor developers, specs matter. This chart breaks down the key models by what you care about most: cost, context, and core strengths.\\n\\n| Model | Pricing (per user/month) | Context Window | Key Strength / Ecosystem |\\n|-------|--------------------------|----------------|--------------------------|\\n| **GPT-4o** | ~$20 (API is usage-based) | 128k tokens | Versatility; a powerful \\"second brain\\" for logic and algorithms. |\\n| **Claude 3.5 Sonnet** | ~$20 (API is usage-based) | 200k tokens | Massive context for codebase analysis and complex refactoring. |\\n| **GitHub Copilot** | $19 (Business) / $39 (Enterprise) | Varies (uses GPT-4) | Deep integration with GitHub, VS Code, and the PR lifecycle. |\\n| **Replit Ghostwriter** | $20 (Pro) / $50 (Teams) | Varies | Native to the Replit cloud IDE for seamless prototyping. |\\n\\n<div style={{textAlign:\'right\',fontSize:\'0.9rem\'}}><a href=\\"/export/coding-scorecard.csv\\">Export to Sheets →</a></div>\\n\\n---\\n\\n## The Code Challenge: Simple Bugs vs. High-Context Flaws\\n\\nNot all bugs are created equal. Some are simple typos, while others are subtle logical flaws that hide deep within a large codebase. We tested the leading models with two distinct challenges to see where they shine and where they falter.\\n\\n### Snippet 1: The Flawless Fix\\n\\nThis simple Python function is meant to calculate the total price of items in a cart but has a common off-by-one error.\\n\\n**Buggy Code:**\\n\\n```python\\ndef calculate_cart_total(prices):\\n  total = 0\\n  # Bug: range stops before the last index\\n  for i in range(len(prices) - 1):\\n    total += prices[i]\\n  return total\\n\\ncart = [10, 25, 15, 5]\\nprint(f\\"Total: ${calculate_cart_total(cart)}\\") \\n# Expected output: $55\\n# Actual output: $50\\n```\\n\\n**Result:** Every model tested—GPT-4o, Claude, Copilot, and Ghostwriter—fixed this instantly. They correctly identified that the loop failed to include the last item and adjusted `range(len(prices) - 1)` to `range(len(prices))`. This is the table-stakes capability you should expect from any modern AI code generator.\\n\\n### Snippet 2: The High-Context Challenge\\n\\nThis is where premium models prove their worth. The bug here is subtle. A utility function `process_data` incorrectly uses a global `TRANSACTION_FEE` variable, but this is only apparent when you see how `process_data` is called by another function that has already applied a separate, regional tax. Only an AI that can hold the entire call stack in its context can spot the double charge.\\n\\n**Buggy Code (in a large file):**\\n\\n```javascript\\n// Defined 500 lines earlier...\\nconst TRANSACTION_FEE = 0.02; // 2% processing fee\\n\\nfunction process_data(items) {\\n  let subtotal = items.reduce((acc, item) => acc + item.price, 0);\\n  // Bug: This fee is applied redundantly, as the calling function handles taxes.\\n  return subtotal * (1 + TRANSACTION_FEE); \\n}\\n\\n// ... much later in the file ...\\n\\nfunction checkout_for_region(cart, region_config) {\\n  let regional_total = cart.reduce((acc, item) => acc + item.price, 0);\\n  \\n  // Apply regional tax correctly\\n  regional_total *= (1 + region_config.tax_rate);\\n\\n  // Send to processing, unaware that it adds another fee\\n  const final_price = process_data(cart); // Should pass regional_total\\n  \\n  console.log(`Final price is: ${final_price.toFixed(2)}`);\\n}\\n```\\n\\n**Result:**\\n\\n- **Lower-Context Models** typically suggest fixing `process_data` in isolation, perhaps by adding a parameter to toggle the fee. They miss the reason it\'s wrong—the redundant call inside `checkout_for_region`.\\n\\n- **High-Context Models** (Claude 3.5 Sonnet & GPT-4o) excelled. They identified the core issue: `checkout_for_region` performs its own calculation and then calls `process_data` with the original cart, causing a redundant calculation and an extra fee. Claude, in particular, suggested refactoring `checkout_for_region` to pass the `regional_total` into `process_data` and removing the fee logic from `process_data` entirely, demonstrating a deep understanding of the entire file\'s logic.\\n\\n---\\n\\n## The Enterprise Developer\'s Checklist\\n\\nFor teams, choosing an AI coding assistant involves more than just performance—it\'s about security, licensing, and integration. Before committing, run your choice through this permissions checklist.\\n\\n☐ **Data Privacy & Training**: Does the provider offer a zero-retention policy, guaranteeing your proprietary code is never used for training their models? (Look for Enterprise or Business tiers).\\n\\n☐ **Licensing & Indemnification**: Are the terms clear about the ownership of AI-generated code? Does the provider (like GitHub Copilot) offer intellectual property indemnification to protect your company from potential lawsuits?\\n\\n☐ **Seat Management & SSO**: Can you manage user licenses from a central dashboard and integrate with your existing Single Sign-On (SSO) solution for secure access?\\n\\n☐ **Security Compliance**: Is the tool compliant with industry standards like SOC 2 Type 2? This is non-negotiable for most enterprise environments.\\n\\n☐ **IDE & Toolchain Integration**: Does it offer first-party extensions for your team\'s preferred IDEs (VS Code, JetBrains) and version control systems? Seamless integration is key to adoption.\\n\\n---\\n\\n## Deep-dive profiles\\n\\n### GPT-4o — _the versatile problem-solver_\\n\\n**Strengths.** Excellent logical reasoning; handles multiple programming languages; strong algorithmic thinking.\\n**Weaknesses.** Smaller context window than Claude; can be verbose in explanations.\\n*Perfect for: General development, algorithm design, multi-language projects.*\\n\\n### Claude 3.5 Sonnet — _the codebase analyst_\\n\\n**Strengths.** Massive 200k token context; excellent at understanding large file relationships; thoughtful refactoring suggestions.\\n**Weaknesses.** No native IDE integration yet; API-only access.\\n*Perfect for: Large codebase analysis, complex refactoring, architectural decisions.*\\n\\n### GitHub Copilot — _the workflow integrator_\\n\\n**Strengths.** Seamless VS Code integration; understands Git context; PR and issue integration.\\n**Weaknesses.** Limited to GitHub ecosystem; enterprise pricing can be steep.\\n*Perfect for: GitHub-based teams, VS Code users, integrated development workflows.*\\n\\n### Replit Ghostwriter — _the rapid prototyper_\\n\\n**Strengths.** Instant deployment; browser-based development; great for learning and experimentation.\\n**Weaknesses.** Limited to Replit environment; less suitable for complex enterprise projects.\\n*Perfect for: Rapid prototyping, educational projects, web-based development.*\\n\\n---\\n\\n<QuizCta task=\\"coding\\" />\\n\\n---\\n\\nexport const faqData = [\\n  { q: \\"What\'s the cheapest AI coder?\\", a: \\"For free options, GitHub Copilot offers free access for students and open-source contributors. For paid plans, most AI coding assistants are around $20/month, with GitHub Copilot Business at $19/month being slightly cheaper.\\" },\\n  { q: \\"Can AI write a full application?\\", a: \\"While AI can generate significant portions of an application, including boilerplate, functions, and UI components, it cannot yet write a complete, production-ready application from a single prompt without human supervision. It excels as a \'co-pilot\' for assistance.\\" },\\n  { q: \\"Is GPT-4o good for debugging complex code?\\", a: \\"Yes, GPT-4o is excellent for debugging complex code due to its strong logical reasoning. However, for extremely large codebases, Claude 3.5 Sonnet\'s larger context window may have an advantage for understanding file relationships.\\" },\\n  { q: \\"Does GitHub Copilot steal your code?\\", a: \\"No, GitHub Copilot does not \'steal\' your code. For enterprise users, GitHub has a strict policy that private code is not used to train public models. Enterprise licenses include IP indemnification for legal protection.\\" },\\n  { q: \\"Which AI is best for Python development?\\", a: \\"All major AI coding assistants handle Python well. GPT-4o excels at algorithmic problems, Claude 3.5 is great for large Python projects, and GitHub Copilot offers the best IDE integration for Python development.\\" },\\n  { q: \\"Can AI help with code reviews?\\", a: \\"Yes, AI can assist with code reviews by identifying potential bugs, suggesting improvements, and checking for best practices. GitHub Copilot integrates directly with PR workflows, while Claude 3.5\'s large context window is excellent for reviewing entire files.\\" }\\n]\\n\\n</PillarLayout>\\n","code":"var Component=(()=>{var Yr=Object.create;var we=Object.defineProperty;var Xr=Object.getOwnPropertyDescriptor;var Qr=Object.getOwnPropertyNames;var Zr=Object.getPrototypeOf,Jr=Object.prototype.hasOwnProperty;var h=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),ei=(t,e)=>{for(var r in e)we(t,r,{get:e[r],enumerable:!0})},Zt=(t,e,r,i)=>{if(e&&typeof e==\\"object\\"||typeof e==\\"function\\")for(let o of Qr(e))!Jr.call(t,o)&&o!==r&&we(t,o,{get:()=>e[o],enumerable:!(i=Xr(e,o))||i.enumerable});return t};var Jt=(t,e,r)=>(r=t!=null?Yr(Zr(t)):{},Zt(e||!t||!t.__esModule?we(r,\\"default\\",{value:t,enumerable:!0}):r,t)),ti=t=>Zt(we({},\\"__esModule\\",{value:!0}),t);var de=h((Ns,en)=>{en.exports=React});var tn=h(Ve=>{\\"use strict\\";(function(){\\"use strict\\";var t=de(),e=Symbol.for(\\"react.element\\"),r=Symbol.for(\\"react.portal\\"),i=Symbol.for(\\"react.fragment\\"),o=Symbol.for(\\"react.strict_mode\\"),s=Symbol.for(\\"react.profiler\\"),f=Symbol.for(\\"react.provider\\"),d=Symbol.for(\\"react.context\\"),u=Symbol.for(\\"react.forward_ref\\"),p=Symbol.for(\\"react.suspense\\"),v=Symbol.for(\\"react.suspense_list\\"),S=Symbol.for(\\"react.memo\\"),P=Symbol.for(\\"react.lazy\\"),U=Symbol.for(\\"react.offscreen\\"),ae=Symbol.iterator,ge=\\"@@iterator\\";function A(n){if(n===null||typeof n!=\\"object\\")return null;var l=ae&&n[ae]||n[ge];return typeof l==\\"function\\"?l:null}var te=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function _(n){{for(var l=arguments.length,c=new Array(l>1?l-1:0),m=1;m<l;m++)c[m-1]=arguments[m];Ae(\\"error\\",n,c)}}function Ae(n,l,c){{var m=te.ReactDebugCurrentFrame,x=m.getStackAddendum();x!==\\"\\"&&(l+=\\"%s\\",c=c.concat([x]));var T=c.map(function(N){return String(N)});T.unshift(\\"Warning: \\"+l),Function.prototype.apply.call(console[n],console,T)}}var z=!1,M=!1,ie=!1,le=!1,L=!1,O;O=Symbol.for(\\"react.module.reference\\");function ye(n){return!!(typeof n==\\"string\\"||typeof n==\\"function\\"||n===i||n===s||L||n===o||n===p||n===v||le||n===U||z||M||ie||typeof n==\\"object\\"&&n!==null&&(n.$$typeof===P||n.$$typeof===S||n.$$typeof===f||n.$$typeof===d||n.$$typeof===u||n.$$typeof===O||n.getModuleId!==void 0))}function Ne(n,l,c){var m=n.displayName;if(m)return m;var x=l.displayName||l.name||\\"\\";return x!==\\"\\"?c+\\"(\\"+x+\\")\\":c}function R(n){return n.displayName||\\"Context\\"}function w(n){if(n==null)return null;if(typeof n.tag==\\"number\\"&&_(\\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\\"),typeof n==\\"function\\")return n.displayName||n.name||null;if(typeof n==\\"string\\")return n;switch(n){case i:return\\"Fragment\\";case r:return\\"Portal\\";case s:return\\"Profiler\\";case o:return\\"StrictMode\\";case p:return\\"Suspense\\";case v:return\\"SuspenseList\\"}if(typeof n==\\"object\\")switch(n.$$typeof){case d:var l=n;return R(l)+\\".Consumer\\";case f:var c=n;return R(c._context)+\\".Provider\\";case u:return Ne(n,n.render,\\"ForwardRef\\");case S:var m=n.displayName||null;return m!==null?m:w(n.type)||\\"Memo\\";case P:{var x=n,T=x._payload,N=x._init;try{return w(N(T))}catch{return null}}}return null}var J=Object.assign,ne=0,fe,xe,oe,b,re,ee,Ue;function We(){}We.__reactDisabledLog=!0;function ve(){{if(ne===0){fe=console.log,xe=console.info,oe=console.warn,b=console.error,re=console.group,ee=console.groupCollapsed,Ue=console.groupEnd;var n={configurable:!0,enumerable:!0,value:We,writable:!0};Object.defineProperties(console,{info:n,log:n,warn:n,error:n,group:n,groupCollapsed:n,groupEnd:n})}ne++}}function g(){{if(ne--,ne===0){var n={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:J({},n,{value:fe}),info:J({},n,{value:xe}),warn:J({},n,{value:oe}),error:J({},n,{value:b}),group:J({},n,{value:re}),groupCollapsed:J({},n,{value:ee}),groupEnd:J({},n,{value:Ue})})}ne<0&&_(\\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\\")}}var D=te.ReactCurrentDispatcher,Te;function Pe(n,l,c){{if(Te===void 0)try{throw Error()}catch(x){var m=x.stack.trim().match(/\\\\n( *(at )?)/);Te=m&&m[1]||\\"\\"}return`\\n`+Te+n}}var ze=!1,Ee;{var kr=typeof WeakMap==\\"function\\"?WeakMap:Map;Ee=new kr}function Mt(n,l){if(!n||ze)return\\"\\";{var c=Ee.get(n);if(c!==void 0)return c}var m;ze=!0;var x=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var T;T=D.current,D.current=null,ve();try{if(l){var N=function(){throw Error()};if(Object.defineProperty(N.prototype,\\"props\\",{set:function(){throw Error()}}),typeof Reflect==\\"object\\"&&Reflect.construct){try{Reflect.construct(N,[])}catch(j){m=j}Reflect.construct(n,[],N)}else{try{N.call()}catch(j){m=j}n.call(N.prototype)}}else{try{throw Error()}catch(j){m=j}n()}}catch(j){if(j&&m&&typeof j.stack==\\"string\\"){for(var y=j.stack.split(`\\n`),C=m.stack.split(`\\n`),E=y.length-1,k=C.length-1;E>=1&&k>=0&&y[E]!==C[k];)k--;for(;E>=1&&k>=0;E--,k--)if(y[E]!==C[k]){if(E!==1||k!==1)do if(E--,k--,k<0||y[E]!==C[k]){var W=`\\n`+y[E].replace(\\" at new \\",\\" at \\");return n.displayName&&W.includes(\\"<anonymous>\\")&&(W=W.replace(\\"<anonymous>\\",n.displayName)),typeof n==\\"function\\"&&Ee.set(n,W),W}while(E>=1&&k>=0);break}}}finally{ze=!1,D.current=T,g(),Error.prepareStackTrace=x}var ce=n?n.displayName||n.name:\\"\\",se=ce?Pe(ce):\\"\\";return typeof n==\\"function\\"&&Ee.set(n,se),se}function Rr(n,l,c){return Mt(n,!1)}function wr(n){var l=n.prototype;return!!(l&&l.isReactComponent)}function ke(n,l,c){if(n==null)return\\"\\";if(typeof n==\\"function\\")return Mt(n,wr(n));if(typeof n==\\"string\\")return Pe(n);switch(n){case p:return Pe(\\"Suspense\\");case v:return Pe(\\"SuspenseList\\")}if(typeof n==\\"object\\")switch(n.$$typeof){case u:return Rr(n.render);case S:return ke(n.type,l,c);case P:{var m=n,x=m._payload,T=m._init;try{return ke(T(x),l,c)}catch{}}}return\\"\\"}var pe=Object.prototype.hasOwnProperty,Lt={},qt=te.ReactDebugCurrentFrame;function Re(n){if(n){var l=n._owner,c=ke(n.type,n._source,l?l.type:null);qt.setExtraStackFrame(c)}else qt.setExtraStackFrame(null)}function Or(n,l,c,m,x){{var T=Function.call.bind(pe);for(var N in n)if(T(n,N)){var y=void 0;try{if(typeof n[N]!=\\"function\\"){var C=Error((m||\\"React class\\")+\\": \\"+c+\\" type `\\"+N+\\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\\"+typeof n[N]+\\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\\");throw C.name=\\"Invariant Violation\\",C}y=n[N](l,N,m,c,null,\\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\\")}catch(E){y=E}y&&!(y instanceof Error)&&(Re(x),_(\\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\\",m||\\"React class\\",c,N,typeof y),Re(null)),y instanceof Error&&!(y.message in Lt)&&(Lt[y.message]=!0,Re(x),_(\\"Failed %s type: %s\\",c,y.message),Re(null))}}}var Cr=Array.isArray;function Me(n){return Cr(n)}function Sr(n){{var l=typeof Symbol==\\"function\\"&&Symbol.toStringTag,c=l&&n[Symbol.toStringTag]||n.constructor.name||\\"Object\\";return c}}function Dr(n){try{return Ft(n),!1}catch{return!0}}function Ft(n){return\\"\\"+n}function Gt(n){if(Dr(n))return _(\\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\\",Sr(n)),Ft(n)}var me=te.ReactCurrentOwner,jr={key:!0,ref:!0,__self:!0,__source:!0},Ht,Vt,Le;Le={};function Ir(n){if(pe.call(n,\\"ref\\")){var l=Object.getOwnPropertyDescriptor(n,\\"ref\\").get;if(l&&l.isReactWarning)return!1}return n.ref!==void 0}function Ar(n){if(pe.call(n,\\"key\\")){var l=Object.getOwnPropertyDescriptor(n,\\"key\\").get;if(l&&l.isReactWarning)return!1}return n.key!==void 0}function Ur(n,l){if(typeof n.ref==\\"string\\"&&me.current&&l&&me.current.stateNode!==l){var c=w(me.current.type);Le[c]||(_(\'Component \\"%s\\" contains the string ref \\"%s\\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref\',w(me.current.type),n.ref),Le[c]=!0)}}function Wr(n,l){{var c=function(){Ht||(Ht=!0,_(\\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\\",l))};c.isReactWarning=!0,Object.defineProperty(n,\\"key\\",{get:c,configurable:!0})}}function zr(n,l){{var c=function(){Vt||(Vt=!0,_(\\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\\",l))};c.isReactWarning=!0,Object.defineProperty(n,\\"ref\\",{get:c,configurable:!0})}}var Mr=function(n,l,c,m,x,T,N){var y={$$typeof:e,type:n,key:l,ref:c,props:N,_owner:T};return y._store={},Object.defineProperty(y._store,\\"validated\\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(y,\\"_self\\",{configurable:!1,enumerable:!1,writable:!1,value:m}),Object.defineProperty(y,\\"_source\\",{configurable:!1,enumerable:!1,writable:!1,value:x}),Object.freeze&&(Object.freeze(y.props),Object.freeze(y)),y};function Lr(n,l,c,m,x){{var T,N={},y=null,C=null;c!==void 0&&(Gt(c),y=\\"\\"+c),Ar(l)&&(Gt(l.key),y=\\"\\"+l.key),Ir(l)&&(C=l.ref,Ur(l,x));for(T in l)pe.call(l,T)&&!jr.hasOwnProperty(T)&&(N[T]=l[T]);if(n&&n.defaultProps){var E=n.defaultProps;for(T in E)N[T]===void 0&&(N[T]=E[T])}if(y||C){var k=typeof n==\\"function\\"?n.displayName||n.name||\\"Unknown\\":n;y&&Wr(N,k),C&&zr(N,k)}return Mr(n,y,C,x,m,me.current,N)}}var qe=te.ReactCurrentOwner,$t=te.ReactDebugCurrentFrame;function ue(n){if(n){var l=n._owner,c=ke(n.type,n._source,l?l.type:null);$t.setExtraStackFrame(c)}else $t.setExtraStackFrame(null)}var Fe;Fe=!1;function Ge(n){return typeof n==\\"object\\"&&n!==null&&n.$$typeof===e}function Bt(){{if(qe.current){var n=w(qe.current.type);if(n)return`\\n\\nCheck the render method of \\\\``+n+\\"`.\\"}return\\"\\"}}function qr(n){{if(n!==void 0){var l=n.fileName.replace(/^.*[\\\\\\\\\\\\/]/,\\"\\"),c=n.lineNumber;return`\\n\\nCheck your code at `+l+\\":\\"+c+\\".\\"}return\\"\\"}}var Kt={};function Fr(n){{var l=Bt();if(!l){var c=typeof n==\\"string\\"?n:n.displayName||n.name;c&&(l=`\\n\\nCheck the top-level render call using <`+c+\\">.\\")}return l}}function Yt(n,l){{if(!n._store||n._store.validated||n.key!=null)return;n._store.validated=!0;var c=Fr(l);if(Kt[c])return;Kt[c]=!0;var m=\\"\\";n&&n._owner&&n._owner!==qe.current&&(m=\\" It was passed a child from \\"+w(n._owner.type)+\\".\\"),ue(n),_(\'Each child in a list should have a unique \\"key\\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.\',c,m),ue(null)}}function Xt(n,l){{if(typeof n!=\\"object\\")return;if(Me(n))for(var c=0;c<n.length;c++){var m=n[c];Ge(m)&&Yt(m,l)}else if(Ge(n))n._store&&(n._store.validated=!0);else if(n){var x=A(n);if(typeof x==\\"function\\"&&x!==n.entries)for(var T=x.call(n),N;!(N=T.next()).done;)Ge(N.value)&&Yt(N.value,l)}}}function Gr(n){{var l=n.type;if(l==null||typeof l==\\"string\\")return;var c;if(typeof l==\\"function\\")c=l.propTypes;else if(typeof l==\\"object\\"&&(l.$$typeof===u||l.$$typeof===S))c=l.propTypes;else return;if(c){var m=w(l);Or(c,n.props,\\"prop\\",m,n)}else if(l.PropTypes!==void 0&&!Fe){Fe=!0;var x=w(l);_(\\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\\",x||\\"Unknown\\")}typeof l.getDefaultProps==\\"function\\"&&!l.getDefaultProps.isReactClassApproved&&_(\\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\\")}}function Hr(n){{for(var l=Object.keys(n.props),c=0;c<l.length;c++){var m=l[c];if(m!==\\"children\\"&&m!==\\"key\\"){ue(n),_(\\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\\",m),ue(null);break}}n.ref!==null&&(ue(n),_(\\"Invalid attribute `ref` supplied to `React.Fragment`.\\"),ue(null))}}var Qt={};function Vr(n,l,c,m,x,T){{var N=ye(n);if(!N){var y=\\"\\";(n===void 0||typeof n==\\"object\\"&&n!==null&&Object.keys(n).length===0)&&(y+=\\" You likely forgot to export your component from the file it\'s defined in, or you might have mixed up default and named imports.\\");var C=qr(x);C?y+=C:y+=Bt();var E;n===null?E=\\"null\\":Me(n)?E=\\"array\\":n!==void 0&&n.$$typeof===e?(E=\\"<\\"+(w(n.type)||\\"Unknown\\")+\\" />\\",y=\\" Did you accidentally export a JSX literal instead of a component?\\"):E=typeof n,_(\\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\\",E,y)}var k=Lr(n,l,c,x,T);if(k==null)return k;if(N){var W=l.children;if(W!==void 0)if(m)if(Me(W)){for(var ce=0;ce<W.length;ce++)Xt(W[ce],n);Object.freeze&&Object.freeze(W)}else _(\\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\\");else Xt(W,n)}if(pe.call(l,\\"key\\")){var se=w(n),j=Object.keys(l).filter(function(Kr){return Kr!==\\"key\\"}),He=j.length>0?\\"{key: someKey, \\"+j.join(\\": ..., \\")+\\": ...}\\":\\"{key: someKey}\\";if(!Qt[se+He]){var Br=j.length>0?\\"{\\"+j.join(\\": ..., \\")+\\": ...}\\":\\"{}\\";_(`A props object containing a \\"key\\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />`,He,se,Br,se),Qt[se+He]=!0}}return n===i?Hr(k):Gr(k),k}}var $r=Vr;Ve.Fragment=i,Ve.jsxDEV=$r})()});var rn=h((vs,nn)=>{\\"use strict\\";nn.exports=tn()});var Oe=h(Be=>{\\"use strict\\";Be._=Be._interop_require_default=ni;function ni(t){return t&&t.__esModule?t:{default:t}}});var Ye=h(Ke=>{\\"use strict\\";Object.defineProperty(Ke,\\"__esModule\\",{value:!0});function ri(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}ri(Ke,{searchParamsToUrlQuery:function(){return ii},urlQueryToSearchParams:function(){return oi},assign:function(){return si}});function ii(t){let e={};return t.forEach((r,i)=>{typeof e[i]>\\"u\\"?e[i]=r:Array.isArray(e[i])?e[i].push(r):e[i]=[e[i],r]}),e}function on(t){return typeof t==\\"string\\"||typeof t==\\"number\\"&&!isNaN(t)||typeof t==\\"boolean\\"?String(t):\\"\\"}function oi(t){let e=new URLSearchParams;return Object.entries(t).forEach(r=>{let[i,o]=r;Array.isArray(o)?o.forEach(s=>e.append(i,on(s))):e.set(i,on(o))}),e}function si(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];return r.forEach(o=>{Array.from(o.keys()).forEach(s=>t.delete(s)),o.forEach((s,f)=>t.append(f,s))}),t}});var an=h(Xe=>{\\"use strict\\";function sn(t){if(typeof WeakMap!=\\"function\\")return null;var e=new WeakMap,r=new WeakMap;return(sn=function(i){return i?r:e})(t)}Xe._=Xe._interop_require_wildcard=ai;function ai(t,e){if(!e&&t&&t.__esModule)return t;if(t===null||typeof t!=\\"object\\"&&typeof t!=\\"function\\")return{default:t};var r=sn(e);if(r&&r.has(t))return r.get(t);var i={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if(s!==\\"default\\"&&Object.prototype.hasOwnProperty.call(t,s)){var f=o?Object.getOwnPropertyDescriptor(t,s):null;f&&(f.get||f.set)?Object.defineProperty(i,s,f):i[s]=t[s]}return i.default=t,r&&r.set(t,i),i}});var Ze=h(Qe=>{\\"use strict\\";Object.defineProperty(Qe,\\"__esModule\\",{value:!0});function li(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}li(Qe,{formatUrl:function(){return ln},urlObjectKeys:function(){return un},formatWithValidation:function(){return fi}});var ui=an(),ci=ui._(Ye()),di=/https?|ftp|gopher|file/;function ln(t){let{auth:e,hostname:r}=t,i=t.protocol||\\"\\",o=t.pathname||\\"\\",s=t.hash||\\"\\",f=t.query||\\"\\",d=!1;e=e?encodeURIComponent(e).replace(/%3A/i,\\":\\")+\\"@\\":\\"\\",t.host?d=e+t.host:r&&(d=e+(~r.indexOf(\\":\\")?\\"[\\"+r+\\"]\\":r),t.port&&(d+=\\":\\"+t.port)),f&&typeof f==\\"object\\"&&(f=String(ci.urlQueryToSearchParams(f)));let u=t.search||f&&\\"?\\"+f||\\"\\";return i&&!i.endsWith(\\":\\")&&(i+=\\":\\"),t.slashes||(!i||di.test(i))&&d!==!1?(d=\\"//\\"+(d||\\"\\"),o&&o[0]!==\\"/\\"&&(o=\\"/\\"+o)):d||(d=\\"\\"),s&&s[0]!==\\"#\\"&&(s=\\"#\\"+s),u&&u[0]!==\\"?\\"&&(u=\\"?\\"+u),o=o.replace(/[?#]/g,encodeURIComponent),u=u.replace(\\"#\\",\\"%23\\"),\\"\\"+i+d+o+u+s}var un=[\\"auth\\",\\"hash\\",\\"host\\",\\"hostname\\",\\"href\\",\\"path\\",\\"pathname\\",\\"port\\",\\"protocol\\",\\"query\\",\\"search\\",\\"slashes\\"];function fi(t){return t!==null&&typeof t==\\"object\\"&&Object.keys(t).forEach(e=>{un.includes(e)||console.warn(\\"Unknown key passed via urlObject into url.format: \\"+e)}),ln(t)}});var cn=h(Je=>{\\"use strict\\";Object.defineProperty(Je,\\"__esModule\\",{value:!0});Object.defineProperty(Je,\\"omit\\",{enumerable:!0,get:function(){return pi}});function pi(t,e){let r={};return Object.keys(t).forEach(i=>{e.includes(i)||(r[i]=t[i])}),r}});var he=h(ot=>{\\"use strict\\";Object.defineProperty(ot,\\"__esModule\\",{value:!0});function mi(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}mi(ot,{WEB_VITALS:function(){return hi},execOnce:function(){return bi},isAbsoluteUrl:function(){return gi},getLocationOrigin:function(){return dn},getURL:function(){return yi},getDisplayName:function(){return Ce},isResSent:function(){return fn},normalizeRepeatedSlashes:function(){return Ni},loadGetInitialProps:function(){return pn},SP:function(){return mn},ST:function(){return xi},DecodeError:function(){return et},NormalizeError:function(){return tt},PageNotFoundError:function(){return nt},MissingStaticPage:function(){return rt},MiddlewareNotFoundError:function(){return it},stringifyError:function(){return vi}});var hi=[\\"CLS\\",\\"FCP\\",\\"FID\\",\\"INP\\",\\"LCP\\",\\"TTFB\\"];function bi(t){let e=!1,r;return function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return e||(e=!0,r=t(...o)),r}}var _i=/^[a-zA-Z][a-zA-Z\\\\d+\\\\-.]*?:/,gi=t=>_i.test(t);function dn(){let{protocol:t,hostname:e,port:r}=window.location;return t+\\"//\\"+e+(r?\\":\\"+r:\\"\\")}function yi(){let{href:t}=window.location,e=dn();return t.substring(e.length)}function Ce(t){return typeof t==\\"string\\"?t:t.displayName||t.name||\\"Unknown\\"}function fn(t){return t.finished||t.headersSent}function Ni(t){let e=t.split(\\"?\\");return e[0].replace(/\\\\\\\\/g,\\"/\\").replace(/\\\\/\\\\/+/g,\\"/\\")+(e[1]?\\"?\\"+e.slice(1).join(\\"?\\"):\\"\\")}async function pn(t,e){var r;if((r=t.prototype)!=null&&r.getInitialProps){let s=\'\\"\'+Ce(t)+\'.getInitialProps()\\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.\';throw new Error(s)}let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await pn(e.Component,e.ctx)}:{};let o=await t.getInitialProps(e);if(i&&fn(i))return o;if(!o){let s=\'\\"\'+Ce(t)+\'.getInitialProps()\\" should resolve to an object. But found \\"\'+o+\'\\" instead.\';throw new Error(s)}return Object.keys(o).length===0&&!e.ctx&&console.warn(\\"\\"+Ce(t)+\\" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\\"),o}var mn=typeof performance<\\"u\\",xi=mn&&[\\"mark\\",\\"measure\\",\\"getEntriesByName\\"].every(t=>typeof performance[t]==\\"function\\"),et=class extends Error{},tt=class extends Error{},nt=class extends Error{constructor(e){super(),this.code=\\"ENOENT\\",this.name=\\"PageNotFoundError\\",this.message=\\"Cannot find module for page: \\"+e}},rt=class extends Error{constructor(e,r){super(),this.message=\\"Failed to load static file for page: \\"+e+\\" \\"+r}},it=class extends Error{constructor(){super(),this.code=\\"ENOENT\\",this.message=\\"Cannot find the middleware module\\"}};function vi(t){return JSON.stringify({message:t.message,stack:t.stack})}});var at=h(st=>{\\"use strict\\";Object.defineProperty(st,\\"__esModule\\",{value:!0});Object.defineProperty(st,\\"removeTrailingSlash\\",{enumerable:!0,get:function(){return Ti}});function Ti(t){return t.replace(/\\\\/$/,\\"\\")||\\"/\\"}});var Se=h(lt=>{\\"use strict\\";Object.defineProperty(lt,\\"__esModule\\",{value:!0});Object.defineProperty(lt,\\"parsePath\\",{enumerable:!0,get:function(){return Pi}});function Pi(t){let e=t.indexOf(\\"#\\"),r=t.indexOf(\\"?\\"),i=r>-1&&(e<0||r<e);return i||e>-1?{pathname:t.substring(0,i?r:e),query:i?t.substring(r,e>-1?e:void 0):\\"\\",hash:e>-1?t.slice(e):\\"\\"}:{pathname:t,query:\\"\\",hash:\\"\\"}}});var be=h((q,bn)=>{\\"use strict\\";Object.defineProperty(q,\\"__esModule\\",{value:!0});Object.defineProperty(q,\\"normalizePathTrailingSlash\\",{enumerable:!0,get:function(){return ki}});var hn=at(),Ei=Se(),ki=t=>{if(!t.startsWith(\\"/\\")||process.env.__NEXT_MANUAL_TRAILING_SLASH)return t;let{pathname:e,query:r,hash:i}=(0,Ei.parsePath)(t);return process.env.__NEXT_TRAILING_SLASH?/\\\\.[^/]+\\\\/?$/.test(e)?\\"\\"+(0,hn.removeTrailingSlash)(e)+r+i:e.endsWith(\\"/\\")?\\"\\"+e+r+i:e+\\"/\\"+r+i:\\"\\"+(0,hn.removeTrailingSlash)(e)+r+i};(typeof q.default==\\"function\\"||typeof q.default==\\"object\\"&&q.default!==null)&&typeof q.default.__esModule>\\"u\\"&&(Object.defineProperty(q.default,\\"__esModule\\",{value:!0}),Object.assign(q.default,q),bn.exports=q.default)});var ct=h(ut=>{\\"use strict\\";Object.defineProperty(ut,\\"__esModule\\",{value:!0});Object.defineProperty(ut,\\"pathHasPrefix\\",{enumerable:!0,get:function(){return wi}});var Ri=Se();function wi(t,e){if(typeof t!=\\"string\\")return!1;let{pathname:r}=(0,Ri.parsePath)(t);return r===e||r.startsWith(e+\\"/\\")}});var gn=h((F,_n)=>{\\"use strict\\";Object.defineProperty(F,\\"__esModule\\",{value:!0});Object.defineProperty(F,\\"hasBasePath\\",{enumerable:!0,get:function(){return Si}});var Oi=ct(),Ci=process.env.__NEXT_ROUTER_BASEPATH||\\"\\";function Si(t){return(0,Oi.pathHasPrefix)(t,Ci)}(typeof F.default==\\"function\\"||typeof F.default==\\"object\\"&&F.default!==null)&&typeof F.default.__esModule>\\"u\\"&&(Object.defineProperty(F.default,\\"__esModule\\",{value:!0}),Object.assign(F.default,F),_n.exports=F.default)});var ft=h(dt=>{\\"use strict\\";Object.defineProperty(dt,\\"__esModule\\",{value:!0});Object.defineProperty(dt,\\"isLocalURL\\",{enumerable:!0,get:function(){return ji}});var yn=he(),Di=gn();function ji(t){if(!(0,yn.isAbsoluteUrl)(t))return!0;try{let e=(0,yn.getLocationOrigin)(),r=new URL(t,e);return r.origin===e&&(0,Di.hasBasePath)(r.pathname)}catch{return!1}}});var Nn=h(mt=>{\\"use strict\\";Object.defineProperty(mt,\\"__esModule\\",{value:!0});Object.defineProperty(mt,\\"getSortedRoutes\\",{enumerable:!0,get:function(){return Ii}});var pt=class t{insert(e){this._insert(e.split(\\"/\\").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){e===void 0&&(e=\\"/\\");let r=[...this.children.keys()].sort();this.slugName!==null&&r.splice(r.indexOf(\\"[]\\"),1),this.restSlugName!==null&&r.splice(r.indexOf(\\"[...]\\"),1),this.optionalRestSlugName!==null&&r.splice(r.indexOf(\\"[[...]]\\"),1);let i=r.map(o=>this.children.get(o)._smoosh(\\"\\"+e+o+\\"/\\")).reduce((o,s)=>[...o,...s],[]);if(this.slugName!==null&&i.push(...this.children.get(\\"[]\\")._smoosh(e+\\"[\\"+this.slugName+\\"]/\\")),!this.placeholder){let o=e===\\"/\\"?\\"/\\":e.slice(0,-1);if(this.optionalRestSlugName!=null)throw new Error(\'You cannot define a route with the same specificity as a optional catch-all route (\\"\'+o+\'\\" and \\"\'+o+\\"[[...\\"+this.optionalRestSlugName+\']]\\").\');i.unshift(o)}return this.restSlugName!==null&&i.push(...this.children.get(\\"[...]\\")._smoosh(e+\\"[...\\"+this.restSlugName+\\"]/\\")),this.optionalRestSlugName!==null&&i.push(...this.children.get(\\"[[...]]\\")._smoosh(e+\\"[[...\\"+this.optionalRestSlugName+\\"]]/\\")),i}_insert(e,r,i){if(e.length===0){this.placeholder=!1;return}if(i)throw new Error(\\"Catch-all must be the last part of the URL.\\");let o=e[0];if(o.startsWith(\\"[\\")&&o.endsWith(\\"]\\")){let d=function(u,p){if(u!==null&&u!==p)throw new Error(\\"You cannot use different slug names for the same dynamic path (\'\\"+u+\\"\' !== \'\\"+p+\\"\').\\");r.forEach(v=>{if(v===p)throw new Error(\'You cannot have the same slug name \\"\'+p+\'\\" repeat within a single dynamic path\');if(v.replace(/\\\\W/g,\\"\\")===o.replace(/\\\\W/g,\\"\\"))throw new Error(\'You cannot have the slug names \\"\'+v+\'\\" and \\"\'+p+\'\\" differ only by non-word symbols within a single dynamic path\')}),r.push(p)},s=o.slice(1,-1),f=!1;if(s.startsWith(\\"[\\")&&s.endsWith(\\"]\\")&&(s=s.slice(1,-1),f=!0),s.startsWith(\\"...\\")&&(s=s.substring(3),i=!0),s.startsWith(\\"[\\")||s.endsWith(\\"]\\"))throw new Error(\\"Segment names may not start or end with extra brackets (\'\\"+s+\\"\').\\");if(s.startsWith(\\".\\"))throw new Error(\\"Segment names may not start with erroneous periods (\'\\"+s+\\"\').\\");if(i)if(f){if(this.restSlugName!=null)throw new Error(\'You cannot use both an required and optional catch-all route at the same level (\\"[...\'+this.restSlugName+\']\\" and \\"\'+e[0]+\'\\" ).\');d(this.optionalRestSlugName,s),this.optionalRestSlugName=s,o=\\"[[...]]\\"}else{if(this.optionalRestSlugName!=null)throw new Error(\'You cannot use both an optional and required catch-all route at the same level (\\"[[...\'+this.optionalRestSlugName+\']]\\" and \\"\'+e[0]+\'\\").\');d(this.restSlugName,s),this.restSlugName=s,o=\\"[...]\\"}else{if(f)throw new Error(\'Optional route parameters are not yet supported (\\"\'+e[0]+\'\\").\');d(this.slugName,s),this.slugName=s,o=\\"[]\\"}}this.children.has(o)||this.children.set(o,new t),this.children.get(o)._insert(e.slice(1),r,i)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}};function Ii(t){let e=new pt;return t.forEach(r=>e.insert(r)),e.smoosh()}});var xn=h(ht=>{\\"use strict\\";Object.defineProperty(ht,\\"__esModule\\",{value:!0});Object.defineProperty(ht,\\"isDynamicRoute\\",{enumerable:!0,get:function(){return Ui}});var Ai=/\\\\/\\\\[[^/]+?\\\\](?=\\\\/|$)/;function Ui(t){return Ai.test(t)}});var vn=h(bt=>{\\"use strict\\";Object.defineProperty(bt,\\"__esModule\\",{value:!0});function Wi(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}Wi(bt,{getSortedRoutes:function(){return zi.getSortedRoutes},isDynamicRoute:function(){return Mi.isDynamicRoute}});var zi=Nn(),Mi=xn()});var Tn=h(_t=>{\\"use strict\\";Object.defineProperty(_t,\\"__esModule\\",{value:!0});Object.defineProperty(_t,\\"getRouteMatcher\\",{enumerable:!0,get:function(){return qi}});var Li=he();function qi(t){let{re:e,groups:r}=t;return i=>{let o=e.exec(i);if(!o)return!1;let s=d=>{try{return decodeURIComponent(d)}catch{throw new Li.DecodeError(\\"failed to decode param\\")}},f={};return Object.keys(r).forEach(d=>{let u=r[d],p=o[u.pos];p!==void 0&&(f[d]=~p.indexOf(\\"/\\")?p.split(\\"/\\").map(v=>s(v)):u.repeat?[s(p)]:s(p))}),f}}});var Pn=h(gt=>{\\"use strict\\";Object.defineProperty(gt,\\"__esModule\\",{value:!0});Object.defineProperty(gt,\\"ensureLeadingSlash\\",{enumerable:!0,get:function(){return Fi}});function Fi(t){return t.startsWith(\\"/\\")?t:\\"/\\"+t}});var En=h(yt=>{\\"use strict\\";Object.defineProperty(yt,\\"__esModule\\",{value:!0});Object.defineProperty(yt,\\"isGroupSegment\\",{enumerable:!0,get:function(){return Gi}});function Gi(t){return t[0]===\\"(\\"&&t.endsWith(\\")\\")}});var kn=h(Nt=>{\\"use strict\\";Object.defineProperty(Nt,\\"__esModule\\",{value:!0});function Hi(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}Hi(Nt,{normalizeAppPath:function(){return Bi},normalizeRscPath:function(){return Ki}});var Vi=Pn(),$i=En();function Bi(t){return(0,Vi.ensureLeadingSlash)(t.split(\\"/\\").reduce((e,r,i,o)=>!r||(0,$i.isGroupSegment)(r)||r[0]===\\"@\\"||(r===\\"page\\"||r===\\"route\\")&&i===o.length-1?e:e+\\"/\\"+r,\\"\\"))}function Ki(t,e){return e?t.replace(/\\\\.rsc($|\\\\?)/,\\"$1\\"):t}});var Rn=h(vt=>{\\"use strict\\";Object.defineProperty(vt,\\"__esModule\\",{value:!0});function Yi(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}Yi(vt,{INTERCEPTION_ROUTE_MARKERS:function(){return xt},isInterceptionRouteAppPath:function(){return Qi},extractInterceptionRouteInformation:function(){return Zi}});var Xi=kn(),xt=[\\"(..)(..)\\",\\"(.)\\",\\"(..)\\",\\"(...)\\"];function Qi(t){return t.split(\\"/\\").find(e=>xt.find(r=>e.startsWith(r)))!==void 0}function Zi(t){let e,r,i;for(let o of t.split(\\"/\\"))if(r=xt.find(s=>o.startsWith(s)),r){[e,i]=t.split(r,2);break}if(!e||!r||!i)throw new Error(`Invalid interception route: ${t}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(e=(0,Xi.normalizeAppPath)(e),r){case\\"(.)\\":e===\\"/\\"?i=`/${i}`:i=e+\\"/\\"+i;break;case\\"(..)\\":if(e===\\"/\\")throw new Error(`Invalid interception route: ${t}. Cannot use (..) marker at the root level, use (.) instead.`);i=e.split(\\"/\\").slice(0,-1).concat(i).join(\\"/\\");break;case\\"(...)\\":i=\\"/\\"+i;break;case\\"(..)(..)\\":let o=e.split(\\"/\\");if(o.length<=2)throw new Error(`Invalid interception route: ${t}. Cannot use (..)(..) marker at the root level or one level up.`);i=o.slice(0,-2).concat(i).join(\\"/\\");break;default:throw new Error(\\"Invariant: unexpected marker\\")}return{interceptingRoute:e,interceptedRoute:i}}});var wn=h(Tt=>{\\"use strict\\";Object.defineProperty(Tt,\\"__esModule\\",{value:!0});Object.defineProperty(Tt,\\"escapeStringRegexp\\",{enumerable:!0,get:function(){return to}});var Ji=/[|\\\\\\\\{}()[\\\\]^$+*?.-]/,eo=/[|\\\\\\\\{}()[\\\\]^$+*?.-]/g;function to(t){return Ji.test(t)?t.replace(eo,\\"\\\\\\\\$&\\"):t}});var An=h(kt=>{\\"use strict\\";Object.defineProperty(kt,\\"__esModule\\",{value:!0});function no(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}no(kt,{getRouteRegex:function(){return jn},getNamedRouteRegex:function(){return so},getNamedMiddlewareRegex:function(){return ao}});var Cn=Rn(),Pt=wn(),Sn=at(),ro=\\"nxtP\\",io=\\"nxtI\\";function Et(t){let e=t.startsWith(\\"[\\")&&t.endsWith(\\"]\\");e&&(t=t.slice(1,-1));let r=t.startsWith(\\"...\\");return r&&(t=t.slice(3)),{key:t,repeat:r,optional:e}}function Dn(t){let e=(0,Sn.removeTrailingSlash)(t).slice(1).split(\\"/\\"),r={},i=1;return{parameterizedRoute:e.map(o=>{let s=Cn.INTERCEPTION_ROUTE_MARKERS.find(d=>o.startsWith(d)),f=o.match(/\\\\[((?:\\\\[.*\\\\])|.+)\\\\]/);if(s&&f){let{key:d,optional:u,repeat:p}=Et(f[1]);return r[d]={pos:i++,repeat:p,optional:u},\\"/\\"+(0,Pt.escapeStringRegexp)(s)+\\"([^/]+?)\\"}else if(f){let{key:d,repeat:u,optional:p}=Et(f[1]);return r[d]={pos:i++,repeat:u,optional:p},u?p?\\"(?:/(.+?))?\\":\\"/(.+?)\\":\\"/([^/]+?)\\"}else return\\"/\\"+(0,Pt.escapeStringRegexp)(o)}).join(\\"\\"),groups:r}}function jn(t){let{parameterizedRoute:e,groups:r}=Dn(t);return{re:new RegExp(\\"^\\"+e+\\"(?:/)?$\\"),groups:r}}function oo(){let t=0;return()=>{let e=\\"\\",r=++t;for(;r>0;)e+=String.fromCharCode(97+(r-1)%26),r=Math.floor((r-1)/26);return e}}function On(t){let{getSafeRouteKey:e,segment:r,routeKeys:i,keyPrefix:o}=t,{key:s,optional:f,repeat:d}=Et(r),u=s.replace(/\\\\W/g,\\"\\");o&&(u=\\"\\"+o+u);let p=!1;return(u.length===0||u.length>30)&&(p=!0),isNaN(parseInt(u.slice(0,1)))||(p=!0),p&&(u=e()),o?i[u]=\\"\\"+o+s:i[u]=\\"\\"+s,d?f?\\"(?:/(?<\\"+u+\\">.+?))?\\":\\"/(?<\\"+u+\\">.+?)\\":\\"/(?<\\"+u+\\">[^/]+?)\\"}function In(t,e){let r=(0,Sn.removeTrailingSlash)(t).slice(1).split(\\"/\\"),i=oo(),o={};return{namedParameterizedRoute:r.map(s=>{let f=Cn.INTERCEPTION_ROUTE_MARKERS.some(u=>s.startsWith(u)),d=s.match(/\\\\[((?:\\\\[.*\\\\])|.+)\\\\]/);return f&&d?On({getSafeRouteKey:i,segment:d[1],routeKeys:o,keyPrefix:e?io:void 0}):d?On({getSafeRouteKey:i,segment:d[1],routeKeys:o,keyPrefix:e?ro:void 0}):\\"/\\"+(0,Pt.escapeStringRegexp)(s)}).join(\\"\\"),routeKeys:o}}function so(t,e){let r=In(t,e);return{...jn(t),namedRegex:\\"^\\"+r.namedParameterizedRoute+\\"(?:/)?$\\",routeKeys:r.routeKeys}}function ao(t,e){let{parameterizedRoute:r}=Dn(t),{catchAll:i=!0}=e;if(r===\\"/\\")return{namedRegex:\\"^/\\"+(i?\\".*\\":\\"\\")+\\"$\\"};let{namedParameterizedRoute:o}=In(t,!1),s=i?\\"(?:(/.*)?)\\":\\"\\";return{namedRegex:\\"^\\"+o+s+\\"$\\"}}});var Un=h(Rt=>{\\"use strict\\";Object.defineProperty(Rt,\\"__esModule\\",{value:!0});Object.defineProperty(Rt,\\"interpolateAs\\",{enumerable:!0,get:function(){return co}});var lo=Tn(),uo=An();function co(t,e,r){let i=\\"\\",o=(0,uo.getRouteRegex)(t),s=o.groups,f=(e!==t?(0,lo.getRouteMatcher)(o)(e):\\"\\")||r;i=t;let d=Object.keys(s);return d.every(u=>{let p=f[u]||\\"\\",{repeat:v,optional:S}=s[u],P=\\"[\\"+(v?\\"...\\":\\"\\")+u+\\"]\\";return S&&(P=(p?\\"\\":\\"/\\")+\\"[\\"+P+\\"]\\"),v&&!Array.isArray(p)&&(p=[p]),(S||u in f)&&(i=i.replace(P,v?p.map(U=>encodeURIComponent(U)).join(\\"/\\"):encodeURIComponent(p))||\\"/\\")})||(i=\\"\\"),{params:d,result:i}}});var Mn=h((G,zn)=>{\\"use strict\\";Object.defineProperty(G,\\"__esModule\\",{value:!0});Object.defineProperty(G,\\"resolveHref\\",{enumerable:!0,get:function(){return yo}});var fo=Ye(),Wn=Ze(),po=cn(),mo=he(),ho=be(),bo=ft(),_o=vn(),go=Un();function yo(t,e,r){let i,o=typeof e==\\"string\\"?e:(0,Wn.formatWithValidation)(e),s=o.match(/^[a-zA-Z]{1,}:\\\\/\\\\//),f=s?o.slice(s[0].length):o;if((f.split(\\"?\\")[0]||\\"\\").match(/(\\\\/\\\\/|\\\\\\\\)/)){console.error(\\"Invalid href \'\\"+o+\\"\' passed to next/router in page: \'\\"+t.pathname+\\"\'. Repeated forward-slashes (//) or backslashes \\\\\\\\ are not valid in the href.\\");let u=(0,mo.normalizeRepeatedSlashes)(f);o=(s?s[0]:\\"\\")+u}if(!(0,bo.isLocalURL)(o))return r?[o]:o;try{i=new URL(o.startsWith(\\"#\\")?t.asPath:t.pathname,\\"http://n\\")}catch{i=new URL(\\"/\\",\\"http://n\\")}try{let u=new URL(o,i);u.pathname=(0,ho.normalizePathTrailingSlash)(u.pathname);let p=\\"\\";if((0,_o.isDynamicRoute)(u.pathname)&&u.searchParams&&r){let S=(0,fo.searchParamsToUrlQuery)(u.searchParams),{result:P,params:U}=(0,go.interpolateAs)(u.pathname,u.pathname,S);P&&(p=(0,Wn.formatWithValidation)({pathname:P,hash:u.hash,query:(0,po.omit)(S,U)}))}let v=u.origin===i.origin?u.href.slice(u.origin.length):u.href;return r?[v,p||v]:v}catch{return r?[o]:o}}(typeof G.default==\\"function\\"||typeof G.default==\\"object\\"&&G.default!==null)&&typeof G.default.__esModule>\\"u\\"&&(Object.defineProperty(G.default,\\"__esModule\\",{value:!0}),Object.assign(G.default,G),zn.exports=G.default)});var Ot=h(wt=>{\\"use strict\\";Object.defineProperty(wt,\\"__esModule\\",{value:!0});Object.defineProperty(wt,\\"addPathPrefix\\",{enumerable:!0,get:function(){return xo}});var No=Se();function xo(t,e){if(!t.startsWith(\\"/\\")||!e)return t;let{pathname:r,query:i,hash:o}=(0,No.parsePath)(t);return\\"\\"+e+r+i+o}});var qn=h(Ct=>{\\"use strict\\";Object.defineProperty(Ct,\\"__esModule\\",{value:!0});Object.defineProperty(Ct,\\"addLocale\\",{enumerable:!0,get:function(){return To}});var vo=Ot(),Ln=ct();function To(t,e,r,i){if(!e||e===r)return t;let o=t.toLowerCase();return!i&&((0,Ln.pathHasPrefix)(o,\\"/api\\")||(0,Ln.pathHasPrefix)(o,\\"/\\"+e.toLowerCase()))?t:(0,vo.addPathPrefix)(t,\\"/\\"+e)}});var Gn=h((H,Fn)=>{\\"use strict\\";Object.defineProperty(H,\\"__esModule\\",{value:!0});Object.defineProperty(H,\\"addLocale\\",{enumerable:!0,get:function(){return Eo}});var Po=be(),Eo=function(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];return process.env.__NEXT_I18N_SUPPORT?(0,Po.normalizePathTrailingSlash)(qn().addLocale(t,...r)):t};(typeof H.default==\\"function\\"||typeof H.default==\\"object\\"&&H.default!==null)&&typeof H.default.__esModule>\\"u\\"&&(Object.defineProperty(H.default,\\"__esModule\\",{value:!0}),Object.assign(H.default,H),Fn.exports=H.default)});var Vn=h(St=>{\\"use strict\\";Object.defineProperty(St,\\"__esModule\\",{value:!0});Object.defineProperty(St,\\"RouterContext\\",{enumerable:!0,get:function(){return Hn}});var ko=Oe(),Ro=ko._(de()),Hn=Ro.default.createContext(null);Hn.displayName=\\"RouterContext\\"});var Xn=h(jt=>{\\"use client\\";\\"use strict\\";Object.defineProperty(jt,\\"__esModule\\",{value:!0});function wo(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}wo(jt,{CacheStates:function(){return Dt},AppRouterContext:function(){return $n},LayoutRouterContext:function(){return Bn},GlobalLayoutRouterContext:function(){return Kn},TemplateContext:function(){return Yn}});var Oo=Oe(),De=Oo._(de()),Dt;(function(t){t.LAZY_INITIALIZED=\\"LAZYINITIALIZED\\",t.DATA_FETCH=\\"DATAFETCH\\",t.READY=\\"READY\\"})(Dt||(Dt={}));var $n=De.default.createContext(null),Bn=De.default.createContext(null),Kn=De.default.createContext(null),Yn=De.default.createContext(null);$n.displayName=\\"AppRouterContext\\",Bn.displayName=\\"LayoutRouterContext\\",Kn.displayName=\\"GlobalLayoutRouterContext\\",Yn.displayName=\\"TemplateContext\\"});var Zn=h((V,Qn)=>{\\"use strict\\";Object.defineProperty(V,\\"__esModule\\",{value:!0});function Co(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}Co(V,{requestIdleCallback:function(){return So},cancelIdleCallback:function(){return Do}});var So=typeof self<\\"u\\"&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(t){let e=Date.now();return self.setTimeout(function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})},1)},Do=typeof self<\\"u\\"&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(t){return clearTimeout(t)};(typeof V.default==\\"function\\"||typeof V.default==\\"object\\"&&V.default!==null)&&typeof V.default.__esModule>\\"u\\"&&(Object.defineProperty(V.default,\\"__esModule\\",{value:!0}),Object.assign(V.default,V),Qn.exports=V.default)});var nr=h(($,tr)=>{\\"use strict\\";Object.defineProperty($,\\"__esModule\\",{value:!0});Object.defineProperty($,\\"useIntersection\\",{enumerable:!0,get:function(){return Ao}});var _e=de(),Jn=Zn(),er=typeof IntersectionObserver==\\"function\\",It=new Map,je=[];function jo(t){let e={root:t.root||null,margin:t.rootMargin||\\"\\"},r=je.find(f=>f.root===e.root&&f.margin===e.margin),i;if(r&&(i=It.get(r),i))return i;let o=new Map,s=new IntersectionObserver(f=>{f.forEach(d=>{let u=o.get(d.target),p=d.isIntersecting||d.intersectionRatio>0;u&&p&&u(p)})},t);return i={id:e,observer:s,elements:o},je.push(e),It.set(e,i),i}function Io(t,e,r){let{id:i,observer:o,elements:s}=jo(r);return s.set(t,e),o.observe(t),function(){if(s.delete(t),o.unobserve(t),s.size===0){o.disconnect(),It.delete(i);let d=je.findIndex(u=>u.root===i.root&&u.margin===i.margin);d>-1&&je.splice(d,1)}}}function Ao(t){let{rootRef:e,rootMargin:r,disabled:i}=t,o=i||!er,[s,f]=(0,_e.useState)(!1),d=(0,_e.useRef)(null),u=(0,_e.useCallback)(v=>{d.current=v},[]);(0,_e.useEffect)(()=>{if(er){if(o||s)return;let v=d.current;if(v&&v.tagName)return Io(v,P=>P&&f(P),{root:e?.current,rootMargin:r})}else if(!s){let v=(0,Jn.requestIdleCallback)(()=>f(!0));return()=>(0,Jn.cancelIdleCallback)(v)}},[o,r,e,s,d.current]);let p=(0,_e.useCallback)(()=>{f(!1)},[]);return[u,s,p]}(typeof $.default==\\"function\\"||typeof $.default==\\"object\\"&&$.default!==null)&&typeof $.default.__esModule>\\"u\\"&&(Object.defineProperty($.default,\\"__esModule\\",{value:!0}),Object.assign($.default,$),tr.exports=$.default)});var rr=h(At=>{\\"use strict\\";Object.defineProperty(At,\\"__esModule\\",{value:!0});Object.defineProperty(At,\\"normalizeLocalePath\\",{enumerable:!0,get:function(){return Uo}});function Uo(t,e){let r,i=t.split(\\"/\\");return(e||[]).some(o=>i[1]&&i[1].toLowerCase()===o.toLowerCase()?(r=o,i.splice(1,1),t=i.join(\\"/\\")||\\"/\\",!0):!1),{pathname:t,detectedLocale:r}}});var or=h((B,ir)=>{\\"use strict\\";Object.defineProperty(B,\\"__esModule\\",{value:!0});Object.defineProperty(B,\\"normalizeLocalePath\\",{enumerable:!0,get:function(){return Wo}});var Wo=(t,e)=>process.env.__NEXT_I18N_SUPPORT?rr().normalizeLocalePath(t,e):{pathname:t,detectedLocale:void 0};(typeof B.default==\\"function\\"||typeof B.default==\\"object\\"&&B.default!==null)&&typeof B.default.__esModule>\\"u\\"&&(Object.defineProperty(B.default,\\"__esModule\\",{value:!0}),Object.assign(B.default,B),ir.exports=B.default)});var sr=h(Ut=>{\\"use strict\\";Object.defineProperty(Ut,\\"__esModule\\",{value:!0});Object.defineProperty(Ut,\\"detectDomainLocale\\",{enumerable:!0,get:function(){return zo}});function zo(t,e,r){if(t){r&&(r=r.toLowerCase());for(let s of t){var i,o;let f=(i=s.domain)==null?void 0:i.split(\\":\\")[0].toLowerCase();if(e===f||r===s.defaultLocale.toLowerCase()||(o=s.locales)!=null&&o.some(d=>d.toLowerCase()===r))return s}}}});var lr=h((K,ar)=>{\\"use strict\\";Object.defineProperty(K,\\"__esModule\\",{value:!0});Object.defineProperty(K,\\"detectDomainLocale\\",{enumerable:!0,get:function(){return Mo}});var Mo=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];if(process.env.__NEXT_I18N_SUPPORT)return sr().detectDomainLocale(...e)};(typeof K.default==\\"function\\"||typeof K.default==\\"object\\"&&K.default!==null)&&typeof K.default.__esModule>\\"u\\"&&(Object.defineProperty(K.default,\\"__esModule\\",{value:!0}),Object.assign(K.default,K),ar.exports=K.default)});var cr=h((Y,ur)=>{\\"use strict\\";Object.defineProperty(Y,\\"__esModule\\",{value:!0});Object.defineProperty(Y,\\"getDomainLocale\\",{enumerable:!0,get:function(){return Fo}});var Lo=be(),qo=process.env.__NEXT_ROUTER_BASEPATH||\\"\\";function Fo(t,e,r,i){if(process.env.__NEXT_I18N_SUPPORT){let o=or().normalizeLocalePath,s=lr().detectDomainLocale,f=e||o(t,r).detectedLocale,d=s(i,void 0,f);if(d){let u=\\"http\\"+(d.http?\\"\\":\\"s\\")+\\"://\\",p=f===d.defaultLocale?\\"\\":\\"/\\"+f;return\\"\\"+u+d.domain+(0,Lo.normalizePathTrailingSlash)(\\"\\"+qo+p+t)}return!1}else return!1}(typeof Y.default==\\"function\\"||typeof Y.default==\\"object\\"&&Y.default!==null)&&typeof Y.default.__esModule>\\"u\\"&&(Object.defineProperty(Y.default,\\"__esModule\\",{value:!0}),Object.assign(Y.default,Y),ur.exports=Y.default)});var fr=h((X,dr)=>{\\"use strict\\";Object.defineProperty(X,\\"__esModule\\",{value:!0});Object.defineProperty(X,\\"addBasePath\\",{enumerable:!0,get:function(){return $o}});var Go=Ot(),Ho=be(),Vo=process.env.__NEXT_ROUTER_BASEPATH||\\"\\";function $o(t,e){return(0,Ho.normalizePathTrailingSlash)(process.env.__NEXT_MANUAL_CLIENT_BASE_PATH&&!e?t:(0,Go.addPathPrefix)(t,Vo))}(typeof X.default==\\"function\\"||typeof X.default==\\"object\\"&&X.default!==null)&&typeof X.default.__esModule>\\"u\\"&&(Object.defineProperty(X.default,\\"__esModule\\",{value:!0}),Object.assign(X.default,X),dr.exports=X.default)});var mr=h((Q,pr)=>{\\"use strict\\";Object.defineProperty(Q,\\"__esModule\\",{value:!0});function Bo(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}Bo(Q,{PrefetchKind:function(){return Wt},ACTION_REFRESH:function(){return Ko},ACTION_NAVIGATE:function(){return Yo},ACTION_RESTORE:function(){return Xo},ACTION_SERVER_PATCH:function(){return Qo},ACTION_PREFETCH:function(){return Zo},ACTION_FAST_REFRESH:function(){return Jo},ACTION_SERVER_ACTION:function(){return es}});var Ko=\\"refresh\\",Yo=\\"navigate\\",Xo=\\"restore\\",Qo=\\"server-patch\\",Zo=\\"prefetch\\",Jo=\\"fast-refresh\\",es=\\"server-action\\",Wt;(function(t){t.AUTO=\\"auto\\",t.FULL=\\"full\\",t.TEMPORARY=\\"temporary\\"})(Wt||(Wt={}));(typeof Q.default==\\"function\\"||typeof Q.default==\\"object\\"&&Q.default!==null)&&typeof Q.default.__esModule>\\"u\\"&&(Object.defineProperty(Q.default,\\"__esModule\\",{value:!0}),Object.assign(Q.default,Q),pr.exports=Q.default)});var xr=h((Z,Nr)=>{\\"use client\\";\\"use strict\\";Object.defineProperty(Z,\\"__esModule\\",{value:!0});Object.defineProperty(Z,\\"default\\",{enumerable:!0,get:function(){return ps}});var ts=Oe(),I=ts._(de()),hr=Mn(),yr=ft(),ns=Ze(),rs=he(),is=Gn(),os=Vn(),ss=Xn(),as=nr(),ls=cr(),us=fr(),br=mr(),_r=new Set;function zt(t,e,r,i,o,s){if(typeof window>\\"u\\"||!s&&!(0,yr.isLocalURL)(e))return;if(!i.bypassPrefetchedCheck){let d=typeof i.locale<\\"u\\"?i.locale:\\"locale\\"in t?t.locale:void 0,u=e+\\"%\\"+r+\\"%\\"+d;if(_r.has(u))return;_r.add(u)}let f=s?t.prefetch(e,o):t.prefetch(e,r,i);Promise.resolve(f).catch(d=>{throw d})}function cs(t){let r=t.currentTarget.getAttribute(\\"target\\");return r&&r!==\\"_self\\"||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&t.nativeEvent.which===2}function ds(t,e,r,i,o,s,f,d,u,p){let{nodeName:v}=t.currentTarget;if(v.toUpperCase()===\\"A\\"&&(cs(t)||!u&&!(0,yr.isLocalURL)(r)))return;t.preventDefault();let P=()=>{let U=f??!0;\\"beforePopState\\"in e?e[o?\\"replace\\":\\"push\\"](r,i,{shallow:s,locale:d,scroll:U}):e[o?\\"replace\\":\\"push\\"](i||r,{forceOptimisticNavigation:!p,scroll:U})};u?I.default.startTransition(P):P()}function gr(t){return typeof t==\\"string\\"?t:(0,ns.formatUrl)(t)}var fs=I.default.forwardRef(function(e,r){let i,{href:o,as:s,children:f,prefetch:d=null,passHref:u,replace:p,shallow:v,scroll:S,locale:P,onClick:U,onMouseEnter:ae,onTouchStart:ge,legacyBehavior:A=!1,...te}=e;i=f,A&&(typeof i==\\"string\\"||typeof i==\\"number\\")&&(i=I.default.createElement(\\"a\\",null,i));let _=I.default.useContext(os.RouterContext),Ae=I.default.useContext(ss.AppRouterContext),z=_??Ae,M=!_,ie=d!==!1,le=d===null?br.PrefetchKind.AUTO:br.PrefetchKind.FULL;{let b=function(g){return new Error(\\"Failed prop type: The prop `\\"+g.key+\\"` expects a \\"+g.expected+\\" in `<Link>`, but got `\\"+g.actual+\\"` instead.\\"+(typeof window<\\"u\\"?`\\nOpen your browser\'s console to view the Component stack trace.`:\\"\\"))};Object.keys({href:!0}).forEach(g=>{if(g===\\"href\\"){if(e[g]==null||typeof e[g]!=\\"string\\"&&typeof e[g]!=\\"object\\")throw b({key:g,expected:\\"`string` or `object`\\",actual:e[g]===null?\\"null\\":typeof e[g]})}else{let D=g}}),Object.keys({as:!0,replace:!0,scroll:!0,shallow:!0,passHref:!0,prefetch:!0,locale:!0,onClick:!0,onMouseEnter:!0,onTouchStart:!0,legacyBehavior:!0}).forEach(g=>{let D=typeof e[g];if(g===\\"as\\"){if(e[g]&&D!==\\"string\\"&&D!==\\"object\\")throw b({key:g,expected:\\"`string` or `object`\\",actual:D})}else if(g===\\"locale\\"){if(e[g]&&D!==\\"string\\")throw b({key:g,expected:\\"`string`\\",actual:D})}else if(g===\\"onClick\\"||g===\\"onMouseEnter\\"||g===\\"onTouchStart\\"){if(e[g]&&D!==\\"function\\")throw b({key:g,expected:\\"`function`\\",actual:D})}else if(g===\\"replace\\"||g===\\"scroll\\"||g===\\"shallow\\"||g===\\"passHref\\"||g===\\"prefetch\\"||g===\\"legacyBehavior\\"){if(e[g]!=null&&D!==\\"boolean\\")throw b({key:g,expected:\\"`boolean`\\",actual:D})}else{let Te=g}});let ve=I.default.useRef(!1);e.prefetch&&!ve.current&&!M&&(ve.current=!0,console.warn(\\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\\"))}if(M&&!s){let b;if(typeof o==\\"string\\"?b=o:typeof o==\\"object\\"&&typeof o.pathname==\\"string\\"&&(b=o.pathname),b&&b.split(\\"/\\").some(ee=>ee.startsWith(\\"[\\")&&ee.endsWith(\\"]\\")))throw new Error(\\"Dynamic href `\\"+b+\\"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\\")}let{href:L,as:O}=I.default.useMemo(()=>{if(!_){let ee=gr(o);return{href:ee,as:s?gr(s):ee}}let[b,re]=(0,hr.resolveHref)(_,o,!0);return{href:b,as:s?(0,hr.resolveHref)(_,s):re||b}},[_,o,s]),ye=I.default.useRef(L),Ne=I.default.useRef(O),R;if(A){U&&console.warn(\'\\"onClick\\" was passed to <Link> with `href` of `\'+o+\'` but \\"legacyBehavior\\" was set. The legacy behavior requires onClick be set on the child of next/link\'),ae&&console.warn(\'\\"onMouseEnter\\" was passed to <Link> with `href` of `\'+o+\'` but \\"legacyBehavior\\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link\');try{R=I.default.Children.only(i)}catch{throw i?new Error(\\"Multiple children were passed to <Link> with `href` of `\\"+o+\\"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\\"+(typeof window<\\"u\\"?` \\nOpen your browser\'s console to view the Component stack trace.`:\\"\\")):new Error(\\"No children were passed to <Link> with `href` of `\\"+o+\\"` but one child is required https://nextjs.org/docs/messages/link-no-children\\")}}else if(i?.type===\\"a\\")throw new Error(`Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor`);let w=A?R&&typeof R==\\"object\\"&&R.ref:r,[J,ne,fe]=(0,as.useIntersection)({rootMargin:\\"200px\\"}),xe=I.default.useCallback(b=>{(Ne.current!==O||ye.current!==L)&&(fe(),Ne.current=O,ye.current=L),J(b),w&&(typeof w==\\"function\\"?w(b):typeof w==\\"object\\"&&(w.current=b))},[O,w,L,fe,J]);I.default.useEffect(()=>{},[O,L,ne,P,ie,_?.locale,z,M,le]);let oe={ref:xe,onClick(b){if(!b)throw new Error(\'Component rendered inside next/link has to pass click event to \\"onClick\\" prop.\');!A&&typeof U==\\"function\\"&&U(b),A&&R.props&&typeof R.props.onClick==\\"function\\"&&R.props.onClick(b),z&&(b.defaultPrevented||ds(b,z,L,O,p,v,S,P,M,ie))},onMouseEnter(b){!A&&typeof ae==\\"function\\"&&ae(b),A&&R.props&&typeof R.props.onMouseEnter==\\"function\\"&&R.props.onMouseEnter(b),z&&((!ie||!0)&&M||zt(z,L,O,{locale:P,priority:!0,bypassPrefetchedCheck:!0},{kind:le},M))},onTouchStart(b){!A&&typeof ge==\\"function\\"&&ge(b),A&&R.props&&typeof R.props.onTouchStart==\\"function\\"&&R.props.onTouchStart(b),z&&(!ie&&M||zt(z,L,O,{locale:P,priority:!0,bypassPrefetchedCheck:!0},{kind:le},M))}};if((0,rs.isAbsoluteUrl)(O))oe.href=O;else if(!A||u||R.type===\\"a\\"&&!(\\"href\\"in R.props)){let b=typeof P<\\"u\\"?P:_?.locale,re=_?.isLocaleDomain&&(0,ls.getDomainLocale)(O,b,_?.locales,_?.domainLocales);oe.href=re||(0,us.addBasePath)((0,is.addLocale)(O,b,_?.defaultLocale))}return A?I.default.cloneElement(R,oe):I.default.createElement(\\"a\\",{...te,...oe},i)}),ps=fs;(typeof Z.default==\\"function\\"||typeof Z.default==\\"object\\"&&Z.default!==null)&&typeof Z.default.__esModule>\\"u\\"&&(Object.defineProperty(Z.default,\\"__esModule\\",{value:!0}),Object.assign(Z.default,Z),Nr.exports=Z.default)});var Tr=h((Qs,vr)=>{vr.exports=xr()});var gs={};ei(gs,{default:()=>_s,faqData:()=>hs,frontmatter:()=>ms});var a=Jt(rn());function $e({children:t,quizSlug:e}){return React.createElement(\\"article\\",{className:\\"max-w-4xl mx-auto text-gray-900\\"},React.createElement(\\"div\\",{className:\\"prose prose-lg max-w-none text-gray-900 [&>*]:text-gray-900\\"},t))}var Pr=Jt(Tr());function Ie({task:t,href:e}){let r=e||`/quiz/${t}`;return React.createElement(\\"div\\",{className:\\"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\\"},React.createElement(\\"div\\",{className:\\"text-center\\"},React.createElement(\\"h3\\",{className:\\"text-xl font-semibold text-gray-900 mb-2\\"},\\"Not sure which AI fits your workflow?\\"),React.createElement(\\"p\\",{className:\\"text-gray-600 mb-4\\"},\\"Take our 30-second quiz to get a personalized recommendation\\"),React.createElement(Pr.default,{href:r,className:\\"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\\"},\\"Take the \\",t.charAt(0).toUpperCase()+t.slice(1),\\" Quiz \\\\u2192\\")))}var ms={title:\\"Best AI for Coding & Debugging (2025) \\\\u2014 GPT-4o vs Claude 3.5, GitHub Copilot & more\\",description:\\"Developer-focused comparison of GPT-4o, Claude 3.5 Sonnet, GitHub Copilot, and Replit Ghostwriter. Find your perfect AI coding assistant.\\",slug:\\"best-ai-for-coding-2025-gpt-4o-vs-claude-github-copilot-more\\",template:\\"pillar\\",cluster:\\"code\\",priority:\\"High\\",lastUpdated:\\"2025-07-22\\"},hs=[{q:\\"What\'s the cheapest AI coder?\\",a:\\"For free options, GitHub Copilot offers free access for students and open-source contributors. For paid plans, most AI coding assistants are around $20/month, with GitHub Copilot Business at $19/month being slightly cheaper.\\"},{q:\\"Can AI write a full application?\\",a:\\"While AI can generate significant portions of an application, including boilerplate, functions, and UI components, it cannot yet write a complete, production-ready application from a single prompt without human supervision. It excels as a \'co-pilot\' for assistance.\\"},{q:\\"Is GPT-4o good for debugging complex code?\\",a:\\"Yes, GPT-4o is excellent for debugging complex code due to its strong logical reasoning. However, for extremely large codebases, Claude 3.5 Sonnet\'s larger context window may have an advantage for understanding file relationships.\\"},{q:\\"Does GitHub Copilot steal your code?\\",a:\\"No, GitHub Copilot does not \'steal\' your code. For enterprise users, GitHub has a strict policy that private code is not used to train public models. Enterprise licenses include IP indemnification for legal protection.\\"},{q:\\"Which AI is best for Python development?\\",a:\\"All major AI coding assistants handle Python well. GPT-4o excels at algorithmic problems, Claude 3.5 is great for large Python projects, and GitHub Copilot offers the best IDE integration for Python development.\\"},{q:\\"Can AI help with code reviews?\\",a:\\"Yes, AI can assist with code reviews by identifying potential bugs, suggesting improvements, and checking for best practices. GitHub Copilot integrates directly with PR workflows, while Claude 3.5\'s large context window is excellent for reviewing entire files.\\"}];function Er(t){let e=Object.assign({h2:\\"h2\\",p:\\"p\\",hr:\\"hr\\",strong:\\"strong\\",h3:\\"h3\\",pre:\\"pre\\",code:\\"code\\",ul:\\"ul\\",li:\\"li\\",em:\\"em\\"},t.components);return(0,a.jsxDEV)($e,{quizSlug:\\"coding\\",children:[(0,a.jsxDEV)(Ie,{task:\\"coding\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:16,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\\"The AI Coding Landscape in 2025\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:18,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\'The conversation around AI coding assistants has moved far beyond simple autocomplete. In 2025, these tools are powerful collaborators capable of architecting systems, debugging multi-file repositories, and accelerating development cycles. But the fragmented market means the \\"best\\" AI is no longer a simple choice. The right tool depends entirely on your specific needs: low latency to maintain flow state, a massive context window for complex codebases, a deep plug-in ecosystem for your existing workflow, and robust licensing for enterprise security.\'},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:20,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\\"This guide provides a developer-focused comparison of the top contenders\\\\u2014GPT-4o, Claude 3.5 Sonnet, GitHub Copilot, and Replit Ghostwriter\\\\u2014to help you select the right AI co-pilot for your next project.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:22,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:24,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\\"The AI Coder\'s Scorecard: Specs at a Glance\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:26,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\\"For developers, specs matter. This chart breaks down the key models by what you care about most: cost, context, and core strengths.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:28,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[`| Model | Pricing (per user/month) | Context Window | Key Strength / Ecosystem |\\n|-------|--------------------------|----------------|--------------------------|\\n| `,(0,a.jsxDEV)(e.strong,{children:\\"GPT-4o\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:32,columnNumber:3},this),` | ~$20 (API is usage-based) | 128k tokens | Versatility; a powerful \\"second brain\\" for logic and algorithms. |\\n| `,(0,a.jsxDEV)(e.strong,{children:\\"Claude 3.5 Sonnet\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:33,columnNumber:3},this),` | ~$20 (API is usage-based) | 200k tokens | Massive context for codebase analysis and complex refactoring. |\\n| `,(0,a.jsxDEV)(e.strong,{children:\\"GitHub Copilot\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:34,columnNumber:3},this),` | $19 (Business) / $39 (Enterprise) | Varies (uses GPT-4) | Deep integration with GitHub, VS Code, and the PR lifecycle. |\\n| `,(0,a.jsxDEV)(e.strong,{children:\\"Replit Ghostwriter\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:35,columnNumber:3},this),\\" | $20 (Pro) / $50 (Teams) | Varies | Native to the Replit cloud IDE for seamless prototyping. |\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:30,columnNumber:1},this),(0,a.jsxDEV)(\\"div\\",{style:{textAlign:\\"right\\",fontSize:\\"0.9rem\\"},children:(0,a.jsxDEV)(\\"a\\",{href:\\"/export/coding-scorecard.csv\\",children:\\"Export to Sheets \\\\u2192\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:37,columnNumber:52},this)},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:37,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:39,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\\"The Code Challenge: Simple Bugs vs. High-Context Flaws\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:41,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\\"Not all bugs are created equal. Some are simple typos, while others are subtle logical flaws that hide deep within a large codebase. We tested the leading models with two distinct challenges to see where they shine and where they falter.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:43,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:\\"Snippet 1: The Flawless Fix\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:45,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\\"This simple Python function is meant to calculate the total price of items in a cart but has a common off-by-one error.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:47,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.strong,{children:\\"Buggy Code:\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:49,columnNumber:1},this)},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:49,columnNumber:1},this),(0,a.jsxDEV)(e.pre,{children:(0,a.jsxDEV)(e.code,{className:\\"language-python\\",children:`def calculate_cart_total(prices):\\n  total = 0\\n  # Bug: range stops before the last index\\n  for i in range(len(prices) - 1):\\n    total += prices[i]\\n  return total\\n\\ncart = [10, 25, 15, 5]\\nprint(f\\"Total: \\\\${calculate_cart_total(cart)}\\") \\n# Expected output: $55\\n# Actual output: $50\\n`},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:51,columnNumber:1},this)},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:51,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Result:\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:65,columnNumber:1},this),\\" Every model tested\\\\u2014GPT-4o, Claude, Copilot, and Ghostwriter\\\\u2014fixed this instantly. They correctly identified that the loop failed to include the last item and adjusted \\",(0,a.jsxDEV)(e.code,{children:\\"range(len(prices) - 1)\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:65,columnNumber:180},this),\\" to \\",(0,a.jsxDEV)(e.code,{children:\\"range(len(prices))\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:65,columnNumber:208},this),\\". This is the table-stakes capability you should expect from any modern AI code generator.\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:65,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:\\"Snippet 2: The High-Context Challenge\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:67,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[\\"This is where premium models prove their worth. The bug here is subtle. A utility function \\",(0,a.jsxDEV)(e.code,{children:\\"process_data\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:69,columnNumber:92},this),\\" incorrectly uses a global \\",(0,a.jsxDEV)(e.code,{children:\\"TRANSACTION_FEE\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:69,columnNumber:133},this),\\" variable, but this is only apparent when you see how \\",(0,a.jsxDEV)(e.code,{children:\\"process_data\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:69,columnNumber:204},this),\\" is called by another function that has already applied a separate, regional tax. Only an AI that can hold the entire call stack in its context can spot the double charge.\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:69,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.strong,{children:\\"Buggy Code (in a large file):\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:71,columnNumber:1},this)},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:71,columnNumber:1},this),(0,a.jsxDEV)(e.pre,{children:(0,a.jsxDEV)(e.code,{className:\\"language-javascript\\",children:`// Defined 500 lines earlier...\\nconst TRANSACTION_FEE = 0.02; // 2% processing fee\\n\\nfunction process_data(items) {\\n  let subtotal = items.reduce((acc, item) => acc + item.price, 0);\\n  // Bug: This fee is applied redundantly, as the calling function handles taxes.\\n  return subtotal * (1 + TRANSACTION_FEE); \\n}\\n\\n// ... much later in the file ...\\n\\nfunction checkout_for_region(cart, region_config) {\\n  let regional_total = cart.reduce((acc, item) => acc + item.price, 0);\\n  \\n  // Apply regional tax correctly\\n  regional_total *= (1 + region_config.tax_rate);\\n\\n  // Send to processing, unaware that it adds another fee\\n  const final_price = process_data(cart); // Should pass regional_total\\n  \\n  console.log(\\\\`Final price is: \\\\${final_price.toFixed(2)}\\\\`);\\n}\\n`},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:73,columnNumber:1},this)},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:73,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.strong,{children:\\"Result:\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:98,columnNumber:1},this)},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:98,columnNumber:1},this),(0,a.jsxDEV)(e.ul,{children:[`\\n`,(0,a.jsxDEV)(e.li,{children:[`\\n`,(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Lower-Context Models\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:100,columnNumber:3},this),\\" typically suggest fixing \\",(0,a.jsxDEV)(e.code,{children:\\"process_data\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:100,columnNumber:53},this),\\" in isolation, perhaps by adding a parameter to toggle the fee. They miss the reason it\'s wrong\\\\u2014the redundant call inside \\",(0,a.jsxDEV)(e.code,{children:\\"checkout_for_region\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:100,columnNumber:189},this),\\".\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:100,columnNumber:3},this),`\\n`]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:100,columnNumber:1},this),`\\n`,(0,a.jsxDEV)(e.li,{children:[`\\n`,(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\\"High-Context Models\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:102,columnNumber:3},this),\\" (Claude 3.5 Sonnet & GPT-4o) excelled. They identified the core issue: \\",(0,a.jsxDEV)(e.code,{children:\\"checkout_for_region\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:102,columnNumber:98},this),\\" performs its own calculation and then calls \\",(0,a.jsxDEV)(e.code,{children:\\"process_data\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:102,columnNumber:164},this),\\" with the original cart, causing a redundant calculation and an extra fee. Claude, in particular, suggested refactoring \\",(0,a.jsxDEV)(e.code,{children:\\"checkout_for_region\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:102,columnNumber:298},this),\\" to pass the \\",(0,a.jsxDEV)(e.code,{children:\\"regional_total\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:102,columnNumber:332},this),\\" into \\",(0,a.jsxDEV)(e.code,{children:\\"process_data\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:102,columnNumber:354},this),\\" and removing the fee logic from \\",(0,a.jsxDEV)(e.code,{children:\\"process_data\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:102,columnNumber:401},this),\\" entirely, demonstrating a deep understanding of the entire file\'s logic.\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:102,columnNumber:3},this),`\\n`]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:102,columnNumber:1},this),`\\n`]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:100,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:104,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\\"The Enterprise Developer\'s Checklist\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:106,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\\"For teams, choosing an AI coding assistant involves more than just performance\\\\u2014it\'s about security, licensing, and integration. Before committing, run your choice through this permissions checklist.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:108,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[\\"\\\\u2610 \\",(0,a.jsxDEV)(e.strong,{children:\\"Data Privacy & Training\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:110,columnNumber:3},this),\\": Does the provider offer a zero-retention policy, guaranteeing your proprietary code is never used for training their models? (Look for Enterprise or Business tiers).\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:110,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[\\"\\\\u2610 \\",(0,a.jsxDEV)(e.strong,{children:\\"Licensing & Indemnification\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:112,columnNumber:3},this),\\": Are the terms clear about the ownership of AI-generated code? Does the provider (like GitHub Copilot) offer intellectual property indemnification to protect your company from potential lawsuits?\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:112,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[\\"\\\\u2610 \\",(0,a.jsxDEV)(e.strong,{children:\\"Seat Management & SSO\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:114,columnNumber:3},this),\\": Can you manage user licenses from a central dashboard and integrate with your existing Single Sign-On (SSO) solution for secure access?\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:114,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[\\"\\\\u2610 \\",(0,a.jsxDEV)(e.strong,{children:\\"Security Compliance\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:116,columnNumber:3},this),\\": Is the tool compliant with industry standards like SOC 2 Type 2? This is non-negotiable for most enterprise environments.\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:116,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[\\"\\\\u2610 \\",(0,a.jsxDEV)(e.strong,{children:\\"IDE & Toolchain Integration\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:118,columnNumber:3},this),\\": Does it offer first-party extensions for your team\'s preferred IDEs (VS Code, JetBrains) and version control systems? Seamless integration is key to adoption.\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:118,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:120,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\\"Deep-dive profiles\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:122,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\\"GPT-4o \\\\u2014 \\",(0,a.jsxDEV)(e.em,{children:\\"the versatile problem-solver\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:124,columnNumber:14},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:124,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Strengths.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:126,columnNumber:1},this),` Excellent logical reasoning; handles multiple programming languages; strong algorithmic thinking.\\n`,(0,a.jsxDEV)(e.strong,{children:\\"Weaknesses.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:127,columnNumber:1},this),` Smaller context window than Claude; can be verbose in explanations.\\n`,(0,a.jsxDEV)(e.em,{children:\\"Perfect for: General development, algorithm design, multi-language projects.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:128,columnNumber:1},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:126,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\\"Claude 3.5 Sonnet \\\\u2014 \\",(0,a.jsxDEV)(e.em,{children:\\"the codebase analyst\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:130,columnNumber:25},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:130,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Strengths.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:132,columnNumber:1},this),` Massive 200k token context; excellent at understanding large file relationships; thoughtful refactoring suggestions.\\n`,(0,a.jsxDEV)(e.strong,{children:\\"Weaknesses.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:133,columnNumber:1},this),` No native IDE integration yet; API-only access.\\n`,(0,a.jsxDEV)(e.em,{children:\\"Perfect for: Large codebase analysis, complex refactoring, architectural decisions.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:134,columnNumber:1},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:132,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\\"GitHub Copilot \\\\u2014 \\",(0,a.jsxDEV)(e.em,{children:\\"the workflow integrator\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:136,columnNumber:22},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:136,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Strengths.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:138,columnNumber:1},this),` Seamless VS Code integration; understands Git context; PR and issue integration.\\n`,(0,a.jsxDEV)(e.strong,{children:\\"Weaknesses.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:139,columnNumber:1},this),` Limited to GitHub ecosystem; enterprise pricing can be steep.\\n`,(0,a.jsxDEV)(e.em,{children:\\"Perfect for: GitHub-based teams, VS Code users, integrated development workflows.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:140,columnNumber:1},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:138,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\\"Replit Ghostwriter \\\\u2014 \\",(0,a.jsxDEV)(e.em,{children:\\"the rapid prototyper\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:142,columnNumber:26},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:142,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Strengths.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:144,columnNumber:1},this),` Instant deployment; browser-based development; great for learning and experimentation.\\n`,(0,a.jsxDEV)(e.strong,{children:\\"Weaknesses.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:145,columnNumber:1},this),` Limited to Replit environment; less suitable for complex enterprise projects.\\n`,(0,a.jsxDEV)(e.em,{children:\\"Perfect for: Rapid prototyping, educational projects, web-based development.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:146,columnNumber:1},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:144,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:148,columnNumber:1},this),(0,a.jsxDEV)(Ie,{task:\\"coding\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:150,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:152,columnNumber:1},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\",lineNumber:14,columnNumber:1},this)}function bs(t={}){let{wrapper:e}=t.components||{};return e?(0,a.jsxDEV)(e,Object.assign({},t,{children:(0,a.jsxDEV)(Er,t,void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\"},this)}),void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-61068231-e3de-413a-82c6-d63627e68ab3.mdx\\"},this):Er(t)}var _s=bs;return ti(gs);})();\\n/*! Bundled license information:\\n\\nreact/cjs/react-jsx-dev-runtime.development.js:\\n  (**\\n   * @license React\\n   * react-jsx-dev-runtime.development.js\\n   *\\n   * Copyright (c) Facebook, Inc. and its affiliates.\\n   *\\n   * This source code is licensed under the MIT license found in the\\n   * LICENSE file in the root directory of this source tree.\\n   *)\\n*/\\n;return Component;"},"_id":"pillars/best-ai-for-coding.mdx","_raw":{"sourceFilePath":"pillars/best-ai-for-coding.mdx","sourceFileName":"best-ai-for-coding.mdx","sourceFileDir":"pillars","contentType":"mdx","flattenedPath":"pillars/best-ai-for-coding"},"type":"Pillar","url":"/best-ai-for-coding-2025-gpt-4o-vs-claude-github-copilot-more","slugFromPath":"pillars/best-ai-for-coding"}');

/***/ }),

/***/ "(rsc)/./.contentlayer/generated/Pillar/pillars__best-ai-for-writing.mdx.json":
/*!******************************************************************************!*\
  !*** ./.contentlayer/generated/Pillar/pillars__best-ai-for-writing.mdx.json ***!
  \******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"title":"Best AI for Writing (2025) — Claude 3.5 vs GPT-4o, Gemini & more","description":"Comprehensive comparison of Claude 3.5, GPT-4o, Gemini, Perplexity & Grok for bloggers, students, and marketers. Find your perfect AI writing assistant.","slug":"best-ai-for-writing-2025-claude-3-5-vs-gpt-4o-gemini-more","cluster":"text","template":"pillar","priority":"Low","lastUpdated":"2025-07-22","body":{"raw":"\\nimport PillarLayout from \\"@/templates/PillarLayout\\"\\nimport QuizCta from \\"@/components/QuizCta\\"\\n\\n<PillarLayout quizSlug=\\"writing\\">\\n\\n<QuizCta task=\\"writing\\" />\\n\\n## Who are you writing for ?\\n\\n**The Blogger**\\n\\n- **Pain** – needs original long-form content that won\'t feel robotic or earn an SEO penalty.\\n- **Ideal output** – an AI blog generator that keeps a consistent tone.\\n- **Killer feature** – a huge context window to track details across thousands of words.\\n\\n**The Student**\\n\\n- **Pain** – must research, structure, and cite accurately while avoiding plagiarism.\\n- **Ideal output** – an AI essay writer that returns verifiable facts with citations.\\n- **Killer feature** – can ingest PDFs and analyse them directly.\\n\\n**The Marketer**\\n\\n- **Pain** – high-volume, mixed-format content plus brand-voice consistency.\\n- **Ideal output** – a tool that plugs into Google Workspace and accelerates campaigns.\\n- **Killer feature** – analyses spreadsheet data and builds project plans.\\n\\n---\\n\\n## A market of specialists, not one \\"best\\" model\\n\\nPerplexity is an **answer engine**, Claude a **creative prose specialist**, and Gemini a **productivity layer** for Docs, Sheets, and Gmail. The takeaway: _choose by task_, not by raw IQ.\\n\\n> ### ⚠ Premium trap\\n> The $20 Pro tiers are no longer the cutting edge. OpenAI, Anthropic, Perplexity, and xAI now sell $100–$300 \\"Max / Heavy\\" plans aimed at enterprises. For most writers the $20 tier remains the ROI sweet-spot, but know you\'re not buying the absolute top model.\\n\\n---\\n\\n## 2025 AI-writer scorecard\\n\\n| Model | Best for (archetype) | Stand-out feature | Context window | Free tier | Pro price | Key limitation |\\n|-------|----------------------|-------------------|---------------|-----------|-----------|----------------|\\n| **Claude 3.5 Sonnet** | Creative writing (Poet) | \\"Artifacts\\" live editor | 200 k tokens | Yes (daily cap) | $20 | No native real-time web search |\\n| **GPT-4o** | Generalist (Polymath) | Multimodal + Custom GPTs | 128 k tokens | Yes (cap) | $20 | Output can feel robotic |\\n| **Gemini Advanced** | Productivity (Producer) | Deep Workspace integration | 1 M + tokens | Yes (std) | $19.99 | Creative flair weaker than Claude |\\n| **Perplexity Pro** | Research (Professor) | Clickable citations, Deep Research | — | Yes (cap) | $20 | Not a creative writer |\\n| **Grok** | Real-time insights (Provocateur) | Live X / Twitter data | — | Yes (cap) | $30 | Pricey; edgy tone not for all |\\n\\n<div style={{textAlign:\'right\',fontSize:\'0.9rem\'}}><a href=\\"/export/scorecard.csv\\">Export to Sheets →</a></div>\\n\\n---\\n\\n## Speed test ⚡\\n\\n*[Speed comparison GIF placeholder - GPT-4o vs Claude 3.5 vs Gemini]*\\n\\nGPT-4o starts generating sooner and streams tokens faster, a win for brainstorms and quick Q & A. Claude is ~2× faster than its predecessor Opus but still trails GPT-4o on sheer responsiveness.\\n\\n---\\n\\n## Deep-dive profiles\\n\\n### Claude 3.5 Sonnet — _the creative wordsmith_\\n\\n**Strengths.** Thoughtful, expressive prose; 200 k-token context; \\"Artifacts\\" side-panel for iterative editing.\\n**Weaknesses.** No built-in web browsing; free tier message cap.\\n_Read the full [Claude 3.5 blogging review](/claude-3-5-for-blogging-review)._\\n\\n---\\n\\n### GPT-4o — _the versatile all-rounder_\\n\\nHandles writing, code, data and images in one chat. Custom GPTs unlock niche workflows.\\nCriticisms: increasing verbosity, smaller window than Claude, privacy concerns.\\n\\n---\\n\\n### Gemini Advanced — _the integrated productivity engine_\\n\\nNative in Docs, Sheets, Gmail. Perfect for campaign tables, email summarising, Drive file look-ups.\\nDeep dive: [Gemini for marketers](/gemini-advanced-for-marketers-guide).\\n\\n---\\n\\n### Perplexity Pro — _the research powerhouse_\\n\\nDelivers answers with numbered citations; \\"Deep Research\\" builds exhaustive reports.\\nGuide: [How to use Perplexity for academic research](/how-to-use-perplexity-for-academic-research).\\n\\n---\\n\\n### Grok — _the real-time provocateur_\\n\\nLive social-media pulse plus a snarky attitude. Great for trend analysts, overkill for everyday writing.\\n\\n---\\n\\n<QuizCta task=\\"writing\\" />\\n\\n---\\n\\nexport const faqData = [\\n  { q: \\"What is the best free AI for writing?\\", a: \\"Perplexity offers the strongest free tier for fact-based writing, while Claude\'s free tier is best for creative prose.\\" },\\n  { q: \\"Can Google penalise AI-generated content?\\", a: \\"Google ranks helpful content regardless of how it\'s produced; thin or spammy AI text can be penalised.\\" },\\n  { q: \\"What\'s a context window and why does it matter?\\", a: \\"It\'s the amount of text an AI can \'remember\'. Bigger windows (e.g., Claude\'s 200 k tokens) keep long documents coherent.\\" },\\n  { q: \\"Which AI is best for creative writing?\\", a: \\"Claude 3.5 Sonnet consistently produces the most human-like, nuanced prose.\\" },\\n  { q: \\"Which AI provides reliable citations?\\", a: \\"Perplexity Pro surfaces sources and clickable references by default.\\" },\\n  { q: \\"Is GPT-4o still king in 2025?\\", a: \\"It\'s the best all-rounder, but Claude wins on style and Perplexity on accuracy.\\" }\\n]\\n\\n</PillarLayout>\\n","code":"var Component=(()=>{var Bn=Object.create;var we=Object.defineProperty;var Xn=Object.getOwnPropertyDescriptor;var Qn=Object.getOwnPropertyNames;var Zn=Object.getPrototypeOf,Jn=Object.prototype.hasOwnProperty;var h=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),ei=(t,e)=>{for(var n in e)we(t,n,{get:e[n],enumerable:!0})},Zt=(t,e,n,i)=>{if(e&&typeof e==\\"object\\"||typeof e==\\"function\\")for(let o of Qn(e))!Jn.call(t,o)&&o!==n&&we(t,o,{get:()=>e[o],enumerable:!(i=Xn(e,o))||i.enumerable});return t};var Jt=(t,e,n)=>(n=t!=null?Bn(Zn(t)):{},Zt(e||!t||!t.__esModule?we(n,\\"default\\",{value:t,enumerable:!0}):n,t)),ti=t=>Zt(we({},\\"__esModule\\",{value:!0}),t);var de=h((Ns,er)=>{er.exports=React});var tr=h($e=>{\\"use strict\\";(function(){\\"use strict\\";var t=de(),e=Symbol.for(\\"react.element\\"),n=Symbol.for(\\"react.portal\\"),i=Symbol.for(\\"react.fragment\\"),o=Symbol.for(\\"react.strict_mode\\"),s=Symbol.for(\\"react.profiler\\"),f=Symbol.for(\\"react.provider\\"),d=Symbol.for(\\"react.context\\"),u=Symbol.for(\\"react.forward_ref\\"),p=Symbol.for(\\"react.suspense\\"),x=Symbol.for(\\"react.suspense_list\\"),S=Symbol.for(\\"react.memo\\"),T=Symbol.for(\\"react.lazy\\"),U=Symbol.for(\\"react.offscreen\\"),le=Symbol.iterator,ge=\\"@@iterator\\";function I(r){if(r===null||typeof r!=\\"object\\")return null;var l=le&&r[le]||r[ge];return typeof l==\\"function\\"?l:null}var te=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function _(r){{for(var l=arguments.length,c=new Array(l>1?l-1:0),m=1;m<l;m++)c[m-1]=arguments[m];Ie(\\"error\\",r,c)}}function Ie(r,l,c){{var m=te.ReactDebugCurrentFrame,v=m.getStackAddendum();v!==\\"\\"&&(l+=\\"%s\\",c=c.concat([v]));var P=c.map(function(N){return String(N)});P.unshift(\\"Warning: \\"+l),Function.prototype.apply.call(console[r],console,P)}}var z=!1,M=!1,ie=!1,ae=!1,L=!1,O;O=Symbol.for(\\"react.module.reference\\");function ye(r){return!!(typeof r==\\"string\\"||typeof r==\\"function\\"||r===i||r===s||L||r===o||r===p||r===x||ae||r===U||z||M||ie||typeof r==\\"object\\"&&r!==null&&(r.$$typeof===T||r.$$typeof===S||r.$$typeof===f||r.$$typeof===d||r.$$typeof===u||r.$$typeof===O||r.getModuleId!==void 0))}function Ne(r,l,c){var m=r.displayName;if(m)return m;var v=l.displayName||l.name||\\"\\";return v!==\\"\\"?c+\\"(\\"+v+\\")\\":c}function E(r){return r.displayName||\\"Context\\"}function w(r){if(r==null)return null;if(typeof r.tag==\\"number\\"&&_(\\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\\"),typeof r==\\"function\\")return r.displayName||r.name||null;if(typeof r==\\"string\\")return r;switch(r){case i:return\\"Fragment\\";case n:return\\"Portal\\";case s:return\\"Profiler\\";case o:return\\"StrictMode\\";case p:return\\"Suspense\\";case x:return\\"SuspenseList\\"}if(typeof r==\\"object\\")switch(r.$$typeof){case d:var l=r;return E(l)+\\".Consumer\\";case f:var c=r;return E(c._context)+\\".Provider\\";case u:return Ne(r,r.render,\\"ForwardRef\\");case S:var m=r.displayName||null;return m!==null?m:w(r.type)||\\"Memo\\";case T:{var v=r,P=v._payload,N=v._init;try{return w(N(P))}catch{return null}}}return null}var J=Object.assign,re=0,fe,ve,oe,b,ne,ee,Ue;function We(){}We.__reactDisabledLog=!0;function xe(){{if(re===0){fe=console.log,ve=console.info,oe=console.warn,b=console.error,ne=console.group,ee=console.groupCollapsed,Ue=console.groupEnd;var r={configurable:!0,enumerable:!0,value:We,writable:!0};Object.defineProperties(console,{info:r,log:r,warn:r,error:r,group:r,groupCollapsed:r,groupEnd:r})}re++}}function g(){{if(re--,re===0){var r={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:J({},r,{value:fe}),info:J({},r,{value:ve}),warn:J({},r,{value:oe}),error:J({},r,{value:b}),group:J({},r,{value:ne}),groupCollapsed:J({},r,{value:ee}),groupEnd:J({},r,{value:Ue})})}re<0&&_(\\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\\")}}var D=te.ReactCurrentDispatcher,Pe;function Te(r,l,c){{if(Pe===void 0)try{throw Error()}catch(v){var m=v.stack.trim().match(/\\\\n( *(at )?)/);Pe=m&&m[1]||\\"\\"}return`\\n`+Pe+r}}var ze=!1,Re;{var kn=typeof WeakMap==\\"function\\"?WeakMap:Map;Re=new kn}function Mt(r,l){if(!r||ze)return\\"\\";{var c=Re.get(r);if(c!==void 0)return c}var m;ze=!0;var v=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var P;P=D.current,D.current=null,xe();try{if(l){var N=function(){throw Error()};if(Object.defineProperty(N.prototype,\\"props\\",{set:function(){throw Error()}}),typeof Reflect==\\"object\\"&&Reflect.construct){try{Reflect.construct(N,[])}catch(j){m=j}Reflect.construct(r,[],N)}else{try{N.call()}catch(j){m=j}r.call(N.prototype)}}else{try{throw Error()}catch(j){m=j}r()}}catch(j){if(j&&m&&typeof j.stack==\\"string\\"){for(var y=j.stack.split(`\\n`),C=m.stack.split(`\\n`),R=y.length-1,k=C.length-1;R>=1&&k>=0&&y[R]!==C[k];)k--;for(;R>=1&&k>=0;R--,k--)if(y[R]!==C[k]){if(R!==1||k!==1)do if(R--,k--,k<0||y[R]!==C[k]){var W=`\\n`+y[R].replace(\\" at new \\",\\" at \\");return r.displayName&&W.includes(\\"<anonymous>\\")&&(W=W.replace(\\"<anonymous>\\",r.displayName)),typeof r==\\"function\\"&&Re.set(r,W),W}while(R>=1&&k>=0);break}}}finally{ze=!1,D.current=P,g(),Error.prepareStackTrace=v}var ce=r?r.displayName||r.name:\\"\\",se=ce?Te(ce):\\"\\";return typeof r==\\"function\\"&&Re.set(r,se),se}function En(r,l,c){return Mt(r,!1)}function wn(r){var l=r.prototype;return!!(l&&l.isReactComponent)}function ke(r,l,c){if(r==null)return\\"\\";if(typeof r==\\"function\\")return Mt(r,wn(r));if(typeof r==\\"string\\")return Te(r);switch(r){case p:return Te(\\"Suspense\\");case x:return Te(\\"SuspenseList\\")}if(typeof r==\\"object\\")switch(r.$$typeof){case u:return En(r.render);case S:return ke(r.type,l,c);case T:{var m=r,v=m._payload,P=m._init;try{return ke(P(v),l,c)}catch{}}}return\\"\\"}var pe=Object.prototype.hasOwnProperty,Lt={},qt=te.ReactDebugCurrentFrame;function Ee(r){if(r){var l=r._owner,c=ke(r.type,r._source,l?l.type:null);qt.setExtraStackFrame(c)}else qt.setExtraStackFrame(null)}function On(r,l,c,m,v){{var P=Function.call.bind(pe);for(var N in r)if(P(r,N)){var y=void 0;try{if(typeof r[N]!=\\"function\\"){var C=Error((m||\\"React class\\")+\\": \\"+c+\\" type `\\"+N+\\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\\"+typeof r[N]+\\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\\");throw C.name=\\"Invariant Violation\\",C}y=r[N](l,N,m,c,null,\\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\\")}catch(R){y=R}y&&!(y instanceof Error)&&(Ee(v),_(\\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\\",m||\\"React class\\",c,N,typeof y),Ee(null)),y instanceof Error&&!(y.message in Lt)&&(Lt[y.message]=!0,Ee(v),_(\\"Failed %s type: %s\\",c,y.message),Ee(null))}}}var Cn=Array.isArray;function Me(r){return Cn(r)}function Sn(r){{var l=typeof Symbol==\\"function\\"&&Symbol.toStringTag,c=l&&r[Symbol.toStringTag]||r.constructor.name||\\"Object\\";return c}}function Dn(r){try{return Ft(r),!1}catch{return!0}}function Ft(r){return\\"\\"+r}function Gt(r){if(Dn(r))return _(\\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\\",Sn(r)),Ft(r)}var me=te.ReactCurrentOwner,jn={key:!0,ref:!0,__self:!0,__source:!0},Vt,$t,Le;Le={};function An(r){if(pe.call(r,\\"ref\\")){var l=Object.getOwnPropertyDescriptor(r,\\"ref\\").get;if(l&&l.isReactWarning)return!1}return r.ref!==void 0}function In(r){if(pe.call(r,\\"key\\")){var l=Object.getOwnPropertyDescriptor(r,\\"key\\").get;if(l&&l.isReactWarning)return!1}return r.key!==void 0}function Un(r,l){if(typeof r.ref==\\"string\\"&&me.current&&l&&me.current.stateNode!==l){var c=w(me.current.type);Le[c]||(_(\'Component \\"%s\\" contains the string ref \\"%s\\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref\',w(me.current.type),r.ref),Le[c]=!0)}}function Wn(r,l){{var c=function(){Vt||(Vt=!0,_(\\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\\",l))};c.isReactWarning=!0,Object.defineProperty(r,\\"key\\",{get:c,configurable:!0})}}function zn(r,l){{var c=function(){$t||($t=!0,_(\\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\\",l))};c.isReactWarning=!0,Object.defineProperty(r,\\"ref\\",{get:c,configurable:!0})}}var Mn=function(r,l,c,m,v,P,N){var y={$$typeof:e,type:r,key:l,ref:c,props:N,_owner:P};return y._store={},Object.defineProperty(y._store,\\"validated\\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(y,\\"_self\\",{configurable:!1,enumerable:!1,writable:!1,value:m}),Object.defineProperty(y,\\"_source\\",{configurable:!1,enumerable:!1,writable:!1,value:v}),Object.freeze&&(Object.freeze(y.props),Object.freeze(y)),y};function Ln(r,l,c,m,v){{var P,N={},y=null,C=null;c!==void 0&&(Gt(c),y=\\"\\"+c),In(l)&&(Gt(l.key),y=\\"\\"+l.key),An(l)&&(C=l.ref,Un(l,v));for(P in l)pe.call(l,P)&&!jn.hasOwnProperty(P)&&(N[P]=l[P]);if(r&&r.defaultProps){var R=r.defaultProps;for(P in R)N[P]===void 0&&(N[P]=R[P])}if(y||C){var k=typeof r==\\"function\\"?r.displayName||r.name||\\"Unknown\\":r;y&&Wn(N,k),C&&zn(N,k)}return Mn(r,y,C,v,m,me.current,N)}}var qe=te.ReactCurrentOwner,Ht=te.ReactDebugCurrentFrame;function ue(r){if(r){var l=r._owner,c=ke(r.type,r._source,l?l.type:null);Ht.setExtraStackFrame(c)}else Ht.setExtraStackFrame(null)}var Fe;Fe=!1;function Ge(r){return typeof r==\\"object\\"&&r!==null&&r.$$typeof===e}function Kt(){{if(qe.current){var r=w(qe.current.type);if(r)return`\\n\\nCheck the render method of \\\\``+r+\\"`.\\"}return\\"\\"}}function qn(r){{if(r!==void 0){var l=r.fileName.replace(/^.*[\\\\\\\\\\\\/]/,\\"\\"),c=r.lineNumber;return`\\n\\nCheck your code at `+l+\\":\\"+c+\\".\\"}return\\"\\"}}var Yt={};function Fn(r){{var l=Kt();if(!l){var c=typeof r==\\"string\\"?r:r.displayName||r.name;c&&(l=`\\n\\nCheck the top-level render call using <`+c+\\">.\\")}return l}}function Bt(r,l){{if(!r._store||r._store.validated||r.key!=null)return;r._store.validated=!0;var c=Fn(l);if(Yt[c])return;Yt[c]=!0;var m=\\"\\";r&&r._owner&&r._owner!==qe.current&&(m=\\" It was passed a child from \\"+w(r._owner.type)+\\".\\"),ue(r),_(\'Each child in a list should have a unique \\"key\\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.\',c,m),ue(null)}}function Xt(r,l){{if(typeof r!=\\"object\\")return;if(Me(r))for(var c=0;c<r.length;c++){var m=r[c];Ge(m)&&Bt(m,l)}else if(Ge(r))r._store&&(r._store.validated=!0);else if(r){var v=I(r);if(typeof v==\\"function\\"&&v!==r.entries)for(var P=v.call(r),N;!(N=P.next()).done;)Ge(N.value)&&Bt(N.value,l)}}}function Gn(r){{var l=r.type;if(l==null||typeof l==\\"string\\")return;var c;if(typeof l==\\"function\\")c=l.propTypes;else if(typeof l==\\"object\\"&&(l.$$typeof===u||l.$$typeof===S))c=l.propTypes;else return;if(c){var m=w(l);On(c,r.props,\\"prop\\",m,r)}else if(l.PropTypes!==void 0&&!Fe){Fe=!0;var v=w(l);_(\\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\\",v||\\"Unknown\\")}typeof l.getDefaultProps==\\"function\\"&&!l.getDefaultProps.isReactClassApproved&&_(\\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\\")}}function Vn(r){{for(var l=Object.keys(r.props),c=0;c<l.length;c++){var m=l[c];if(m!==\\"children\\"&&m!==\\"key\\"){ue(r),_(\\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\\",m),ue(null);break}}r.ref!==null&&(ue(r),_(\\"Invalid attribute `ref` supplied to `React.Fragment`.\\"),ue(null))}}var Qt={};function $n(r,l,c,m,v,P){{var N=ye(r);if(!N){var y=\\"\\";(r===void 0||typeof r==\\"object\\"&&r!==null&&Object.keys(r).length===0)&&(y+=\\" You likely forgot to export your component from the file it\'s defined in, or you might have mixed up default and named imports.\\");var C=qn(v);C?y+=C:y+=Kt();var R;r===null?R=\\"null\\":Me(r)?R=\\"array\\":r!==void 0&&r.$$typeof===e?(R=\\"<\\"+(w(r.type)||\\"Unknown\\")+\\" />\\",y=\\" Did you accidentally export a JSX literal instead of a component?\\"):R=typeof r,_(\\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\\",R,y)}var k=Ln(r,l,c,v,P);if(k==null)return k;if(N){var W=l.children;if(W!==void 0)if(m)if(Me(W)){for(var ce=0;ce<W.length;ce++)Xt(W[ce],r);Object.freeze&&Object.freeze(W)}else _(\\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\\");else Xt(W,r)}if(pe.call(l,\\"key\\")){var se=w(r),j=Object.keys(l).filter(function(Yn){return Yn!==\\"key\\"}),Ve=j.length>0?\\"{key: someKey, \\"+j.join(\\": ..., \\")+\\": ...}\\":\\"{key: someKey}\\";if(!Qt[se+Ve]){var Kn=j.length>0?\\"{\\"+j.join(\\": ..., \\")+\\": ...}\\":\\"{}\\";_(`A props object containing a \\"key\\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />`,Ve,se,Kn,se),Qt[se+Ve]=!0}}return r===i?Vn(k):Gn(k),k}}var Hn=$n;$e.Fragment=i,$e.jsxDEV=Hn})()});var nr=h((xs,rr)=>{\\"use strict\\";rr.exports=tr()});var Oe=h(Ke=>{\\"use strict\\";Ke._=Ke._interop_require_default=ri;function ri(t){return t&&t.__esModule?t:{default:t}}});var Be=h(Ye=>{\\"use strict\\";Object.defineProperty(Ye,\\"__esModule\\",{value:!0});function ni(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}ni(Ye,{searchParamsToUrlQuery:function(){return ii},urlQueryToSearchParams:function(){return oi},assign:function(){return si}});function ii(t){let e={};return t.forEach((n,i)=>{typeof e[i]>\\"u\\"?e[i]=n:Array.isArray(e[i])?e[i].push(n):e[i]=[e[i],n]}),e}function ir(t){return typeof t==\\"string\\"||typeof t==\\"number\\"&&!isNaN(t)||typeof t==\\"boolean\\"?String(t):\\"\\"}function oi(t){let e=new URLSearchParams;return Object.entries(t).forEach(n=>{let[i,o]=n;Array.isArray(o)?o.forEach(s=>e.append(i,ir(s))):e.set(i,ir(o))}),e}function si(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return n.forEach(o=>{Array.from(o.keys()).forEach(s=>t.delete(s)),o.forEach((s,f)=>t.append(f,s))}),t}});var sr=h(Xe=>{\\"use strict\\";function or(t){if(typeof WeakMap!=\\"function\\")return null;var e=new WeakMap,n=new WeakMap;return(or=function(i){return i?n:e})(t)}Xe._=Xe._interop_require_wildcard=li;function li(t,e){if(!e&&t&&t.__esModule)return t;if(t===null||typeof t!=\\"object\\"&&typeof t!=\\"function\\")return{default:t};var n=or(e);if(n&&n.has(t))return n.get(t);var i={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if(s!==\\"default\\"&&Object.prototype.hasOwnProperty.call(t,s)){var f=o?Object.getOwnPropertyDescriptor(t,s):null;f&&(f.get||f.set)?Object.defineProperty(i,s,f):i[s]=t[s]}return i.default=t,n&&n.set(t,i),i}});var Ze=h(Qe=>{\\"use strict\\";Object.defineProperty(Qe,\\"__esModule\\",{value:!0});function ai(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}ai(Qe,{formatUrl:function(){return lr},urlObjectKeys:function(){return ar},formatWithValidation:function(){return fi}});var ui=sr(),ci=ui._(Be()),di=/https?|ftp|gopher|file/;function lr(t){let{auth:e,hostname:n}=t,i=t.protocol||\\"\\",o=t.pathname||\\"\\",s=t.hash||\\"\\",f=t.query||\\"\\",d=!1;e=e?encodeURIComponent(e).replace(/%3A/i,\\":\\")+\\"@\\":\\"\\",t.host?d=e+t.host:n&&(d=e+(~n.indexOf(\\":\\")?\\"[\\"+n+\\"]\\":n),t.port&&(d+=\\":\\"+t.port)),f&&typeof f==\\"object\\"&&(f=String(ci.urlQueryToSearchParams(f)));let u=t.search||f&&\\"?\\"+f||\\"\\";return i&&!i.endsWith(\\":\\")&&(i+=\\":\\"),t.slashes||(!i||di.test(i))&&d!==!1?(d=\\"//\\"+(d||\\"\\"),o&&o[0]!==\\"/\\"&&(o=\\"/\\"+o)):d||(d=\\"\\"),s&&s[0]!==\\"#\\"&&(s=\\"#\\"+s),u&&u[0]!==\\"?\\"&&(u=\\"?\\"+u),o=o.replace(/[?#]/g,encodeURIComponent),u=u.replace(\\"#\\",\\"%23\\"),\\"\\"+i+d+o+u+s}var ar=[\\"auth\\",\\"hash\\",\\"host\\",\\"hostname\\",\\"href\\",\\"path\\",\\"pathname\\",\\"port\\",\\"protocol\\",\\"query\\",\\"search\\",\\"slashes\\"];function fi(t){return t!==null&&typeof t==\\"object\\"&&Object.keys(t).forEach(e=>{ar.includes(e)||console.warn(\\"Unknown key passed via urlObject into url.format: \\"+e)}),lr(t)}});var ur=h(Je=>{\\"use strict\\";Object.defineProperty(Je,\\"__esModule\\",{value:!0});Object.defineProperty(Je,\\"omit\\",{enumerable:!0,get:function(){return pi}});function pi(t,e){let n={};return Object.keys(t).forEach(i=>{e.includes(i)||(n[i]=t[i])}),n}});var he=h(ot=>{\\"use strict\\";Object.defineProperty(ot,\\"__esModule\\",{value:!0});function mi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}mi(ot,{WEB_VITALS:function(){return hi},execOnce:function(){return bi},isAbsoluteUrl:function(){return gi},getLocationOrigin:function(){return cr},getURL:function(){return yi},getDisplayName:function(){return Ce},isResSent:function(){return dr},normalizeRepeatedSlashes:function(){return Ni},loadGetInitialProps:function(){return fr},SP:function(){return pr},ST:function(){return vi},DecodeError:function(){return et},NormalizeError:function(){return tt},PageNotFoundError:function(){return rt},MissingStaticPage:function(){return nt},MiddlewareNotFoundError:function(){return it},stringifyError:function(){return xi}});var hi=[\\"CLS\\",\\"FCP\\",\\"FID\\",\\"INP\\",\\"LCP\\",\\"TTFB\\"];function bi(t){let e=!1,n;return function(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return e||(e=!0,n=t(...o)),n}}var _i=/^[a-zA-Z][a-zA-Z\\\\d+\\\\-.]*?:/,gi=t=>_i.test(t);function cr(){let{protocol:t,hostname:e,port:n}=window.location;return t+\\"//\\"+e+(n?\\":\\"+n:\\"\\")}function yi(){let{href:t}=window.location,e=cr();return t.substring(e.length)}function Ce(t){return typeof t==\\"string\\"?t:t.displayName||t.name||\\"Unknown\\"}function dr(t){return t.finished||t.headersSent}function Ni(t){let e=t.split(\\"?\\");return e[0].replace(/\\\\\\\\/g,\\"/\\").replace(/\\\\/\\\\/+/g,\\"/\\")+(e[1]?\\"?\\"+e.slice(1).join(\\"?\\"):\\"\\")}async function fr(t,e){var n;if((n=t.prototype)!=null&&n.getInitialProps){let s=\'\\"\'+Ce(t)+\'.getInitialProps()\\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.\';throw new Error(s)}let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await fr(e.Component,e.ctx)}:{};let o=await t.getInitialProps(e);if(i&&dr(i))return o;if(!o){let s=\'\\"\'+Ce(t)+\'.getInitialProps()\\" should resolve to an object. But found \\"\'+o+\'\\" instead.\';throw new Error(s)}return Object.keys(o).length===0&&!e.ctx&&console.warn(\\"\\"+Ce(t)+\\" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\\"),o}var pr=typeof performance<\\"u\\",vi=pr&&[\\"mark\\",\\"measure\\",\\"getEntriesByName\\"].every(t=>typeof performance[t]==\\"function\\"),et=class extends Error{},tt=class extends Error{},rt=class extends Error{constructor(e){super(),this.code=\\"ENOENT\\",this.name=\\"PageNotFoundError\\",this.message=\\"Cannot find module for page: \\"+e}},nt=class extends Error{constructor(e,n){super(),this.message=\\"Failed to load static file for page: \\"+e+\\" \\"+n}},it=class extends Error{constructor(){super(),this.code=\\"ENOENT\\",this.message=\\"Cannot find the middleware module\\"}};function xi(t){return JSON.stringify({message:t.message,stack:t.stack})}});var lt=h(st=>{\\"use strict\\";Object.defineProperty(st,\\"__esModule\\",{value:!0});Object.defineProperty(st,\\"removeTrailingSlash\\",{enumerable:!0,get:function(){return Pi}});function Pi(t){return t.replace(/\\\\/$/,\\"\\")||\\"/\\"}});var Se=h(at=>{\\"use strict\\";Object.defineProperty(at,\\"__esModule\\",{value:!0});Object.defineProperty(at,\\"parsePath\\",{enumerable:!0,get:function(){return Ti}});function Ti(t){let e=t.indexOf(\\"#\\"),n=t.indexOf(\\"?\\"),i=n>-1&&(e<0||n<e);return i||e>-1?{pathname:t.substring(0,i?n:e),query:i?t.substring(n,e>-1?e:void 0):\\"\\",hash:e>-1?t.slice(e):\\"\\"}:{pathname:t,query:\\"\\",hash:\\"\\"}}});var be=h((q,hr)=>{\\"use strict\\";Object.defineProperty(q,\\"__esModule\\",{value:!0});Object.defineProperty(q,\\"normalizePathTrailingSlash\\",{enumerable:!0,get:function(){return ki}});var mr=lt(),Ri=Se(),ki=t=>{if(!t.startsWith(\\"/\\")||process.env.__NEXT_MANUAL_TRAILING_SLASH)return t;let{pathname:e,query:n,hash:i}=(0,Ri.parsePath)(t);return process.env.__NEXT_TRAILING_SLASH?/\\\\.[^/]+\\\\/?$/.test(e)?\\"\\"+(0,mr.removeTrailingSlash)(e)+n+i:e.endsWith(\\"/\\")?\\"\\"+e+n+i:e+\\"/\\"+n+i:\\"\\"+(0,mr.removeTrailingSlash)(e)+n+i};(typeof q.default==\\"function\\"||typeof q.default==\\"object\\"&&q.default!==null)&&typeof q.default.__esModule>\\"u\\"&&(Object.defineProperty(q.default,\\"__esModule\\",{value:!0}),Object.assign(q.default,q),hr.exports=q.default)});var ct=h(ut=>{\\"use strict\\";Object.defineProperty(ut,\\"__esModule\\",{value:!0});Object.defineProperty(ut,\\"pathHasPrefix\\",{enumerable:!0,get:function(){return wi}});var Ei=Se();function wi(t,e){if(typeof t!=\\"string\\")return!1;let{pathname:n}=(0,Ei.parsePath)(t);return n===e||n.startsWith(e+\\"/\\")}});var _r=h((F,br)=>{\\"use strict\\";Object.defineProperty(F,\\"__esModule\\",{value:!0});Object.defineProperty(F,\\"hasBasePath\\",{enumerable:!0,get:function(){return Si}});var Oi=ct(),Ci=process.env.__NEXT_ROUTER_BASEPATH||\\"\\";function Si(t){return(0,Oi.pathHasPrefix)(t,Ci)}(typeof F.default==\\"function\\"||typeof F.default==\\"object\\"&&F.default!==null)&&typeof F.default.__esModule>\\"u\\"&&(Object.defineProperty(F.default,\\"__esModule\\",{value:!0}),Object.assign(F.default,F),br.exports=F.default)});var ft=h(dt=>{\\"use strict\\";Object.defineProperty(dt,\\"__esModule\\",{value:!0});Object.defineProperty(dt,\\"isLocalURL\\",{enumerable:!0,get:function(){return ji}});var gr=he(),Di=_r();function ji(t){if(!(0,gr.isAbsoluteUrl)(t))return!0;try{let e=(0,gr.getLocationOrigin)(),n=new URL(t,e);return n.origin===e&&(0,Di.hasBasePath)(n.pathname)}catch{return!1}}});var yr=h(mt=>{\\"use strict\\";Object.defineProperty(mt,\\"__esModule\\",{value:!0});Object.defineProperty(mt,\\"getSortedRoutes\\",{enumerable:!0,get:function(){return Ai}});var pt=class t{insert(e){this._insert(e.split(\\"/\\").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){e===void 0&&(e=\\"/\\");let n=[...this.children.keys()].sort();this.slugName!==null&&n.splice(n.indexOf(\\"[]\\"),1),this.restSlugName!==null&&n.splice(n.indexOf(\\"[...]\\"),1),this.optionalRestSlugName!==null&&n.splice(n.indexOf(\\"[[...]]\\"),1);let i=n.map(o=>this.children.get(o)._smoosh(\\"\\"+e+o+\\"/\\")).reduce((o,s)=>[...o,...s],[]);if(this.slugName!==null&&i.push(...this.children.get(\\"[]\\")._smoosh(e+\\"[\\"+this.slugName+\\"]/\\")),!this.placeholder){let o=e===\\"/\\"?\\"/\\":e.slice(0,-1);if(this.optionalRestSlugName!=null)throw new Error(\'You cannot define a route with the same specificity as a optional catch-all route (\\"\'+o+\'\\" and \\"\'+o+\\"[[...\\"+this.optionalRestSlugName+\']]\\").\');i.unshift(o)}return this.restSlugName!==null&&i.push(...this.children.get(\\"[...]\\")._smoosh(e+\\"[...\\"+this.restSlugName+\\"]/\\")),this.optionalRestSlugName!==null&&i.push(...this.children.get(\\"[[...]]\\")._smoosh(e+\\"[[...\\"+this.optionalRestSlugName+\\"]]/\\")),i}_insert(e,n,i){if(e.length===0){this.placeholder=!1;return}if(i)throw new Error(\\"Catch-all must be the last part of the URL.\\");let o=e[0];if(o.startsWith(\\"[\\")&&o.endsWith(\\"]\\")){let d=function(u,p){if(u!==null&&u!==p)throw new Error(\\"You cannot use different slug names for the same dynamic path (\'\\"+u+\\"\' !== \'\\"+p+\\"\').\\");n.forEach(x=>{if(x===p)throw new Error(\'You cannot have the same slug name \\"\'+p+\'\\" repeat within a single dynamic path\');if(x.replace(/\\\\W/g,\\"\\")===o.replace(/\\\\W/g,\\"\\"))throw new Error(\'You cannot have the slug names \\"\'+x+\'\\" and \\"\'+p+\'\\" differ only by non-word symbols within a single dynamic path\')}),n.push(p)},s=o.slice(1,-1),f=!1;if(s.startsWith(\\"[\\")&&s.endsWith(\\"]\\")&&(s=s.slice(1,-1),f=!0),s.startsWith(\\"...\\")&&(s=s.substring(3),i=!0),s.startsWith(\\"[\\")||s.endsWith(\\"]\\"))throw new Error(\\"Segment names may not start or end with extra brackets (\'\\"+s+\\"\').\\");if(s.startsWith(\\".\\"))throw new Error(\\"Segment names may not start with erroneous periods (\'\\"+s+\\"\').\\");if(i)if(f){if(this.restSlugName!=null)throw new Error(\'You cannot use both an required and optional catch-all route at the same level (\\"[...\'+this.restSlugName+\']\\" and \\"\'+e[0]+\'\\" ).\');d(this.optionalRestSlugName,s),this.optionalRestSlugName=s,o=\\"[[...]]\\"}else{if(this.optionalRestSlugName!=null)throw new Error(\'You cannot use both an optional and required catch-all route at the same level (\\"[[...\'+this.optionalRestSlugName+\']]\\" and \\"\'+e[0]+\'\\").\');d(this.restSlugName,s),this.restSlugName=s,o=\\"[...]\\"}else{if(f)throw new Error(\'Optional route parameters are not yet supported (\\"\'+e[0]+\'\\").\');d(this.slugName,s),this.slugName=s,o=\\"[]\\"}}this.children.has(o)||this.children.set(o,new t),this.children.get(o)._insert(e.slice(1),n,i)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}};function Ai(t){let e=new pt;return t.forEach(n=>e.insert(n)),e.smoosh()}});var Nr=h(ht=>{\\"use strict\\";Object.defineProperty(ht,\\"__esModule\\",{value:!0});Object.defineProperty(ht,\\"isDynamicRoute\\",{enumerable:!0,get:function(){return Ui}});var Ii=/\\\\/\\\\[[^/]+?\\\\](?=\\\\/|$)/;function Ui(t){return Ii.test(t)}});var vr=h(bt=>{\\"use strict\\";Object.defineProperty(bt,\\"__esModule\\",{value:!0});function Wi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Wi(bt,{getSortedRoutes:function(){return zi.getSortedRoutes},isDynamicRoute:function(){return Mi.isDynamicRoute}});var zi=yr(),Mi=Nr()});var xr=h(_t=>{\\"use strict\\";Object.defineProperty(_t,\\"__esModule\\",{value:!0});Object.defineProperty(_t,\\"getRouteMatcher\\",{enumerable:!0,get:function(){return qi}});var Li=he();function qi(t){let{re:e,groups:n}=t;return i=>{let o=e.exec(i);if(!o)return!1;let s=d=>{try{return decodeURIComponent(d)}catch{throw new Li.DecodeError(\\"failed to decode param\\")}},f={};return Object.keys(n).forEach(d=>{let u=n[d],p=o[u.pos];p!==void 0&&(f[d]=~p.indexOf(\\"/\\")?p.split(\\"/\\").map(x=>s(x)):u.repeat?[s(p)]:s(p))}),f}}});var Pr=h(gt=>{\\"use strict\\";Object.defineProperty(gt,\\"__esModule\\",{value:!0});Object.defineProperty(gt,\\"ensureLeadingSlash\\",{enumerable:!0,get:function(){return Fi}});function Fi(t){return t.startsWith(\\"/\\")?t:\\"/\\"+t}});var Tr=h(yt=>{\\"use strict\\";Object.defineProperty(yt,\\"__esModule\\",{value:!0});Object.defineProperty(yt,\\"isGroupSegment\\",{enumerable:!0,get:function(){return Gi}});function Gi(t){return t[0]===\\"(\\"&&t.endsWith(\\")\\")}});var Rr=h(Nt=>{\\"use strict\\";Object.defineProperty(Nt,\\"__esModule\\",{value:!0});function Vi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Vi(Nt,{normalizeAppPath:function(){return Ki},normalizeRscPath:function(){return Yi}});var $i=Pr(),Hi=Tr();function Ki(t){return(0,$i.ensureLeadingSlash)(t.split(\\"/\\").reduce((e,n,i,o)=>!n||(0,Hi.isGroupSegment)(n)||n[0]===\\"@\\"||(n===\\"page\\"||n===\\"route\\")&&i===o.length-1?e:e+\\"/\\"+n,\\"\\"))}function Yi(t,e){return e?t.replace(/\\\\.rsc($|\\\\?)/,\\"$1\\"):t}});var kr=h(xt=>{\\"use strict\\";Object.defineProperty(xt,\\"__esModule\\",{value:!0});function Bi(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Bi(xt,{INTERCEPTION_ROUTE_MARKERS:function(){return vt},isInterceptionRouteAppPath:function(){return Qi},extractInterceptionRouteInformation:function(){return Zi}});var Xi=Rr(),vt=[\\"(..)(..)\\",\\"(.)\\",\\"(..)\\",\\"(...)\\"];function Qi(t){return t.split(\\"/\\").find(e=>vt.find(n=>e.startsWith(n)))!==void 0}function Zi(t){let e,n,i;for(let o of t.split(\\"/\\"))if(n=vt.find(s=>o.startsWith(s)),n){[e,i]=t.split(n,2);break}if(!e||!n||!i)throw new Error(`Invalid interception route: ${t}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(e=(0,Xi.normalizeAppPath)(e),n){case\\"(.)\\":e===\\"/\\"?i=`/${i}`:i=e+\\"/\\"+i;break;case\\"(..)\\":if(e===\\"/\\")throw new Error(`Invalid interception route: ${t}. Cannot use (..) marker at the root level, use (.) instead.`);i=e.split(\\"/\\").slice(0,-1).concat(i).join(\\"/\\");break;case\\"(...)\\":i=\\"/\\"+i;break;case\\"(..)(..)\\":let o=e.split(\\"/\\");if(o.length<=2)throw new Error(`Invalid interception route: ${t}. Cannot use (..)(..) marker at the root level or one level up.`);i=o.slice(0,-2).concat(i).join(\\"/\\");break;default:throw new Error(\\"Invariant: unexpected marker\\")}return{interceptingRoute:e,interceptedRoute:i}}});var Er=h(Pt=>{\\"use strict\\";Object.defineProperty(Pt,\\"__esModule\\",{value:!0});Object.defineProperty(Pt,\\"escapeStringRegexp\\",{enumerable:!0,get:function(){return to}});var Ji=/[|\\\\\\\\{}()[\\\\]^$+*?.-]/,eo=/[|\\\\\\\\{}()[\\\\]^$+*?.-]/g;function to(t){return Ji.test(t)?t.replace(eo,\\"\\\\\\\\$&\\"):t}});var Ar=h(kt=>{\\"use strict\\";Object.defineProperty(kt,\\"__esModule\\",{value:!0});function ro(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}ro(kt,{getRouteRegex:function(){return Dr},getNamedRouteRegex:function(){return so},getNamedMiddlewareRegex:function(){return lo}});var Or=kr(),Tt=Er(),Cr=lt(),no=\\"nxtP\\",io=\\"nxtI\\";function Rt(t){let e=t.startsWith(\\"[\\")&&t.endsWith(\\"]\\");e&&(t=t.slice(1,-1));let n=t.startsWith(\\"...\\");return n&&(t=t.slice(3)),{key:t,repeat:n,optional:e}}function Sr(t){let e=(0,Cr.removeTrailingSlash)(t).slice(1).split(\\"/\\"),n={},i=1;return{parameterizedRoute:e.map(o=>{let s=Or.INTERCEPTION_ROUTE_MARKERS.find(d=>o.startsWith(d)),f=o.match(/\\\\[((?:\\\\[.*\\\\])|.+)\\\\]/);if(s&&f){let{key:d,optional:u,repeat:p}=Rt(f[1]);return n[d]={pos:i++,repeat:p,optional:u},\\"/\\"+(0,Tt.escapeStringRegexp)(s)+\\"([^/]+?)\\"}else if(f){let{key:d,repeat:u,optional:p}=Rt(f[1]);return n[d]={pos:i++,repeat:u,optional:p},u?p?\\"(?:/(.+?))?\\":\\"/(.+?)\\":\\"/([^/]+?)\\"}else return\\"/\\"+(0,Tt.escapeStringRegexp)(o)}).join(\\"\\"),groups:n}}function Dr(t){let{parameterizedRoute:e,groups:n}=Sr(t);return{re:new RegExp(\\"^\\"+e+\\"(?:/)?$\\"),groups:n}}function oo(){let t=0;return()=>{let e=\\"\\",n=++t;for(;n>0;)e+=String.fromCharCode(97+(n-1)%26),n=Math.floor((n-1)/26);return e}}function wr(t){let{getSafeRouteKey:e,segment:n,routeKeys:i,keyPrefix:o}=t,{key:s,optional:f,repeat:d}=Rt(n),u=s.replace(/\\\\W/g,\\"\\");o&&(u=\\"\\"+o+u);let p=!1;return(u.length===0||u.length>30)&&(p=!0),isNaN(parseInt(u.slice(0,1)))||(p=!0),p&&(u=e()),o?i[u]=\\"\\"+o+s:i[u]=\\"\\"+s,d?f?\\"(?:/(?<\\"+u+\\">.+?))?\\":\\"/(?<\\"+u+\\">.+?)\\":\\"/(?<\\"+u+\\">[^/]+?)\\"}function jr(t,e){let n=(0,Cr.removeTrailingSlash)(t).slice(1).split(\\"/\\"),i=oo(),o={};return{namedParameterizedRoute:n.map(s=>{let f=Or.INTERCEPTION_ROUTE_MARKERS.some(u=>s.startsWith(u)),d=s.match(/\\\\[((?:\\\\[.*\\\\])|.+)\\\\]/);return f&&d?wr({getSafeRouteKey:i,segment:d[1],routeKeys:o,keyPrefix:e?io:void 0}):d?wr({getSafeRouteKey:i,segment:d[1],routeKeys:o,keyPrefix:e?no:void 0}):\\"/\\"+(0,Tt.escapeStringRegexp)(s)}).join(\\"\\"),routeKeys:o}}function so(t,e){let n=jr(t,e);return{...Dr(t),namedRegex:\\"^\\"+n.namedParameterizedRoute+\\"(?:/)?$\\",routeKeys:n.routeKeys}}function lo(t,e){let{parameterizedRoute:n}=Sr(t),{catchAll:i=!0}=e;if(n===\\"/\\")return{namedRegex:\\"^/\\"+(i?\\".*\\":\\"\\")+\\"$\\"};let{namedParameterizedRoute:o}=jr(t,!1),s=i?\\"(?:(/.*)?)\\":\\"\\";return{namedRegex:\\"^\\"+o+s+\\"$\\"}}});var Ir=h(Et=>{\\"use strict\\";Object.defineProperty(Et,\\"__esModule\\",{value:!0});Object.defineProperty(Et,\\"interpolateAs\\",{enumerable:!0,get:function(){return co}});var ao=xr(),uo=Ar();function co(t,e,n){let i=\\"\\",o=(0,uo.getRouteRegex)(t),s=o.groups,f=(e!==t?(0,ao.getRouteMatcher)(o)(e):\\"\\")||n;i=t;let d=Object.keys(s);return d.every(u=>{let p=f[u]||\\"\\",{repeat:x,optional:S}=s[u],T=\\"[\\"+(x?\\"...\\":\\"\\")+u+\\"]\\";return S&&(T=(p?\\"\\":\\"/\\")+\\"[\\"+T+\\"]\\"),x&&!Array.isArray(p)&&(p=[p]),(S||u in f)&&(i=i.replace(T,x?p.map(U=>encodeURIComponent(U)).join(\\"/\\"):encodeURIComponent(p))||\\"/\\")})||(i=\\"\\"),{params:d,result:i}}});var zr=h((G,Wr)=>{\\"use strict\\";Object.defineProperty(G,\\"__esModule\\",{value:!0});Object.defineProperty(G,\\"resolveHref\\",{enumerable:!0,get:function(){return yo}});var fo=Be(),Ur=Ze(),po=ur(),mo=he(),ho=be(),bo=ft(),_o=vr(),go=Ir();function yo(t,e,n){let i,o=typeof e==\\"string\\"?e:(0,Ur.formatWithValidation)(e),s=o.match(/^[a-zA-Z]{1,}:\\\\/\\\\//),f=s?o.slice(s[0].length):o;if((f.split(\\"?\\")[0]||\\"\\").match(/(\\\\/\\\\/|\\\\\\\\)/)){console.error(\\"Invalid href \'\\"+o+\\"\' passed to next/router in page: \'\\"+t.pathname+\\"\'. Repeated forward-slashes (//) or backslashes \\\\\\\\ are not valid in the href.\\");let u=(0,mo.normalizeRepeatedSlashes)(f);o=(s?s[0]:\\"\\")+u}if(!(0,bo.isLocalURL)(o))return n?[o]:o;try{i=new URL(o.startsWith(\\"#\\")?t.asPath:t.pathname,\\"http://n\\")}catch{i=new URL(\\"/\\",\\"http://n\\")}try{let u=new URL(o,i);u.pathname=(0,ho.normalizePathTrailingSlash)(u.pathname);let p=\\"\\";if((0,_o.isDynamicRoute)(u.pathname)&&u.searchParams&&n){let S=(0,fo.searchParamsToUrlQuery)(u.searchParams),{result:T,params:U}=(0,go.interpolateAs)(u.pathname,u.pathname,S);T&&(p=(0,Ur.formatWithValidation)({pathname:T,hash:u.hash,query:(0,po.omit)(S,U)}))}let x=u.origin===i.origin?u.href.slice(u.origin.length):u.href;return n?[x,p||x]:x}catch{return n?[o]:o}}(typeof G.default==\\"function\\"||typeof G.default==\\"object\\"&&G.default!==null)&&typeof G.default.__esModule>\\"u\\"&&(Object.defineProperty(G.default,\\"__esModule\\",{value:!0}),Object.assign(G.default,G),Wr.exports=G.default)});var Ot=h(wt=>{\\"use strict\\";Object.defineProperty(wt,\\"__esModule\\",{value:!0});Object.defineProperty(wt,\\"addPathPrefix\\",{enumerable:!0,get:function(){return vo}});var No=Se();function vo(t,e){if(!t.startsWith(\\"/\\")||!e)return t;let{pathname:n,query:i,hash:o}=(0,No.parsePath)(t);return\\"\\"+e+n+i+o}});var Lr=h(Ct=>{\\"use strict\\";Object.defineProperty(Ct,\\"__esModule\\",{value:!0});Object.defineProperty(Ct,\\"addLocale\\",{enumerable:!0,get:function(){return Po}});var xo=Ot(),Mr=ct();function Po(t,e,n,i){if(!e||e===n)return t;let o=t.toLowerCase();return!i&&((0,Mr.pathHasPrefix)(o,\\"/api\\")||(0,Mr.pathHasPrefix)(o,\\"/\\"+e.toLowerCase()))?t:(0,xo.addPathPrefix)(t,\\"/\\"+e)}});var Fr=h((V,qr)=>{\\"use strict\\";Object.defineProperty(V,\\"__esModule\\",{value:!0});Object.defineProperty(V,\\"addLocale\\",{enumerable:!0,get:function(){return Ro}});var To=be(),Ro=function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return process.env.__NEXT_I18N_SUPPORT?(0,To.normalizePathTrailingSlash)(Lr().addLocale(t,...n)):t};(typeof V.default==\\"function\\"||typeof V.default==\\"object\\"&&V.default!==null)&&typeof V.default.__esModule>\\"u\\"&&(Object.defineProperty(V.default,\\"__esModule\\",{value:!0}),Object.assign(V.default,V),qr.exports=V.default)});var Vr=h(St=>{\\"use strict\\";Object.defineProperty(St,\\"__esModule\\",{value:!0});Object.defineProperty(St,\\"RouterContext\\",{enumerable:!0,get:function(){return Gr}});var ko=Oe(),Eo=ko._(de()),Gr=Eo.default.createContext(null);Gr.displayName=\\"RouterContext\\"});var Br=h(jt=>{\\"use client\\";\\"use strict\\";Object.defineProperty(jt,\\"__esModule\\",{value:!0});function wo(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}wo(jt,{CacheStates:function(){return Dt},AppRouterContext:function(){return $r},LayoutRouterContext:function(){return Hr},GlobalLayoutRouterContext:function(){return Kr},TemplateContext:function(){return Yr}});var Oo=Oe(),De=Oo._(de()),Dt;(function(t){t.LAZY_INITIALIZED=\\"LAZYINITIALIZED\\",t.DATA_FETCH=\\"DATAFETCH\\",t.READY=\\"READY\\"})(Dt||(Dt={}));var $r=De.default.createContext(null),Hr=De.default.createContext(null),Kr=De.default.createContext(null),Yr=De.default.createContext(null);$r.displayName=\\"AppRouterContext\\",Hr.displayName=\\"LayoutRouterContext\\",Kr.displayName=\\"GlobalLayoutRouterContext\\",Yr.displayName=\\"TemplateContext\\"});var Qr=h(($,Xr)=>{\\"use strict\\";Object.defineProperty($,\\"__esModule\\",{value:!0});function Co(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Co($,{requestIdleCallback:function(){return So},cancelIdleCallback:function(){return Do}});var So=typeof self<\\"u\\"&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(t){let e=Date.now();return self.setTimeout(function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})},1)},Do=typeof self<\\"u\\"&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(t){return clearTimeout(t)};(typeof $.default==\\"function\\"||typeof $.default==\\"object\\"&&$.default!==null)&&typeof $.default.__esModule>\\"u\\"&&(Object.defineProperty($.default,\\"__esModule\\",{value:!0}),Object.assign($.default,$),Xr.exports=$.default)});var tn=h((H,en)=>{\\"use strict\\";Object.defineProperty(H,\\"__esModule\\",{value:!0});Object.defineProperty(H,\\"useIntersection\\",{enumerable:!0,get:function(){return Io}});var _e=de(),Zr=Qr(),Jr=typeof IntersectionObserver==\\"function\\",At=new Map,je=[];function jo(t){let e={root:t.root||null,margin:t.rootMargin||\\"\\"},n=je.find(f=>f.root===e.root&&f.margin===e.margin),i;if(n&&(i=At.get(n),i))return i;let o=new Map,s=new IntersectionObserver(f=>{f.forEach(d=>{let u=o.get(d.target),p=d.isIntersecting||d.intersectionRatio>0;u&&p&&u(p)})},t);return i={id:e,observer:s,elements:o},je.push(e),At.set(e,i),i}function Ao(t,e,n){let{id:i,observer:o,elements:s}=jo(n);return s.set(t,e),o.observe(t),function(){if(s.delete(t),o.unobserve(t),s.size===0){o.disconnect(),At.delete(i);let d=je.findIndex(u=>u.root===i.root&&u.margin===i.margin);d>-1&&je.splice(d,1)}}}function Io(t){let{rootRef:e,rootMargin:n,disabled:i}=t,o=i||!Jr,[s,f]=(0,_e.useState)(!1),d=(0,_e.useRef)(null),u=(0,_e.useCallback)(x=>{d.current=x},[]);(0,_e.useEffect)(()=>{if(Jr){if(o||s)return;let x=d.current;if(x&&x.tagName)return Ao(x,T=>T&&f(T),{root:e?.current,rootMargin:n})}else if(!s){let x=(0,Zr.requestIdleCallback)(()=>f(!0));return()=>(0,Zr.cancelIdleCallback)(x)}},[o,n,e,s,d.current]);let p=(0,_e.useCallback)(()=>{f(!1)},[]);return[u,s,p]}(typeof H.default==\\"function\\"||typeof H.default==\\"object\\"&&H.default!==null)&&typeof H.default.__esModule>\\"u\\"&&(Object.defineProperty(H.default,\\"__esModule\\",{value:!0}),Object.assign(H.default,H),en.exports=H.default)});var rn=h(It=>{\\"use strict\\";Object.defineProperty(It,\\"__esModule\\",{value:!0});Object.defineProperty(It,\\"normalizeLocalePath\\",{enumerable:!0,get:function(){return Uo}});function Uo(t,e){let n,i=t.split(\\"/\\");return(e||[]).some(o=>i[1]&&i[1].toLowerCase()===o.toLowerCase()?(n=o,i.splice(1,1),t=i.join(\\"/\\")||\\"/\\",!0):!1),{pathname:t,detectedLocale:n}}});var on=h((K,nn)=>{\\"use strict\\";Object.defineProperty(K,\\"__esModule\\",{value:!0});Object.defineProperty(K,\\"normalizeLocalePath\\",{enumerable:!0,get:function(){return Wo}});var Wo=(t,e)=>process.env.__NEXT_I18N_SUPPORT?rn().normalizeLocalePath(t,e):{pathname:t,detectedLocale:void 0};(typeof K.default==\\"function\\"||typeof K.default==\\"object\\"&&K.default!==null)&&typeof K.default.__esModule>\\"u\\"&&(Object.defineProperty(K.default,\\"__esModule\\",{value:!0}),Object.assign(K.default,K),nn.exports=K.default)});var sn=h(Ut=>{\\"use strict\\";Object.defineProperty(Ut,\\"__esModule\\",{value:!0});Object.defineProperty(Ut,\\"detectDomainLocale\\",{enumerable:!0,get:function(){return zo}});function zo(t,e,n){if(t){n&&(n=n.toLowerCase());for(let s of t){var i,o;let f=(i=s.domain)==null?void 0:i.split(\\":\\")[0].toLowerCase();if(e===f||n===s.defaultLocale.toLowerCase()||(o=s.locales)!=null&&o.some(d=>d.toLowerCase()===n))return s}}}});var an=h((Y,ln)=>{\\"use strict\\";Object.defineProperty(Y,\\"__esModule\\",{value:!0});Object.defineProperty(Y,\\"detectDomainLocale\\",{enumerable:!0,get:function(){return Mo}});var Mo=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(process.env.__NEXT_I18N_SUPPORT)return sn().detectDomainLocale(...e)};(typeof Y.default==\\"function\\"||typeof Y.default==\\"object\\"&&Y.default!==null)&&typeof Y.default.__esModule>\\"u\\"&&(Object.defineProperty(Y.default,\\"__esModule\\",{value:!0}),Object.assign(Y.default,Y),ln.exports=Y.default)});var cn=h((B,un)=>{\\"use strict\\";Object.defineProperty(B,\\"__esModule\\",{value:!0});Object.defineProperty(B,\\"getDomainLocale\\",{enumerable:!0,get:function(){return Fo}});var Lo=be(),qo=process.env.__NEXT_ROUTER_BASEPATH||\\"\\";function Fo(t,e,n,i){if(process.env.__NEXT_I18N_SUPPORT){let o=on().normalizeLocalePath,s=an().detectDomainLocale,f=e||o(t,n).detectedLocale,d=s(i,void 0,f);if(d){let u=\\"http\\"+(d.http?\\"\\":\\"s\\")+\\"://\\",p=f===d.defaultLocale?\\"\\":\\"/\\"+f;return\\"\\"+u+d.domain+(0,Lo.normalizePathTrailingSlash)(\\"\\"+qo+p+t)}return!1}else return!1}(typeof B.default==\\"function\\"||typeof B.default==\\"object\\"&&B.default!==null)&&typeof B.default.__esModule>\\"u\\"&&(Object.defineProperty(B.default,\\"__esModule\\",{value:!0}),Object.assign(B.default,B),un.exports=B.default)});var fn=h((X,dn)=>{\\"use strict\\";Object.defineProperty(X,\\"__esModule\\",{value:!0});Object.defineProperty(X,\\"addBasePath\\",{enumerable:!0,get:function(){return Ho}});var Go=Ot(),Vo=be(),$o=process.env.__NEXT_ROUTER_BASEPATH||\\"\\";function Ho(t,e){return(0,Vo.normalizePathTrailingSlash)(process.env.__NEXT_MANUAL_CLIENT_BASE_PATH&&!e?t:(0,Go.addPathPrefix)(t,$o))}(typeof X.default==\\"function\\"||typeof X.default==\\"object\\"&&X.default!==null)&&typeof X.default.__esModule>\\"u\\"&&(Object.defineProperty(X.default,\\"__esModule\\",{value:!0}),Object.assign(X.default,X),dn.exports=X.default)});var mn=h((Q,pn)=>{\\"use strict\\";Object.defineProperty(Q,\\"__esModule\\",{value:!0});function Ko(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}Ko(Q,{PrefetchKind:function(){return Wt},ACTION_REFRESH:function(){return Yo},ACTION_NAVIGATE:function(){return Bo},ACTION_RESTORE:function(){return Xo},ACTION_SERVER_PATCH:function(){return Qo},ACTION_PREFETCH:function(){return Zo},ACTION_FAST_REFRESH:function(){return Jo},ACTION_SERVER_ACTION:function(){return es}});var Yo=\\"refresh\\",Bo=\\"navigate\\",Xo=\\"restore\\",Qo=\\"server-patch\\",Zo=\\"prefetch\\",Jo=\\"fast-refresh\\",es=\\"server-action\\",Wt;(function(t){t.AUTO=\\"auto\\",t.FULL=\\"full\\",t.TEMPORARY=\\"temporary\\"})(Wt||(Wt={}));(typeof Q.default==\\"function\\"||typeof Q.default==\\"object\\"&&Q.default!==null)&&typeof Q.default.__esModule>\\"u\\"&&(Object.defineProperty(Q.default,\\"__esModule\\",{value:!0}),Object.assign(Q.default,Q),pn.exports=Q.default)});var vn=h((Z,Nn)=>{\\"use client\\";\\"use strict\\";Object.defineProperty(Z,\\"__esModule\\",{value:!0});Object.defineProperty(Z,\\"default\\",{enumerable:!0,get:function(){return ps}});var ts=Oe(),A=ts._(de()),hn=zr(),yn=ft(),rs=Ze(),ns=he(),is=Fr(),os=Vr(),ss=Br(),ls=tn(),as=cn(),us=fn(),bn=mn(),_n=new Set;function zt(t,e,n,i,o,s){if(typeof window>\\"u\\"||!s&&!(0,yn.isLocalURL)(e))return;if(!i.bypassPrefetchedCheck){let d=typeof i.locale<\\"u\\"?i.locale:\\"locale\\"in t?t.locale:void 0,u=e+\\"%\\"+n+\\"%\\"+d;if(_n.has(u))return;_n.add(u)}let f=s?t.prefetch(e,o):t.prefetch(e,n,i);Promise.resolve(f).catch(d=>{throw d})}function cs(t){let n=t.currentTarget.getAttribute(\\"target\\");return n&&n!==\\"_self\\"||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&t.nativeEvent.which===2}function ds(t,e,n,i,o,s,f,d,u,p){let{nodeName:x}=t.currentTarget;if(x.toUpperCase()===\\"A\\"&&(cs(t)||!u&&!(0,yn.isLocalURL)(n)))return;t.preventDefault();let T=()=>{let U=f??!0;\\"beforePopState\\"in e?e[o?\\"replace\\":\\"push\\"](n,i,{shallow:s,locale:d,scroll:U}):e[o?\\"replace\\":\\"push\\"](i||n,{forceOptimisticNavigation:!p,scroll:U})};u?A.default.startTransition(T):T()}function gn(t){return typeof t==\\"string\\"?t:(0,rs.formatUrl)(t)}var fs=A.default.forwardRef(function(e,n){let i,{href:o,as:s,children:f,prefetch:d=null,passHref:u,replace:p,shallow:x,scroll:S,locale:T,onClick:U,onMouseEnter:le,onTouchStart:ge,legacyBehavior:I=!1,...te}=e;i=f,I&&(typeof i==\\"string\\"||typeof i==\\"number\\")&&(i=A.default.createElement(\\"a\\",null,i));let _=A.default.useContext(os.RouterContext),Ie=A.default.useContext(ss.AppRouterContext),z=_??Ie,M=!_,ie=d!==!1,ae=d===null?bn.PrefetchKind.AUTO:bn.PrefetchKind.FULL;{let b=function(g){return new Error(\\"Failed prop type: The prop `\\"+g.key+\\"` expects a \\"+g.expected+\\" in `<Link>`, but got `\\"+g.actual+\\"` instead.\\"+(typeof window<\\"u\\"?`\\nOpen your browser\'s console to view the Component stack trace.`:\\"\\"))};Object.keys({href:!0}).forEach(g=>{if(g===\\"href\\"){if(e[g]==null||typeof e[g]!=\\"string\\"&&typeof e[g]!=\\"object\\")throw b({key:g,expected:\\"`string` or `object`\\",actual:e[g]===null?\\"null\\":typeof e[g]})}else{let D=g}}),Object.keys({as:!0,replace:!0,scroll:!0,shallow:!0,passHref:!0,prefetch:!0,locale:!0,onClick:!0,onMouseEnter:!0,onTouchStart:!0,legacyBehavior:!0}).forEach(g=>{let D=typeof e[g];if(g===\\"as\\"){if(e[g]&&D!==\\"string\\"&&D!==\\"object\\")throw b({key:g,expected:\\"`string` or `object`\\",actual:D})}else if(g===\\"locale\\"){if(e[g]&&D!==\\"string\\")throw b({key:g,expected:\\"`string`\\",actual:D})}else if(g===\\"onClick\\"||g===\\"onMouseEnter\\"||g===\\"onTouchStart\\"){if(e[g]&&D!==\\"function\\")throw b({key:g,expected:\\"`function`\\",actual:D})}else if(g===\\"replace\\"||g===\\"scroll\\"||g===\\"shallow\\"||g===\\"passHref\\"||g===\\"prefetch\\"||g===\\"legacyBehavior\\"){if(e[g]!=null&&D!==\\"boolean\\")throw b({key:g,expected:\\"`boolean`\\",actual:D})}else{let Pe=g}});let xe=A.default.useRef(!1);e.prefetch&&!xe.current&&!M&&(xe.current=!0,console.warn(\\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\\"))}if(M&&!s){let b;if(typeof o==\\"string\\"?b=o:typeof o==\\"object\\"&&typeof o.pathname==\\"string\\"&&(b=o.pathname),b&&b.split(\\"/\\").some(ee=>ee.startsWith(\\"[\\")&&ee.endsWith(\\"]\\")))throw new Error(\\"Dynamic href `\\"+b+\\"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\\")}let{href:L,as:O}=A.default.useMemo(()=>{if(!_){let ee=gn(o);return{href:ee,as:s?gn(s):ee}}let[b,ne]=(0,hn.resolveHref)(_,o,!0);return{href:b,as:s?(0,hn.resolveHref)(_,s):ne||b}},[_,o,s]),ye=A.default.useRef(L),Ne=A.default.useRef(O),E;if(I){U&&console.warn(\'\\"onClick\\" was passed to <Link> with `href` of `\'+o+\'` but \\"legacyBehavior\\" was set. The legacy behavior requires onClick be set on the child of next/link\'),le&&console.warn(\'\\"onMouseEnter\\" was passed to <Link> with `href` of `\'+o+\'` but \\"legacyBehavior\\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link\');try{E=A.default.Children.only(i)}catch{throw i?new Error(\\"Multiple children were passed to <Link> with `href` of `\\"+o+\\"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\\"+(typeof window<\\"u\\"?` \\nOpen your browser\'s console to view the Component stack trace.`:\\"\\")):new Error(\\"No children were passed to <Link> with `href` of `\\"+o+\\"` but one child is required https://nextjs.org/docs/messages/link-no-children\\")}}else if(i?.type===\\"a\\")throw new Error(`Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor`);let w=I?E&&typeof E==\\"object\\"&&E.ref:n,[J,re,fe]=(0,ls.useIntersection)({rootMargin:\\"200px\\"}),ve=A.default.useCallback(b=>{(Ne.current!==O||ye.current!==L)&&(fe(),Ne.current=O,ye.current=L),J(b),w&&(typeof w==\\"function\\"?w(b):typeof w==\\"object\\"&&(w.current=b))},[O,w,L,fe,J]);A.default.useEffect(()=>{},[O,L,re,T,ie,_?.locale,z,M,ae]);let oe={ref:ve,onClick(b){if(!b)throw new Error(\'Component rendered inside next/link has to pass click event to \\"onClick\\" prop.\');!I&&typeof U==\\"function\\"&&U(b),I&&E.props&&typeof E.props.onClick==\\"function\\"&&E.props.onClick(b),z&&(b.defaultPrevented||ds(b,z,L,O,p,x,S,T,M,ie))},onMouseEnter(b){!I&&typeof le==\\"function\\"&&le(b),I&&E.props&&typeof E.props.onMouseEnter==\\"function\\"&&E.props.onMouseEnter(b),z&&((!ie||!0)&&M||zt(z,L,O,{locale:T,priority:!0,bypassPrefetchedCheck:!0},{kind:ae},M))},onTouchStart(b){!I&&typeof ge==\\"function\\"&&ge(b),I&&E.props&&typeof E.props.onTouchStart==\\"function\\"&&E.props.onTouchStart(b),z&&(!ie&&M||zt(z,L,O,{locale:T,priority:!0,bypassPrefetchedCheck:!0},{kind:ae},M))}};if((0,ns.isAbsoluteUrl)(O))oe.href=O;else if(!I||u||E.type===\\"a\\"&&!(\\"href\\"in E.props)){let b=typeof T<\\"u\\"?T:_?.locale,ne=_?.isLocaleDomain&&(0,as.getDomainLocale)(O,b,_?.locales,_?.domainLocales);oe.href=ne||(0,us.addBasePath)((0,is.addLocale)(O,b,_?.defaultLocale))}return I?A.default.cloneElement(E,oe):A.default.createElement(\\"a\\",{...te,...oe},i)}),ps=fs;(typeof Z.default==\\"function\\"||typeof Z.default==\\"object\\"&&Z.default!==null)&&typeof Z.default.__esModule>\\"u\\"&&(Object.defineProperty(Z.default,\\"__esModule\\",{value:!0}),Object.assign(Z.default,Z),Nn.exports=Z.default)});var Pn=h((Qs,xn)=>{xn.exports=vn()});var gs={};ei(gs,{default:()=>_s,faqData:()=>hs,frontmatter:()=>ms});var a=Jt(nr());function He({children:t,quizSlug:e}){return React.createElement(\\"article\\",{className:\\"max-w-4xl mx-auto text-gray-900\\"},React.createElement(\\"div\\",{className:\\"prose prose-lg max-w-none text-gray-900 [&>*]:text-gray-900\\"},t))}var Tn=Jt(Pn());function Ae({task:t,href:e}){let n=e||`/quiz/${t}`;return React.createElement(\\"div\\",{className:\\"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8\\"},React.createElement(\\"div\\",{className:\\"text-center\\"},React.createElement(\\"h3\\",{className:\\"text-xl font-semibold text-gray-900 mb-2\\"},\\"Not sure which AI fits your workflow?\\"),React.createElement(\\"p\\",{className:\\"text-gray-600 mb-4\\"},\\"Take our 30-second quiz to get a personalized recommendation\\"),React.createElement(Tn.default,{href:n,className:\\"inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors\\"},\\"Take the \\",t.charAt(0).toUpperCase()+t.slice(1),\\" Quiz \\\\u2192\\")))}var ms={title:\\"Best AI for Writing (2025) \\\\u2014 Claude 3.5 vs GPT-4o, Gemini & more\\",description:\\"Comprehensive comparison of Claude 3.5, GPT-4o, Gemini, Perplexity & Grok for bloggers, students, and marketers. Find your perfect AI writing assistant.\\",slug:\\"best-ai-for-writing-2025-claude-3-5-vs-gpt-4o-gemini-more\\",template:\\"pillar\\",cluster:\\"text\\",priority:\\"Low\\",lastUpdated:\\"2025-07-22\\"},hs=[{q:\\"What is the best free AI for writing?\\",a:\\"Perplexity offers the strongest free tier for fact-based writing, while Claude\'s free tier is best for creative prose.\\"},{q:\\"Can Google penalise AI-generated content?\\",a:\\"Google ranks helpful content regardless of how it\'s produced; thin or spammy AI text can be penalised.\\"},{q:\\"What\'s a context window and why does it matter?\\",a:\\"It\'s the amount of text an AI can \'remember\'. Bigger windows (e.g., Claude\'s 200 k tokens) keep long documents coherent.\\"},{q:\\"Which AI is best for creative writing?\\",a:\\"Claude 3.5 Sonnet consistently produces the most human-like, nuanced prose.\\"},{q:\\"Which AI provides reliable citations?\\",a:\\"Perplexity Pro surfaces sources and clickable references by default.\\"},{q:\\"Is GPT-4o still king in 2025?\\",a:\\"It\'s the best all-rounder, but Claude wins on style and Perplexity on accuracy.\\"}];function Rn(t){let e=Object.assign({h2:\\"h2\\",p:\\"p\\",strong:\\"strong\\",ul:\\"ul\\",li:\\"li\\",hr:\\"hr\\",em:\\"em\\",blockquote:\\"blockquote\\",h3:\\"h3\\",a:\\"a\\"},t.components);return(0,a.jsxDEV)(He,{quizSlug:\\"writing\\",children:[(0,a.jsxDEV)(Ae,{task:\\"writing\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:16,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\\"Who are you writing for ?\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:18,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.strong,{children:\\"The Blogger\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:20,columnNumber:1},this)},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:20,columnNumber:1},this),(0,a.jsxDEV)(e.ul,{children:[`\\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Pain\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:22,columnNumber:3},this),\\" \\\\u2013 needs original long-form content that won\'t feel robotic or earn an SEO penalty.\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:22,columnNumber:1},this),`\\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Ideal output\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:23,columnNumber:3},this),\\" \\\\u2013 an AI blog generator that keeps a consistent tone.\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:23,columnNumber:1},this),`\\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Killer feature\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:24,columnNumber:3},this),\\" \\\\u2013 a huge context window to track details across thousands of words.\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:24,columnNumber:1},this),`\\n`]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:22,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.strong,{children:\\"The Student\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:26,columnNumber:1},this)},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:26,columnNumber:1},this),(0,a.jsxDEV)(e.ul,{children:[`\\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Pain\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:28,columnNumber:3},this),\\" \\\\u2013 must research, structure, and cite accurately while avoiding plagiarism.\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:28,columnNumber:1},this),`\\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Ideal output\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:29,columnNumber:3},this),\\" \\\\u2013 an AI essay writer that returns verifiable facts with citations.\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:29,columnNumber:1},this),`\\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Killer feature\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:30,columnNumber:3},this),\\" \\\\u2013 can ingest PDFs and analyse them directly.\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:30,columnNumber:1},this),`\\n`]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:28,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.strong,{children:\\"The Marketer\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:32,columnNumber:1},this)},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:32,columnNumber:1},this),(0,a.jsxDEV)(e.ul,{children:[`\\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Pain\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:34,columnNumber:3},this),\\" \\\\u2013 high-volume, mixed-format content plus brand-voice consistency.\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:34,columnNumber:1},this),`\\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Ideal output\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:35,columnNumber:3},this),\\" \\\\u2013 a tool that plugs into Google Workspace and accelerates campaigns.\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:35,columnNumber:1},this),`\\n`,(0,a.jsxDEV)(e.li,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Killer feature\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:36,columnNumber:3},this),\\" \\\\u2013 analyses spreadsheet data and builds project plans.\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:36,columnNumber:1},this),`\\n`]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:34,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:38,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\'A market of specialists, not one \\"best\\" model\'},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:40,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[\\"Perplexity is an \\",(0,a.jsxDEV)(e.strong,{children:\\"answer engine\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:42,columnNumber:18},this),\\", Claude a \\",(0,a.jsxDEV)(e.strong,{children:\\"creative prose specialist\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:42,columnNumber:46},this),\\", and Gemini a \\",(0,a.jsxDEV)(e.strong,{children:\\"productivity layer\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:42,columnNumber:90},this),\\" for Docs, Sheets, and Gmail. The takeaway: \\",(0,a.jsxDEV)(e.em,{children:\\"choose by task\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:42,columnNumber:156},this),\\", not by raw IQ.\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:42,columnNumber:1},this),(0,a.jsxDEV)(e.blockquote,{children:[`\\n`,(0,a.jsxDEV)(e.h3,{children:\\"\\\\u26A0 Premium trap\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:44,columnNumber:3},this),`\\n`,(0,a.jsxDEV)(e.p,{children:`The $20 Pro tiers are no longer the cutting edge. OpenAI, Anthropic, Perplexity, and xAI now sell $100\\\\u2013$300 \\"Max / Heavy\\" plans aimed at enterprises. For most writers the $20 tier remains the ROI sweet-spot, but know you\'re not buying the absolute top model.`},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:45,columnNumber:3},this),`\\n`]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:44,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:47,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\\"2025 AI-writer scorecard\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:49,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[`| Model | Best for (archetype) | Stand-out feature | Context window | Free tier | Pro price | Key limitation |\\n|-------|----------------------|-------------------|---------------|-----------|-----------|----------------|\\n| `,(0,a.jsxDEV)(e.strong,{children:\\"Claude 3.5 Sonnet\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:53,columnNumber:3},this),` | Creative writing (Poet) | \\"Artifacts\\" live editor | 200 k tokens | Yes (daily cap) | $20 | No native real-time web search |\\n| `,(0,a.jsxDEV)(e.strong,{children:\\"GPT-4o\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:54,columnNumber:3},this),` | Generalist (Polymath) | Multimodal + Custom GPTs | 128 k tokens | Yes (cap) | $20 | Output can feel robotic |\\n| `,(0,a.jsxDEV)(e.strong,{children:\\"Gemini Advanced\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:55,columnNumber:3},this),` | Productivity (Producer) | Deep Workspace integration | 1 M + tokens | Yes (std) | $19.99 | Creative flair weaker than Claude |\\n| `,(0,a.jsxDEV)(e.strong,{children:\\"Perplexity Pro\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:56,columnNumber:3},this),` | Research (Professor) | Clickable citations, Deep Research | \\\\u2014 | Yes (cap) | $20 | Not a creative writer |\\n| `,(0,a.jsxDEV)(e.strong,{children:\\"Grok\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:57,columnNumber:3},this),\\" | Real-time insights (Provocateur) | Live X / Twitter data | \\\\u2014 | Yes (cap) | $30 | Pricey; edgy tone not for all |\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:51,columnNumber:1},this),(0,a.jsxDEV)(\\"div\\",{style:{textAlign:\\"right\\",fontSize:\\"0.9rem\\"},children:(0,a.jsxDEV)(\\"a\\",{href:\\"/export/scorecard.csv\\",children:\\"Export to Sheets \\\\u2192\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:59,columnNumber:52},this)},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:59,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:61,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\\"Speed test \\\\u26A1\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:63,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:(0,a.jsxDEV)(e.em,{children:\\"[Speed comparison GIF placeholder - GPT-4o vs Claude 3.5 vs Gemini]\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:65,columnNumber:1},this)},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:65,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\\"GPT-4o starts generating sooner and streams tokens faster, a win for brainstorms and quick Q & A. Claude is ~2\\\\xD7 faster than its predecessor Opus but still trails GPT-4o on sheer responsiveness.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:67,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:69,columnNumber:1},this),(0,a.jsxDEV)(e.h2,{children:\\"Deep-dive profiles\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:71,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\\"Claude 3.5 Sonnet \\\\u2014 \\",(0,a.jsxDEV)(e.em,{children:\\"the creative wordsmith\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:73,columnNumber:25},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:73,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[(0,a.jsxDEV)(e.strong,{children:\\"Strengths.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:75,columnNumber:1},this),` Thoughtful, expressive prose; 200 k-token context; \\"Artifacts\\" side-panel for iterative editing.\\n`,(0,a.jsxDEV)(e.strong,{children:\\"Weaknesses.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:76,columnNumber:1},this),` No built-in web browsing; free tier message cap.\\n`,(0,a.jsxDEV)(e.em,{children:[\\"Read the full \\",(0,a.jsxDEV)(e.a,{href:\\"/claude-3-5-for-blogging-review\\",children:\\"Claude 3.5 blogging review\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:77,columnNumber:16},this),\\".\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:77,columnNumber:1},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:75,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:79,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\\"GPT-4o \\\\u2014 \\",(0,a.jsxDEV)(e.em,{children:\\"the versatile all-rounder\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:81,columnNumber:14},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:81,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:`Handles writing, code, data and images in one chat. Custom GPTs unlock niche workflows.\\nCriticisms: increasing verbosity, smaller window than Claude, privacy concerns.`},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:83,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:86,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\\"Gemini Advanced \\\\u2014 \\",(0,a.jsxDEV)(e.em,{children:\\"the integrated productivity engine\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:88,columnNumber:23},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:88,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[`Native in Docs, Sheets, Gmail. Perfect for campaign tables, email summarising, Drive file look-ups.\\nDeep dive: `,(0,a.jsxDEV)(e.a,{href:\\"/gemini-advanced-for-marketers-guide\\",children:\\"Gemini for marketers\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:91,columnNumber:12},this),\\".\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:90,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:93,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\\"Perplexity Pro \\\\u2014 \\",(0,a.jsxDEV)(e.em,{children:\\"the research powerhouse\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:95,columnNumber:22},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:95,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:[`Delivers answers with numbered citations; \\"Deep Research\\" builds exhaustive reports.\\nGuide: `,(0,a.jsxDEV)(e.a,{href:\\"/how-to-use-perplexity-for-academic-research\\",children:\\"How to use Perplexity for academic research\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:98,columnNumber:8},this),\\".\\"]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:97,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:100,columnNumber:1},this),(0,a.jsxDEV)(e.h3,{children:[\\"Grok \\\\u2014 \\",(0,a.jsxDEV)(e.em,{children:\\"the real-time provocateur\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:102,columnNumber:12},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:102,columnNumber:1},this),(0,a.jsxDEV)(e.p,{children:\\"Live social-media pulse plus a snarky attitude. Great for trend analysts, overkill for everyday writing.\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:104,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:106,columnNumber:1},this),(0,a.jsxDEV)(Ae,{task:\\"writing\\"},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:108,columnNumber:1},this),(0,a.jsxDEV)(e.hr,{},void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:110,columnNumber:1},this)]},void 0,!0,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\",lineNumber:14,columnNumber:1},this)}function bs(t={}){let{wrapper:e}=t.components||{};return e?(0,a.jsxDEV)(e,Object.assign({},t,{children:(0,a.jsxDEV)(Rn,t,void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\"},this)}),void 0,!1,{fileName:\\"/Users/<USER>/Desktop/Webiste Tool/content/pillars/_mdx_bundler_entry_point-f2d417c4-**************-66525f96bdbe.mdx\\"},this):Rn(t)}var _s=bs;return ti(gs);})();\\n/*! Bundled license information:\\n\\nreact/cjs/react-jsx-dev-runtime.development.js:\\n  (**\\n   * @license React\\n   * react-jsx-dev-runtime.development.js\\n   *\\n   * Copyright (c) Facebook, Inc. and its affiliates.\\n   *\\n   * This source code is licensed under the MIT license found in the\\n   * LICENSE file in the root directory of this source tree.\\n   *)\\n*/\\n;return Component;"},"_id":"pillars/best-ai-for-writing.mdx","_raw":{"sourceFilePath":"pillars/best-ai-for-writing.mdx","sourceFileName":"best-ai-for-writing.mdx","sourceFileDir":"pillars","contentType":"mdx","flattenedPath":"pillars/best-ai-for-writing"},"type":"Pillar","url":"/best-ai-for-writing-2025-claude-3-5-vs-gpt-4o-gemini-more","slugFromPath":"pillars/best-ai-for-writing"}');

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@contentlayer","vendor-chunks/contentlayer"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Bslug%5D%2Fpage&page=%2F%5Bslug%5D%2Fpage&appPaths=%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();