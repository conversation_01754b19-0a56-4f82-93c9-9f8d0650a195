/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/quiz/coding/page";
exports.ids = ["app/quiz/coding/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fcoding%2Fpage&page=%2Fquiz%2Fcoding%2Fpage&appPaths=%2Fquiz%2Fcoding%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fcoding%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fcoding%2Fpage&page=%2Fquiz%2Fcoding%2Fpage&appPaths=%2Fquiz%2Fcoding%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fcoding%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'quiz',\n        {\n        children: [\n        'coding',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/quiz/coding/page.tsx */ \"(rsc)/./app/quiz/coding/page.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/quiz/coding/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/quiz/coding/page\",\n        pathname: \"/quiz/coding\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fcoding%2Fpage&page=%2Fquiz%2Fcoding%2Fpage&appPaths=%2Fquiz%2Fcoding%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fcoding%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fquiz%2Fcoding%2Fpage.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fquiz%2Fcoding%2Fpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/quiz/coding/page.tsx */ \"(ssr)/./app/quiz/coding/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZhcHAlMkZxdWl6JTJGY29kaW5nJTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8/NTdjZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9waGlsbWNrZW56aWUvRGVza3RvcC9XZWJpc3RlIFRvb2wvYXBwL3F1aXovY29kaW5nL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fquiz%2Fcoding%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZhcHAtcm91dGVyLmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZlcnJvci1ib3VuZGFyeS5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGcGhpbG1ja2VuemllJTJGRGVza3RvcCUyRldlYmlzdGUlMjBUb29sJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGcGhpbG1ja2VuemllJTJGRGVza3RvcCUyRldlYmlzdGUlMjBUb29sJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbm90LWZvdW5kLWJvdW5kYXJ5LmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQW9JO0FBQ3BJLDBPQUF3STtBQUN4SSx3T0FBdUk7QUFDdkksa1BBQTRJO0FBQzVJLHNRQUFzSjtBQUN0SiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXRvb2wvPzUzY2UiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcGhpbG1ja2VuemllL0Rlc2t0b3AvV2ViaXN0ZSBUb29sL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYXBwLXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3BoaWxtY2tlbnppZS9EZXNrdG9wL1dlYmlzdGUgVG9vbC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcGhpbG1ja2VuemllL0Rlc2t0b3AvV2ViaXN0ZSBUb29sL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbGF5b3V0LXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3BoaWxtY2tlbnppZS9EZXNrdG9wL1dlYmlzdGUgVG9vbC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3BoaWxtY2tlbnppZS9EZXNrdG9wL1dlYmlzdGUgVG9vbC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9waGlsbWNrZW56aWUvRGVza3RvcC9XZWJpc3RlIFRvb2wvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9zdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!*****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmxpbmsuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8/MGQ2ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9waGlsbWNrZW56aWUvRGVza3RvcC9XZWJpc3RlIFRvb2wvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvbGluay5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/quiz/coding/page.tsx":
/*!**********************************!*\
  !*** ./app/quiz/coding/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CodingQuiz)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _quizConfig__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./quizConfig */ \"(ssr)/./app/quiz/coding/quizConfig.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction CodingQuiz() {\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [answers, setAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const currentQ = _quizConfig__WEBPACK_IMPORTED_MODULE_2__.questions[step];\n    const progress = (step + 1) / _quizConfig__WEBPACK_IMPORTED_MODULE_2__.questions.length * 100;\n    function next(answer) {\n        setIsLoading(true);\n        const newAnswers = {\n            ...answers,\n            [currentQ.id]: answer\n        };\n        // Check override rules\n        const hit = _quizConfig__WEBPACK_IMPORTED_MODULE_2__.overrides.find((ov)=>ov.questionId === currentQ.id && ov.answerKey === answer);\n        if (hit) {\n            router.push(`/recommend/${hit.result}`);\n            return;\n        }\n        setAnswers(newAnswers);\n        if (step + 1 === _quizConfig__WEBPACK_IMPORTED_MODULE_2__.questions.length) {\n            // Calculate score\n            const score = {};\n            Object.values(newAnswers).forEach((key)=>{\n                Object.entries(_quizConfig__WEBPACK_IMPORTED_MODULE_2__.scoreMatrix[key]).forEach(([model, pts])=>{\n                    score[model] = (score[model] || 0) + pts;\n                });\n            });\n            const winner = Object.entries(score).sort((a, b)=>b[1] - a[1])[0][0];\n            router.push(`/recommend/${winner}`);\n        } else {\n            setStep(step + 1);\n            setIsLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-100 py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Find Your Perfect AI Coding Assistant\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Answer 5 quick questions to get a personalized recommendation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between text-sm text-gray-600 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Question \",\n                                        step + 1,\n                                        \" of \",\n                                        _quizConfig__WEBPACK_IMPORTED_MODULE_2__.questions.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        Math.round(progress),\n                                        \"% complete\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-200 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-600 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: `${progress}%`\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-6\",\n                            children: currentQ.text\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: currentQ.answers.map(({ key, label })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>next(key),\n                                    disabled: isLoading,\n                                    className: \"w-full text-left border-2 border-gray-200 rounded-lg py-4 px-6 hover:border-green-500 hover:bg-green-50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6 h-6 border-2 border-gray-300 rounded-full mr-4 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-600 rounded-full opacity-0 group-hover:opacity-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-800 font-medium\",\n                                                children: label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this)\n                                }, key, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center text-green-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-3 h-5 w-5\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Analyzing your development needs...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                step > 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setStep(step - 1),\n                        className: \"text-gray-600 hover:text-gray-800 font-medium\",\n                        children: \"← Back to previous question\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/quiz/coding/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/quiz/coding/quizConfig.ts":
/*!***************************************!*\
  !*** ./app/quiz/coding/quizConfig.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   modelNames: () => (/* binding */ modelNames),\n/* harmony export */   overrides: () => (/* binding */ overrides),\n/* harmony export */   questions: () => (/* binding */ questions),\n/* harmony export */   scoreMatrix: () => (/* binding */ scoreMatrix)\n/* harmony export */ });\n// Coding-specific quiz questions\nconst questions = [\n    {\n        id: \"primary_language\",\n        text: \"What is your primary programming language?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Python (data science, web, automation)\"\n            },\n            {\n                key: \"B\",\n                label: \"JavaScript/TypeScript (web development)\"\n            },\n            {\n                key: \"C\",\n                label: \"Java/C# (enterprise, backend)\"\n            },\n            {\n                key: \"D\",\n                label: \"Multiple languages / polyglot\"\n            }\n        ]\n    },\n    {\n        id: \"project_type\",\n        text: \"What type of projects do you primarily work on?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Large enterprise applications\"\n            },\n            {\n                key: \"B\",\n                label: \"Web apps and APIs\"\n            },\n            {\n                key: \"C\",\n                label: \"Scripts and automation\"\n            },\n            {\n                key: \"D\",\n                label: \"Rapid prototypes and experiments\"\n            }\n        ]\n    },\n    {\n        id: \"team_size\",\n        text: \"What best describes your development environment?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Solo developer / freelancer\"\n            },\n            {\n                key: \"B\",\n                label: \"Small team (2-10 developers)\"\n            },\n            {\n                key: \"C\",\n                label: \"Large enterprise team (10+ developers)\"\n            }\n        ]\n    },\n    {\n        id: \"ide_preference\",\n        text: \"What is your preferred development environment?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"VS Code\"\n            },\n            {\n                key: \"B\",\n                label: \"JetBrains IDEs (IntelliJ, PyCharm, etc.)\"\n            },\n            {\n                key: \"C\",\n                label: \"Browser-based / cloud IDEs\"\n            },\n            {\n                key: \"D\",\n                label: \"Terminal / Vim / Emacs\"\n            }\n        ]\n    },\n    {\n        id: \"codebase_complexity\",\n        text: \"How would you describe your typical codebase?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Simple scripts and small projects\"\n            },\n            {\n                key: \"B\",\n                label: \"Medium-sized applications (1000s of lines)\"\n            },\n            {\n                key: \"C\",\n                label: \"Large, complex codebases (10k+ lines, multiple files)\"\n            }\n        ]\n    }\n];\n// Scoring matrix for coding AI recommendations\nconst scoreMatrix = {\n    A: {\n        gpt: 1,\n        claude: 2,\n        copilot: 1,\n        replit: 0\n    },\n    B: {\n        gpt: 2,\n        claude: 1,\n        copilot: 2,\n        replit: 1\n    },\n    C: {\n        gpt: 1,\n        claude: 1,\n        copilot: 3,\n        replit: 0\n    },\n    D: {\n        gpt: 2,\n        claude: 1,\n        copilot: 1,\n        replit: 2\n    }\n};\n// Override rules for critical requirements\nconst overrides = [\n    {\n        questionId: \"team_size\",\n        answerKey: \"C\",\n        result: \"copilot\"\n    },\n    {\n        questionId: \"ide_preference\",\n        answerKey: \"C\",\n        result: \"replit\"\n    },\n    {\n        questionId: \"codebase_complexity\",\n        answerKey: \"C\",\n        result: \"claude\"\n    }\n];\n// Model display names for coding tools\nconst modelNames = {\n    gpt: \"GPT-4o\",\n    claude: \"Claude 3.5 Sonnet\",\n    copilot: \"GitHub Copilot\",\n    replit: \"Replit Ghostwriter\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/quiz/coding/quizConfig.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"92c3bf8256c7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS10b29sLy4vYXBwL2dsb2JhbHMuY3NzP2U3YmYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5MmMzYmY4MjU2YzdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"AI Recommender\",\n    description: \"Find the best AI tools for your needs\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"AI Recommender\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"container mx-auto px-4 py-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-2xl mx-auto text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-4\",\n                children: \"404 - Page Not Found\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-gray-600 mb-8\",\n                children: \"The page you're looking for doesn't exist.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/\",\n                className: \"inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                children: \"Go Home\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEI7QUFFYixTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUEwQjs7Ozs7OzBCQUN4Qyw4REFBQ0U7Z0JBQUVGLFdBQVU7MEJBQTZCOzs7Ozs7MEJBRzFDLDhEQUFDSCxrREFBSUE7Z0JBQ0hNLE1BQUs7Z0JBQ0xILFdBQVU7MEJBQ1g7Ozs7Ozs7Ozs7OztBQUtQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8uL2FwcC9ub3QtZm91bmQudHN4PzVjODAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RGb3VuZCgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTJ4bCBteC1hdXRvIHRleHQtY2VudGVyXCI+XG4gICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIG1iLTRcIj40MDQgLSBQYWdlIE5vdCBGb3VuZDwvaDE+XG4gICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS02MDAgbWItOFwiPlxuICAgICAgICBUaGUgcGFnZSB5b3UncmUgbG9va2luZyBmb3IgZG9lc24ndCBleGlzdC5cbiAgICAgIDwvcD5cbiAgICAgIDxMaW5rIFxuICAgICAgICBocmVmPVwiL1wiXG4gICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1ibG9jayBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHB4LTYgcHktMyByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtNzAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgID5cbiAgICAgICAgR28gSG9tZVxuICAgICAgPC9MaW5rPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiTGluayIsIk5vdEZvdW5kIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwiaHJlZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./app/quiz/coding/page.tsx":
/*!**********************************!*\
  !*** ./app/quiz/coding/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/Webiste Tool/app/quiz/coding/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fcoding%2Fpage&page=%2Fquiz%2Fcoding%2Fpage&appPaths=%2Fquiz%2Fcoding%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fcoding%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();