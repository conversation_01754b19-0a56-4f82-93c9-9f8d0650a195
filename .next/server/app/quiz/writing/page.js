/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/quiz/writing/page";
exports.ids = ["app/quiz/writing/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fwriting%2Fpage&page=%2Fquiz%2Fwriting%2Fpage&appPaths=%2Fquiz%2Fwriting%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fwriting%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fwriting%2Fpage&page=%2Fquiz%2Fwriting%2Fpage&appPaths=%2Fquiz%2Fwriting%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fwriting%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'quiz',\n        {\n        children: [\n        'writing',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/quiz/writing/page.tsx */ \"(rsc)/./app/quiz/writing/page.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/quiz/writing/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/quiz/writing/page\",\n        pathname: \"/quiz/writing\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZxdWl6JTJGd3JpdGluZyUyRnBhZ2UmcGFnZT0lMkZxdWl6JTJGd3JpdGluZyUyRnBhZ2UmYXBwUGF0aHM9JTJGcXVpeiUyRndyaXRpbmclMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcXVpeiUyRndyaXRpbmclMkZwYWdlLnRzeCZhcHBEaXI9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZhcHAmcGFnZUV4dGVuc2lvbnM9anMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz1tZCZwYWdlRXh0ZW5zaW9ucz1tZHgmcm9vdERpcj0lMkZVc2VycyUyRnBoaWxtY2tlbnppZSUyRkRlc2t0b3AlMkZXZWJpc3RlJTIwVG9vbCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFvRztBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsdUJBQXVCLGtLQUF1RztBQUM5SDtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EseUJBQXlCLDRJQUE0RjtBQUNySCxvQkFBb0Isa0pBQStGO0FBQ25IO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8/MDlhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAncXVpeicsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ3dyaXRpbmcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcGhpbG1ja2VuemllL0Rlc2t0b3AvV2ViaXN0ZSBUb29sL2FwcC9xdWl6L3dyaXRpbmcvcGFnZS50c3hcIiksIFwiL1VzZXJzL3BoaWxtY2tlbnppZS9EZXNrdG9wL1dlYmlzdGUgVG9vbC9hcHAvcXVpei93cml0aW5nL3BhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9waGlsbWNrZW56aWUvRGVza3RvcC9XZWJpc3RlIFRvb2wvYXBwL2xheW91dC50c3hcIiksIFwiL1VzZXJzL3BoaWxtY2tlbnppZS9EZXNrdG9wL1dlYmlzdGUgVG9vbC9hcHAvbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcGhpbG1ja2VuemllL0Rlc2t0b3AvV2ViaXN0ZSBUb29sL2FwcC9ub3QtZm91bmQudHN4XCIpLCBcIi9Vc2Vycy9waGlsbWNrZW56aWUvRGVza3RvcC9XZWJpc3RlIFRvb2wvYXBwL25vdC1mb3VuZC50c3hcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiL1VzZXJzL3BoaWxtY2tlbnppZS9EZXNrdG9wL1dlYmlzdGUgVG9vbC9hcHAvcXVpei93cml0aW5nL3BhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvcXVpei93cml0aW5nL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvcXVpei93cml0aW5nL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL3F1aXovd3JpdGluZ1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fwriting%2Fpage&page=%2Fquiz%2Fwriting%2Fpage&appPaths=%2Fquiz%2Fwriting%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fwriting%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fquiz%2Fwriting%2Fpage.tsx&server=true!":
/*!***************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fquiz%2Fwriting%2Fpage.tsx&server=true! ***!
  \***************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/quiz/writing/page.tsx */ \"(ssr)/./app/quiz/writing/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZhcHAlMkZxdWl6JTJGd3JpdGluZyUyRnBhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXRvb2wvPzhiYzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcGhpbG1ja2VuemllL0Rlc2t0b3AvV2ViaXN0ZSBUb29sL2FwcC9xdWl6L3dyaXRpbmcvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fquiz%2Fwriting%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!*****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmxpbmsuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8/MGQ2ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9waGlsbWNrZW56aWUvRGVza3RvcC9XZWJpc3RlIFRvb2wvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvbGluay5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/quiz/writing/page.tsx":
/*!***********************************!*\
  !*** ./app/quiz/writing/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WritingQuiz)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _quizConfig__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./quizConfig */ \"(ssr)/./app/quiz/writing/quizConfig.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction WritingQuiz() {\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [answers, setAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const currentQ = _quizConfig__WEBPACK_IMPORTED_MODULE_2__.questions[step];\n    const progress = (step + 1) / _quizConfig__WEBPACK_IMPORTED_MODULE_2__.questions.length * 100;\n    function next(answer) {\n        setIsLoading(true);\n        const newAnswers = {\n            ...answers,\n            [currentQ.id]: answer\n        };\n        // Check override\n        const hit = _quizConfig__WEBPACK_IMPORTED_MODULE_2__.overrides.find((ov)=>ov.questionId === currentQ.id && ov.answerKey === answer);\n        if (hit) {\n            router.push(`/recommend/${hit.result}`);\n            return;\n        }\n        setAnswers(newAnswers);\n        if (step + 1 === _quizConfig__WEBPACK_IMPORTED_MODULE_2__.questions.length) {\n            // Calculate score\n            const score = {};\n            Object.values(newAnswers).forEach((key)=>{\n                Object.entries(_quizConfig__WEBPACK_IMPORTED_MODULE_2__.scoreMatrix[key]).forEach(([model, pts])=>{\n                    score[model] = (score[model] || 0) + pts;\n                });\n            });\n            const winner = Object.entries(score).sort((a, b)=>b[1] - a[1])[0][0];\n            router.push(`/recommend/${winner}`);\n        } else {\n            setStep(step + 1);\n            setIsLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Find Your Perfect AI Writing Assistant\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Answer 5 quick questions to get a personalized recommendation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between text-sm text-gray-600 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Question \",\n                                        step + 1,\n                                        \" of \",\n                                        _quizConfig__WEBPACK_IMPORTED_MODULE_2__.questions.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        Math.round(progress),\n                                        \"% complete\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-200 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: `${progress}%`\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-6\",\n                            children: currentQ.text\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: currentQ.answers.map(({ key, label })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>next(key),\n                                    disabled: isLoading,\n                                    className: \"w-full text-left border-2 border-gray-200 rounded-lg py-4 px-6 hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6 h-6 border-2 border-gray-300 rounded-full mr-4 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-600 rounded-full opacity-0 group-hover:opacity-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-800 font-medium\",\n                                                children: label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this)\n                                }, key, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center text-blue-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-3 h-5 w-5\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Analyzing your preferences...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                step > 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setStep(step - 1),\n                        className: \"text-gray-600 hover:text-gray-800 font-medium\",\n                        children: \"← Back to previous question\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/quiz/writing/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/quiz/writing/quizConfig.ts":
/*!****************************************!*\
  !*** ./app/quiz/writing/quizConfig.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   modelNames: () => (/* binding */ modelNames),\n/* harmony export */   overrides: () => (/* binding */ overrides),\n/* harmony export */   questions: () => (/* binding */ questions),\n/* harmony export */   scoreMatrix: () => (/* binding */ scoreMatrix)\n/* harmony export */ });\n// 1 ⬇ Questions\nconst questions = [\n    {\n        id: \"objective\",\n        text: \"What is your primary writing task today?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Blog / long-form article\"\n            },\n            {\n                key: \"B\",\n                label: \"Academic / research paper\"\n            },\n            {\n                key: \"C\",\n                label: \"Social-media copy\"\n            },\n            {\n                key: \"D\",\n                label: \"Email / outreach\"\n            }\n        ]\n    },\n    {\n        id: \"importance_citations\",\n        text: \"How important are verifiable sources & citations?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Absolutely essential\"\n            },\n            {\n                key: \"B\",\n                label: \"Nice to have\"\n            },\n            {\n                key: \"C\",\n                label: \"Not important\"\n            }\n        ]\n    },\n    {\n        id: \"writing_style\",\n        text: \"Which best describes your ideal writing style?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Human-like, nuanced, creative\"\n            },\n            {\n                key: \"B\",\n                label: \"Factual, direct, academic\"\n            },\n            {\n                key: \"C\",\n                label: \"Clear, concise, professional\"\n            }\n        ]\n    },\n    {\n        id: \"budget\",\n        text: \"Budget?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Free only\"\n            },\n            {\n                key: \"B\",\n                label: \"≈ $20/mo is fine\"\n            }\n        ]\n    },\n    {\n        id: \"need_integration\",\n        text: \"Do you need direct integration with Google Docs / Gmail?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Yes\"\n            },\n            {\n                key: \"B\",\n                label: \"No\"\n            }\n        ]\n    }\n];\n// 2 ⬇ Scoring matrix\nconst scoreMatrix = {\n    A: {\n        claude: 2,\n        perplexity: 1,\n        gemini: 0,\n        gpt: 1,\n        grok: 0\n    },\n    B: {\n        claude: 0,\n        perplexity: 3,\n        gemini: 1,\n        gpt: 1,\n        grok: 0\n    },\n    C: {\n        claude: 1,\n        perplexity: 0,\n        gemini: 2,\n        gpt: 1,\n        grok: 0\n    },\n    D: {\n        claude: 0,\n        perplexity: 0,\n        gemini: 1,\n        gpt: 2,\n        grok: 0\n    }\n};\n// Optional: hard rules override – e.g., if citations absolutely essential:\nconst overrides = [\n    {\n        questionId: \"importance_citations\",\n        answerKey: \"A\",\n        result: \"perplexity\"\n    },\n    {\n        questionId: \"need_integration\",\n        answerKey: \"A\",\n        result: \"gemini\"\n    }\n];\n// Model display names for results\nconst modelNames = {\n    claude: \"Claude 3.5 Sonnet\",\n    gpt: \"GPT-4o\",\n    gemini: \"Gemini Advanced\",\n    perplexity: \"Perplexity Pro\",\n    grok: \"Grok\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/quiz/writing/quizConfig.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"92c3bf8256c7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS10b29sLy4vYXBwL2dsb2JhbHMuY3NzP2U3YmYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5MmMzYmY4MjU2YzdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"AI Recommender\",\n    description: \"Find the best AI tools for your needs\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"AI Recommender\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"container mx-auto px-4 py-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-2xl mx-auto text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-4\",\n                children: \"404 - Page Not Found\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-gray-600 mb-8\",\n                children: \"The page you're looking for doesn't exist.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/\",\n                className: \"inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                children: \"Go Home\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEI7QUFFYixTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUEwQjs7Ozs7OzBCQUN4Qyw4REFBQ0U7Z0JBQUVGLFdBQVU7MEJBQTZCOzs7Ozs7MEJBRzFDLDhEQUFDSCxrREFBSUE7Z0JBQ0hNLE1BQUs7Z0JBQ0xILFdBQVU7MEJBQ1g7Ozs7Ozs7Ozs7OztBQUtQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8uL2FwcC9ub3QtZm91bmQudHN4PzVjODAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RGb3VuZCgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTJ4bCBteC1hdXRvIHRleHQtY2VudGVyXCI+XG4gICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIG1iLTRcIj40MDQgLSBQYWdlIE5vdCBGb3VuZDwvaDE+XG4gICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS02MDAgbWItOFwiPlxuICAgICAgICBUaGUgcGFnZSB5b3UncmUgbG9va2luZyBmb3IgZG9lc24ndCBleGlzdC5cbiAgICAgIDwvcD5cbiAgICAgIDxMaW5rIFxuICAgICAgICBocmVmPVwiL1wiXG4gICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1ibG9jayBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHB4LTYgcHktMyByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtNzAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgID5cbiAgICAgICAgR28gSG9tZVxuICAgICAgPC9MaW5rPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiTGluayIsIk5vdEZvdW5kIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwiaHJlZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./app/quiz/writing/page.tsx":
/*!***********************************!*\
  !*** ./app/quiz/writing/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/Webiste Tool/app/quiz/writing/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fwriting%2Fpage&page=%2Fquiz%2Fwriting%2Fpage&appPaths=%2Fquiz%2Fwriting%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fwriting%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();