/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/quiz/video/page";
exports.ids = ["app/quiz/video/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fvideo%2Fpage&page=%2Fquiz%2Fvideo%2Fpage&appPaths=%2Fquiz%2Fvideo%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fvideo%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fvideo%2Fpage&page=%2Fquiz%2Fvideo%2Fpage&appPaths=%2Fquiz%2Fvideo%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fvideo%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'quiz',\n        {\n        children: [\n        'video',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/quiz/video/page.tsx */ \"(rsc)/./app/quiz/video/page.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/quiz/video/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/quiz/video/page\",\n        pathname: \"/quiz/video\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fvideo%2Fpage&page=%2Fquiz%2Fvideo%2Fpage&appPaths=%2Fquiz%2Fvideo%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fvideo%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fquiz%2Fvideo%2Fpage.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fquiz%2Fvideo%2Fpage.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/quiz/video/page.tsx */ \"(ssr)/./app/quiz/video/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZhcHAlMkZxdWl6JTJGdmlkZW8lMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS10b29sLz9lYTJlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3BoaWxtY2tlbnppZS9EZXNrdG9wL1dlYmlzdGUgVG9vbC9hcHAvcXVpei92aWRlby9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fquiz%2Fvideo%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZhcHAtcm91dGVyLmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZlcnJvci1ib3VuZGFyeS5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGcGhpbG1ja2VuemllJTJGRGVza3RvcCUyRldlYmlzdGUlMjBUb29sJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGcGhpbG1ja2VuemllJTJGRGVza3RvcCUyRldlYmlzdGUlMjBUb29sJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbm90LWZvdW5kLWJvdW5kYXJ5LmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQW9JO0FBQ3BJLDBPQUF3STtBQUN4SSx3T0FBdUk7QUFDdkksa1BBQTRJO0FBQzVJLHNRQUFzSjtBQUN0SiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXRvb2wvPzUzY2UiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcGhpbG1ja2VuemllL0Rlc2t0b3AvV2ViaXN0ZSBUb29sL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYXBwLXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3BoaWxtY2tlbnppZS9EZXNrdG9wL1dlYmlzdGUgVG9vbC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcGhpbG1ja2VuemllL0Rlc2t0b3AvV2ViaXN0ZSBUb29sL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbGF5b3V0LXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3BoaWxtY2tlbnppZS9EZXNrdG9wL1dlYmlzdGUgVG9vbC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3BoaWxtY2tlbnppZS9EZXNrdG9wL1dlYmlzdGUgVG9vbC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9waGlsbWNrZW56aWUvRGVza3RvcC9XZWJpc3RlIFRvb2wvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9zdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!*****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmxpbmsuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8/MGQ2ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9waGlsbWNrZW56aWUvRGVza3RvcC9XZWJpc3RlIFRvb2wvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvbGluay5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/quiz/video/page.tsx":
/*!*********************************!*\
  !*** ./app/quiz/video/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VideoQuizPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _quizConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./quizConfig */ \"(ssr)/./app/quiz/video/quizConfig.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction VideoQuizPage() {\n    const [currentQuestion, setCurrentQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [answers, setAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isComplete, setIsComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleAnswer = (answerKey)=>{\n        const questionId = _quizConfig__WEBPACK_IMPORTED_MODULE_3__.questions[currentQuestion].id;\n        const newAnswers = {\n            ...answers,\n            [questionId]: answerKey\n        };\n        setAnswers(newAnswers);\n        if (currentQuestion < _quizConfig__WEBPACK_IMPORTED_MODULE_3__.questions.length - 1) {\n            setCurrentQuestion(currentQuestion + 1);\n        } else {\n            // Quiz complete, calculate result\n            const result = calculateResult(newAnswers);\n            router.push(`/recommend/${result}`);\n        }\n    };\n    const calculateResult = (userAnswers)=>{\n        // Check for override conditions first\n        for (const override of _quizConfig__WEBPACK_IMPORTED_MODULE_3__.overrides){\n            if (userAnswers[override.questionId] === override.answerKey) {\n                return override.result;\n            }\n        }\n        // Calculate scores for each model\n        const scores = {\n            capcut: 0,\n            veed: 0,\n            runway: 0,\n            pika: 0,\n            descript: 0,\n            captions: 0\n        };\n        Object.entries(userAnswers).forEach(([questionId, answerKey])=>{\n            const questionScores = _quizConfig__WEBPACK_IMPORTED_MODULE_3__.scoreMatrix[answerKey];\n            Object.entries(questionScores).forEach(([model, points])=>{\n                scores[model] += points;\n            });\n        });\n        // Return the model with the highest score\n        return Object.entries(scores).reduce((a, b)=>scores[a[0]] > scores[b[0]] ? a : b)[0];\n    };\n    const goBack = ()=>{\n        if (currentQuestion > 0) {\n            setCurrentQuestion(currentQuestion - 1);\n        }\n    };\n    const progress = (currentQuestion + 1) / _quizConfig__WEBPACK_IMPORTED_MODULE_3__.questions.length * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto px-4 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold mb-4 bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent\",\n                            children: \"AI Video Generator Quiz\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Find your perfect AI video creation tool in 5 questions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between text-sm text-gray-500 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Question \",\n                                        currentQuestion + 1,\n                                        \" of \",\n                                        _quizConfig__WEBPACK_IMPORTED_MODULE_3__.questions.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        Math.round(progress),\n                                        \"% complete\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-200 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-red-500 to-orange-500 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: `${progress}%`\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-lg p-8 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-6 text-gray-900\",\n                            children: _quizConfig__WEBPACK_IMPORTED_MODULE_3__.questions[currentQuestion].text\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: _quizConfig__WEBPACK_IMPORTED_MODULE_3__.questions[currentQuestion].answers.map((answer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleAnswer(answer.key),\n                                    className: \"w-full text-left p-4 rounded-lg border-2 border-gray-200 hover:border-red-300 hover:bg-red-50 transition-all duration-200 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-full border-2 border-gray-300 group-hover:border-red-500 flex items-center justify-center mr-4 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-500 group-hover:text-red-600\",\n                                                    children: answer.key\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700 group-hover:text-gray-900 font-medium\",\n                                                children: answer.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this)\n                                }, answer.key, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: goBack,\n                            disabled: currentQuestion === 0,\n                            className: `px-6 py-3 rounded-lg font-medium transition-colors ${currentQuestion === 0 ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-gray-200 text-gray-700 hover:bg-gray-300\"}`,\n                            children: \"← Previous\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 flex items-center\",\n                            children: \"Click an answer to continue\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-12 text-sm text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"This quiz analyzes your content type, output frequency, collaboration needs, and technical skills to recommend the best AI video tool for your workflow.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/quiz/video/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/quiz/video/quizConfig.ts":
/*!**************************************!*\
  !*** ./app/quiz/video/quizConfig.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   modelNames: () => (/* binding */ modelNames),\n/* harmony export */   overrides: () => (/* binding */ overrides),\n/* harmony export */   questions: () => (/* binding */ questions),\n/* harmony export */   scoreMatrix: () => (/* binding */ scoreMatrix)\n/* harmony export */ });\n// Video generation quiz questions\nconst questions = [\n    {\n        id: \"content_type\",\n        text: \"What type of video content do you primarily create?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Social media content (TikTok, Instagram Reels, YouTube Shorts)\"\n            },\n            {\n                key: \"B\",\n                label: \"Professional/corporate videos (marketing, training, presentations)\"\n            },\n            {\n                key: \"C\",\n                label: \"Creative/artistic projects (films, music videos, experiments)\"\n            },\n            {\n                key: \"D\",\n                label: \"Educational content (tutorials, podcasts, webinars)\"\n            }\n        ]\n    },\n    {\n        id: \"output_frequency\",\n        text: \"How often do you need to create video content?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Daily or multiple times per day (high-volume social media)\"\n            },\n            {\n                key: \"B\",\n                label: \"Weekly (regular content schedule)\"\n            },\n            {\n                key: \"C\",\n                label: \"Monthly or project-based (quality over quantity)\"\n            },\n            {\n                key: \"D\",\n                label: \"Occasionally (as needed for specific campaigns)\"\n            }\n        ]\n    },\n    {\n        id: \"collaboration_needs\",\n        text: \"Do you work with a team on video projects?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Yes, I need strong collaboration and brand consistency tools\"\n            },\n            {\n                key: \"B\",\n                label: \"Sometimes, basic sharing and feedback features are helpful\"\n            },\n            {\n                key: \"C\",\n                label: \"No, I work solo and prefer simple, fast tools\"\n            }\n        ]\n    },\n    {\n        id: \"technical_skill\",\n        text: \"What is your video editing experience level?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Beginner - I want automated, template-based solutions\"\n            },\n            {\n                key: \"B\",\n                label: \"Intermediate - I can handle some complexity for better results\"\n            },\n            {\n                key: \"C\",\n                label: \"Advanced - I want maximum control and professional features\"\n            }\n        ]\n    },\n    {\n        id: \"budget_constraints\",\n        text: \"What is your budget for AI video tools?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Free or very low cost (under $15/month)\"\n            },\n            {\n                key: \"B\",\n                label: \"Moderate budget ($15-40/month)\"\n            },\n            {\n                key: \"C\",\n                label: \"Professional budget ($40+ or pay-per-use)\"\n            }\n        ]\n    }\n];\n// Scoring matrix for video generation AI recommendations\nconst scoreMatrix = {\n    A: {\n        capcut: 2,\n        veed: 1,\n        runway: 0,\n        pika: 1,\n        descript: 0,\n        captions: 2\n    },\n    B: {\n        capcut: 1,\n        veed: 2,\n        runway: 1,\n        pika: 0,\n        descript: 2,\n        captions: 1\n    },\n    C: {\n        capcut: 0,\n        veed: 1,\n        runway: 2,\n        pika: 2,\n        descript: 1,\n        captions: 0\n    },\n    D: {\n        capcut: 0,\n        veed: 1,\n        runway: 0,\n        pika: 0,\n        descript: 2,\n        captions: 1\n    }\n};\n// Override rules for critical requirements\nconst overrides = [\n    {\n        questionId: \"collaboration_needs\",\n        answerKey: \"A\",\n        result: \"veed\"\n    },\n    {\n        questionId: \"content_type\",\n        answerKey: \"D\",\n        result: \"descript\"\n    },\n    {\n        questionId: \"output_frequency\",\n        answerKey: \"A\",\n        result: \"capcut\"\n    },\n    {\n        questionId: \"technical_skill\",\n        answerKey: \"C\",\n        result: \"runway\"\n    }\n];\n// Model display names for video generation tools\nconst modelNames = {\n    capcut: \"CapCut\",\n    veed: \"Veed.io\",\n    runway: \"Runway Gen-3\",\n    pika: \"Pika\",\n    descript: \"Descript\",\n    captions: \"Captions.ai\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/quiz/video/quizConfig.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"92c3bf8256c7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS10b29sLy4vYXBwL2dsb2JhbHMuY3NzP2U3YmYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5MmMzYmY4MjU2YzdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"AI Recommender\",\n    description: \"Find the best AI tools for your needs\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"AI Recommender\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"container mx-auto px-4 py-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-2xl mx-auto text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-4\",\n                children: \"404 - Page Not Found\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-gray-600 mb-8\",\n                children: \"The page you're looking for doesn't exist.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/\",\n                className: \"inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                children: \"Go Home\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEI7QUFFYixTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUEwQjs7Ozs7OzBCQUN4Qyw4REFBQ0U7Z0JBQUVGLFdBQVU7MEJBQTZCOzs7Ozs7MEJBRzFDLDhEQUFDSCxrREFBSUE7Z0JBQ0hNLE1BQUs7Z0JBQ0xILFdBQVU7MEJBQ1g7Ozs7Ozs7Ozs7OztBQUtQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8uL2FwcC9ub3QtZm91bmQudHN4PzVjODAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RGb3VuZCgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTJ4bCBteC1hdXRvIHRleHQtY2VudGVyXCI+XG4gICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIG1iLTRcIj40MDQgLSBQYWdlIE5vdCBGb3VuZDwvaDE+XG4gICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS02MDAgbWItOFwiPlxuICAgICAgICBUaGUgcGFnZSB5b3UncmUgbG9va2luZyBmb3IgZG9lc24ndCBleGlzdC5cbiAgICAgIDwvcD5cbiAgICAgIDxMaW5rIFxuICAgICAgICBocmVmPVwiL1wiXG4gICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1ibG9jayBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHB4LTYgcHktMyByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtNzAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgID5cbiAgICAgICAgR28gSG9tZVxuICAgICAgPC9MaW5rPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiTGluayIsIk5vdEZvdW5kIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwiaHJlZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./app/quiz/video/page.tsx":
/*!*********************************!*\
  !*** ./app/quiz/video/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/Webiste Tool/app/quiz/video/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fvideo%2Fpage&page=%2Fquiz%2Fvideo%2Fpage&appPaths=%2Fquiz%2Fvideo%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fvideo%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();