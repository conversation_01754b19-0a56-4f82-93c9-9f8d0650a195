/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/quiz/image/page";
exports.ids = ["app/quiz/image/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fimage%2Fpage&page=%2Fquiz%2Fimage%2Fpage&appPaths=%2Fquiz%2Fimage%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fimage%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fimage%2Fpage&page=%2Fquiz%2Fimage%2Fpage&appPaths=%2Fquiz%2Fimage%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fimage%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'quiz',\n        {\n        children: [\n        'image',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/quiz/image/page.tsx */ \"(rsc)/./app/quiz/image/page.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/quiz/image/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/quiz/image/page\",\n        pathname: \"/quiz/image\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fimage%2Fpage&page=%2Fquiz%2Fimage%2Fpage&appPaths=%2Fquiz%2Fimage%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fimage%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fquiz%2Fimage%2Fpage.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fquiz%2Fimage%2Fpage.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/quiz/image/page.tsx */ \"(ssr)/./app/quiz/image/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZhcHAlMkZxdWl6JTJGaW1hZ2UlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS10b29sLz84NDk1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3BoaWxtY2tlbnppZS9EZXNrdG9wL1dlYmlzdGUgVG9vbC9hcHAvcXVpei9pbWFnZS9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fquiz%2Fimage%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZhcHAtcm91dGVyLmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZlcnJvci1ib3VuZGFyeS5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGcGhpbG1ja2VuemllJTJGRGVza3RvcCUyRldlYmlzdGUlMjBUb29sJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGcGhpbG1ja2VuemllJTJGRGVza3RvcCUyRldlYmlzdGUlMjBUb29sJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbm90LWZvdW5kLWJvdW5kYXJ5LmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQW9JO0FBQ3BJLDBPQUF3STtBQUN4SSx3T0FBdUk7QUFDdkksa1BBQTRJO0FBQzVJLHNRQUFzSjtBQUN0SiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXRvb2wvPzUzY2UiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcGhpbG1ja2VuemllL0Rlc2t0b3AvV2ViaXN0ZSBUb29sL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYXBwLXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3BoaWxtY2tlbnppZS9EZXNrdG9wL1dlYmlzdGUgVG9vbC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcGhpbG1ja2VuemllL0Rlc2t0b3AvV2ViaXN0ZSBUb29sL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbGF5b3V0LXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3BoaWxtY2tlbnppZS9EZXNrdG9wL1dlYmlzdGUgVG9vbC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3BoaWxtY2tlbnppZS9EZXNrdG9wL1dlYmlzdGUgVG9vbC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9waGlsbWNrZW56aWUvRGVza3RvcC9XZWJpc3RlIFRvb2wvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9zdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!*****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmxpbmsuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8/MGQ2ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9waGlsbWNrZW56aWUvRGVza3RvcC9XZWJpc3RlIFRvb2wvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvbGluay5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/quiz/image/page.tsx":
/*!*********************************!*\
  !*** ./app/quiz/image/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageQuizPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _quizConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./quizConfig */ \"(ssr)/./app/quiz/image/quizConfig.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ImageQuizPage() {\n    const [currentQuestion, setCurrentQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [answers, setAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isComplete, setIsComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleAnswer = (answerKey)=>{\n        const questionId = _quizConfig__WEBPACK_IMPORTED_MODULE_3__.questions[currentQuestion].id;\n        const newAnswers = {\n            ...answers,\n            [questionId]: answerKey\n        };\n        setAnswers(newAnswers);\n        if (currentQuestion < _quizConfig__WEBPACK_IMPORTED_MODULE_3__.questions.length - 1) {\n            setCurrentQuestion(currentQuestion + 1);\n        } else {\n            // Quiz complete, calculate result\n            const result = calculateResult(newAnswers);\n            router.push(`/recommend/${result}`);\n        }\n    };\n    const calculateResult = (userAnswers)=>{\n        // Check for override conditions first\n        for (const override of _quizConfig__WEBPACK_IMPORTED_MODULE_3__.overrides){\n            if (userAnswers[override.questionId] === override.answerKey) {\n                return override.result;\n            }\n        }\n        // Calculate scores for each model\n        const scores = {\n            midjourney: 0,\n            stable_diffusion: 0,\n            ideogram: 0,\n            dalle: 0\n        };\n        Object.entries(userAnswers).forEach(([questionId, answerKey])=>{\n            const questionScores = _quizConfig__WEBPACK_IMPORTED_MODULE_3__.scoreMatrix[answerKey];\n            Object.entries(questionScores).forEach(([model, points])=>{\n                scores[model] += points;\n            });\n        });\n        // Return the model with the highest score\n        return Object.entries(scores).reduce((a, b)=>scores[a[0]] > scores[b[0]] ? a : b)[0];\n    };\n    const goBack = ()=>{\n        if (currentQuestion > 0) {\n            setCurrentQuestion(currentQuestion - 1);\n        }\n    };\n    const progress = (currentQuestion + 1) / _quizConfig__WEBPACK_IMPORTED_MODULE_3__.questions.length * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto px-4 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold mb-4 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent\",\n                            children: \"AI Image Generator Quiz\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Find your perfect AI image generation tool in 5 questions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between text-sm text-gray-500 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Question \",\n                                        currentQuestion + 1,\n                                        \" of \",\n                                        _quizConfig__WEBPACK_IMPORTED_MODULE_3__.questions.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        Math.round(progress),\n                                        \"% complete\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-200 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    width: `${progress}%`\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-lg p-8 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-6 text-gray-900\",\n                            children: _quizConfig__WEBPACK_IMPORTED_MODULE_3__.questions[currentQuestion].text\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: _quizConfig__WEBPACK_IMPORTED_MODULE_3__.questions[currentQuestion].answers.map((answer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleAnswer(answer.key),\n                                    className: \"w-full text-left p-4 rounded-lg border-2 border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-full border-2 border-gray-300 group-hover:border-purple-500 flex items-center justify-center mr-4 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-500 group-hover:text-purple-600\",\n                                                    children: answer.key\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700 group-hover:text-gray-900 font-medium\",\n                                                children: answer.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, this)\n                                }, answer.key, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: goBack,\n                            disabled: currentQuestion === 0,\n                            className: `px-6 py-3 rounded-lg font-medium transition-colors ${currentQuestion === 0 ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-gray-200 text-gray-700 hover:bg-gray-300\"}`,\n                            children: \"← Previous\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 flex items-center\",\n                            children: \"Click an answer to continue\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-12 text-sm text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"This quiz analyzes your needs across aesthetic quality, technical control, typography requirements, and budget to recommend the best AI image generator for you.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/quiz/image/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/quiz/image/quizConfig.ts":
/*!**************************************!*\
  !*** ./app/quiz/image/quizConfig.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   modelNames: () => (/* binding */ modelNames),\n/* harmony export */   overrides: () => (/* binding */ overrides),\n/* harmony export */   questions: () => (/* binding */ questions),\n/* harmony export */   scoreMatrix: () => (/* binding */ scoreMatrix)\n/* harmony export */ });\n// Image generation quiz questions\nconst questions = [\n    {\n        id: \"use_case\",\n        text: \"What is your primary use case for AI image generation?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Game development (concept art, textures, characters)\"\n            },\n            {\n                key: \"B\",\n                label: \"Digital art and illustration\"\n            },\n            {\n                key: \"C\",\n                label: \"Marketing and social media content\"\n            },\n            {\n                key: \"D\",\n                label: \"General creative projects and experimentation\"\n            }\n        ]\n    },\n    {\n        id: \"text_requirements\",\n        text: \"How important is accurate text rendering in your images?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Essential - I need logos, signs, and readable text\"\n            },\n            {\n                key: \"B\",\n                label: \"Occasionally useful for certain projects\"\n            },\n            {\n                key: \"C\",\n                label: \"Not important - I rarely need text in images\"\n            }\n        ]\n    },\n    {\n        id: \"technical_skill\",\n        text: \"What is your technical comfort level?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Beginner - I want simple, user-friendly tools\"\n            },\n            {\n                key: \"B\",\n                label: \"Intermediate - I can handle some technical setup\"\n            },\n            {\n                key: \"C\",\n                label: \"Advanced - I want maximum control and customization\"\n            }\n        ]\n    },\n    {\n        id: \"budget_constraints\",\n        text: \"What is your budget for AI image generation?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Free or very low cost (under $10/month)\"\n            },\n            {\n                key: \"B\",\n                label: \"Moderate budget ($10-30/month)\"\n            },\n            {\n                key: \"C\",\n                label: \"Professional budget ($30+ or pay-per-use)\"\n            }\n        ]\n    },\n    {\n        id: \"commercial_needs\",\n        text: \"Do you need to use generated images commercially?\",\n        answers: [\n            {\n                key: \"A\",\n                label: \"Yes, for business/client work with high revenue\"\n            },\n            {\n                key: \"B\",\n                label: \"Yes, for small business or personal projects\"\n            },\n            {\n                key: \"C\",\n                label: \"No, just for personal use and experimentation\"\n            }\n        ]\n    }\n];\n// Scoring matrix for image generation AI recommendations\nconst scoreMatrix = {\n    A: {\n        midjourney: 2,\n        stable_diffusion: 1,\n        ideogram: 1,\n        dalle: 1\n    },\n    B: {\n        midjourney: 1,\n        stable_diffusion: 1,\n        ideogram: 1,\n        dalle: 2\n    },\n    C: {\n        midjourney: 1,\n        stable_diffusion: 2,\n        ideogram: 2,\n        dalle: 1\n    },\n    D: {\n        midjourney: 1,\n        stable_diffusion: 0,\n        ideogram: 0,\n        dalle: 2\n    }\n};\n// Override rules for critical requirements\nconst overrides = [\n    {\n        questionId: \"text_requirements\",\n        answerKey: \"A\",\n        result: \"ideogram\"\n    },\n    {\n        questionId: \"budget_constraints\",\n        answerKey: \"A\",\n        result: \"stable_diffusion\"\n    },\n    {\n        questionId: \"technical_skill\",\n        answerKey: \"C\",\n        result: \"stable_diffusion\"\n    }\n];\n// Model display names for image generation tools\nconst modelNames = {\n    midjourney: \"Midjourney\",\n    stable_diffusion: \"Stable Diffusion\",\n    ideogram: \"Ideogram\",\n    dalle: \"DALL-E\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/quiz/image/quizConfig.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"92c3bf8256c7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS10b29sLy4vYXBwL2dsb2JhbHMuY3NzP2U3YmYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5MmMzYmY4MjU2YzdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"AI Recommender\",\n    description: \"Find the best AI tools for your needs\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"AI Recommender\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"container mx-auto px-4 py-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-2xl mx-auto text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-4\",\n                children: \"404 - Page Not Found\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-gray-600 mb-8\",\n                children: \"The page you're looking for doesn't exist.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/\",\n                className: \"inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                children: \"Go Home\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEI7QUFFYixTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUEwQjs7Ozs7OzBCQUN4Qyw4REFBQ0U7Z0JBQUVGLFdBQVU7MEJBQTZCOzs7Ozs7MEJBRzFDLDhEQUFDSCxrREFBSUE7Z0JBQ0hNLE1BQUs7Z0JBQ0xILFdBQVU7MEJBQ1g7Ozs7Ozs7Ozs7OztBQUtQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8uL2FwcC9ub3QtZm91bmQudHN4PzVjODAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RGb3VuZCgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTJ4bCBteC1hdXRvIHRleHQtY2VudGVyXCI+XG4gICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIG1iLTRcIj40MDQgLSBQYWdlIE5vdCBGb3VuZDwvaDE+XG4gICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS02MDAgbWItOFwiPlxuICAgICAgICBUaGUgcGFnZSB5b3UncmUgbG9va2luZyBmb3IgZG9lc24ndCBleGlzdC5cbiAgICAgIDwvcD5cbiAgICAgIDxMaW5rIFxuICAgICAgICBocmVmPVwiL1wiXG4gICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1ibG9jayBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHB4LTYgcHktMyByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtNzAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgID5cbiAgICAgICAgR28gSG9tZVxuICAgICAgPC9MaW5rPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiTGluayIsIk5vdEZvdW5kIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwiaHJlZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./app/quiz/image/page.tsx":
/*!*********************************!*\
  !*** ./app/quiz/image/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/Webiste Tool/app/quiz/image/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquiz%2Fimage%2Fpage&page=%2Fquiz%2Fimage%2Fpage&appPaths=%2Fquiz%2Fimage%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fimage%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();