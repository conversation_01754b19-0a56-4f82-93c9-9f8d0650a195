/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/recommend/[model]/page";
exports.ids = ["app/recommend/[model]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Frecommend%2F%5Bmodel%5D%2Fpage&page=%2Frecommend%2F%5Bmodel%5D%2Fpage&appPaths=%2Frecommend%2F%5Bmodel%5D%2Fpage&pagePath=private-next-app-dir%2Frecommend%2F%5Bmodel%5D%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Frecommend%2F%5Bmodel%5D%2Fpage&page=%2Frecommend%2F%5Bmodel%5D%2Fpage&appPaths=%2Frecommend%2F%5Bmodel%5D%2Fpage&pagePath=private-next-app-dir%2Frecommend%2F%5Bmodel%5D%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'recommend',\n        {\n        children: [\n        '[model]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/recommend/[model]/page.tsx */ \"(rsc)/./app/recommend/[model]/page.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/recommend/[model]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/recommend/[model]/page\",\n        pathname: \"/recommend/[model]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Frecommend%2F%5Bmodel%5D%2Fpage&page=%2Frecommend%2F%5Bmodel%5D%2Fpage&appPaths=%2Frecommend%2F%5Bmodel%5D%2Fpage&pagePath=private-next-app-dir%2Frecommend%2F%5Bmodel%5D%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fshared%2Flib%2Flazy-dynamic%2Fdynamic-no-ssr.js&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fshared%2Flib%2Flazy-dynamic%2Fdynamic-no-ssr.js&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-no-ssr.js */ \"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-no-ssr.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmxpbmsuanMmbW9kdWxlcz0lMkZVc2VycyUyRnBoaWxtY2tlbnppZSUyRkRlc2t0b3AlMkZXZWJpc3RlJTIwVG9vbCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGc2hhcmVkJTJGbGliJTJGbGF6eS1keW5hbWljJTJGZHluYW1pYy1uby1zc3IuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdNQUFtSDtBQUNuSCIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXRvb2wvP2ZiYzAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcGhpbG1ja2VuemllL0Rlc2t0b3AvV2ViaXN0ZSBUb29sL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpbmsuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9waGlsbWNrZW56aWUvRGVza3RvcC9XZWJpc3RlIFRvb2wvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL2xhenktZHluYW1pYy9keW5hbWljLW5vLXNzci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fshared%2Flib%2Flazy-dynamic%2Fdynamic-no-ssr.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!*****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwaGlsbWNrZW56aWUlMkZEZXNrdG9wJTJGV2ViaXN0ZSUyMFRvb2wlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmxpbmsuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8/MGQ2ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9waGlsbWNrZW56aWUvRGVza3RvcC9XZWJpc3RlIFRvb2wvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvbGluay5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp%2Fglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"92c3bf8256c7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS10b29sLy4vYXBwL2dsb2JhbHMuY3NzP2U3YmYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5MmMzYmY4MjU2YzdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"AI Recommender\",\n    description: \"Find the best AI tools for your needs\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"AI Recommender\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"container mx-auto px-4 py-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-2xl mx-auto text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-4\",\n                children: \"404 - Page Not Found\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-gray-600 mb-8\",\n                children: \"The page you're looking for doesn't exist.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/\",\n                className: \"inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                children: \"Go Home\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/not-found.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEI7QUFFYixTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUEwQjs7Ozs7OzBCQUN4Qyw4REFBQ0U7Z0JBQUVGLFdBQVU7MEJBQTZCOzs7Ozs7MEJBRzFDLDhEQUFDSCxrREFBSUE7Z0JBQ0hNLE1BQUs7Z0JBQ0xILFdBQVU7MEJBQ1g7Ozs7Ozs7Ozs7OztBQUtQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktdG9vbC8uL2FwcC9ub3QtZm91bmQudHN4PzVjODAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RGb3VuZCgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTJ4bCBteC1hdXRvIHRleHQtY2VudGVyXCI+XG4gICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIG1iLTRcIj40MDQgLSBQYWdlIE5vdCBGb3VuZDwvaDE+XG4gICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS02MDAgbWItOFwiPlxuICAgICAgICBUaGUgcGFnZSB5b3UncmUgbG9va2luZyBmb3IgZG9lc24ndCBleGlzdC5cbiAgICAgIDwvcD5cbiAgICAgIDxMaW5rIFxuICAgICAgICBocmVmPVwiL1wiXG4gICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1ibG9jayBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHB4LTYgcHktMyByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtNzAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgID5cbiAgICAgICAgR28gSG9tZVxuICAgICAgPC9MaW5rPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiTGluayIsIk5vdEZvdW5kIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwiaHJlZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./app/recommend/[model]/page.tsx":
/*!****************************************!*\
  !*** ./app/recommend/[model]/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RecommendPage),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Model configurations\nconst modelConfigs = {\n    claude: {\n        name: \"Claude 3.5 Sonnet\",\n        tagline: \"The Creative Wordsmith\",\n        description: \"Perfect for bloggers and creative writers who need human-like, nuanced prose with a huge context window.\",\n        strengths: [\n            \"Thoughtful, expressive writing style\",\n            \"200k token context window\",\n            '\"Artifacts\" live editor for iterative editing',\n            \"Excellent for long-form content\"\n        ],\n        weaknesses: [\n            \"No built-in web browsing\",\n            \"Free tier has daily message caps\"\n        ],\n        pricing: \"$20/month (Pro)\",\n        freeOption: \"Yes (with daily limits)\",\n        bestFor: \"Blog posts, creative writing, long-form articles\",\n        ctaUrl: \"https://claude.ai\",\n        ctaText: \"Try Claude 3.5 Sonnet\",\n        color: \"orange\"\n    },\n    gpt: {\n        name: \"GPT-4o\",\n        tagline: \"The Versatile All-Rounder\",\n        description: \"The most versatile AI that handles writing, code, data, and images in one chat. Custom GPTs unlock specialized workflows.\",\n        strengths: [\n            \"Multimodal capabilities (text, images, code)\",\n            \"Custom GPTs for specialized tasks\",\n            \"Fast response times\",\n            \"Broad knowledge base\"\n        ],\n        weaknesses: [\n            \"Can feel robotic at times\",\n            \"Smaller context window than Claude\",\n            \"Privacy concerns for some users\"\n        ],\n        pricing: \"$20/month (Plus)\",\n        freeOption: \"Yes (with usage limits)\",\n        bestFor: \"General writing, technical content, mixed-format projects\",\n        ctaUrl: \"https://chat.openai.com\",\n        ctaText: \"Try GPT-4o\",\n        color: \"green\"\n    },\n    gemini: {\n        name: \"Gemini Advanced\",\n        tagline: \"The Integrated Productivity Engine\",\n        description: \"Native integration with Google Workspace makes it perfect for marketers who need seamless workflow integration.\",\n        strengths: [\n            \"Deep Google Workspace integration\",\n            \"Massive 1M+ token context window\",\n            \"Perfect for campaign planning\",\n            \"Email and document analysis\"\n        ],\n        weaknesses: [\n            \"Creative flair weaker than Claude\",\n            \"Less specialized for pure writing\"\n        ],\n        pricing: \"$19.99/month (Advanced)\",\n        freeOption: \"Yes (standard version)\",\n        bestFor: \"Marketing campaigns, Google Workspace users, data analysis\",\n        ctaUrl: \"https://gemini.google.com\",\n        ctaText: \"Try Gemini Advanced\",\n        color: \"blue\"\n    },\n    perplexity: {\n        name: \"Perplexity Pro\",\n        tagline: \"The Research Powerhouse\",\n        description: \"The ultimate research assistant that delivers answers with clickable citations and builds comprehensive reports.\",\n        strengths: [\n            \"Clickable citations and sources\",\n            \"Deep Research feature\",\n            \"Real-time web access\",\n            \"Academic-grade accuracy\"\n        ],\n        weaknesses: [\n            \"Not optimized for creative writing\",\n            \"Limited style customization\"\n        ],\n        pricing: \"$20/month (Pro)\",\n        freeOption: \"Yes (with search limits)\",\n        bestFor: \"Research papers, fact-checking, academic writing\",\n        ctaUrl: \"https://perplexity.ai\",\n        ctaText: \"Try Perplexity Pro\",\n        color: \"purple\"\n    },\n    grok: {\n        name: \"Grok\",\n        tagline: \"The Real-Time Provocateur\",\n        description: \"Access to live X/Twitter data with a snarky attitude. Great for trend analysis and real-time insights.\",\n        strengths: [\n            \"Live social media data access\",\n            \"Real-time trend analysis\",\n            \"Unique personality and tone\",\n            \"Current events awareness\"\n        ],\n        weaknesses: [\n            \"More expensive than alternatives\",\n            \"Edgy tone not suitable for all contexts\",\n            \"Limited creative writing capabilities\"\n        ],\n        pricing: \"$30/month (Premium)\",\n        freeOption: \"Yes (with limits)\",\n        bestFor: \"Social media analysis, trend research, current events\",\n        ctaUrl: \"https://x.ai\",\n        ctaText: \"Try Grok\",\n        color: \"gray\"\n    },\n    // Image Generation Tools\n    midjourney: {\n        name: \"Midjourney\",\n        tagline: \"The Aesthetic Perfectionist\",\n        description: \"Unmatched photorealistic and artistic image generation with cinematic quality and art-directed aesthetics.\",\n        strengths: [\n            \"Unparalleled photorealism and artistic quality\",\n            \"Intuitive Discord-based interface\",\n            \"Strong community and prompt sharing\",\n            \"Consistent high-quality output\",\n            \"Advanced style parameters and controls\"\n        ],\n        weaknesses: [\n            \"Poor text rendering capabilities\",\n            \"Requires Discord for access\",\n            \"Limited fine-tuning control\",\n            \"Higher cost for commercial use\"\n        ],\n        pricing: \"$30/month (Standard), $60/month (Pro)\",\n        freeOption: \"No (trial available)\",\n        bestFor: \"Concept art, marketing hero images, artistic illustrations\",\n        ctaUrl: \"https://midjourney.com\",\n        ctaText: \"Join Midjourney\",\n        color: \"purple\"\n    },\n    stable_diffusion: {\n        name: \"Stable Diffusion\",\n        tagline: \"The Technical Powerhouse\",\n        description: \"Open-source flexibility with maximum control, ControlNet precision, and the ability to run locally.\",\n        strengths: [\n            \"Open-source and free to use\",\n            \"ControlNet for precise image control\",\n            \"Can run locally on your hardware\",\n            \"Extensive model ecosystem and fine-tuning\",\n            \"Complete customization and privacy\"\n        ],\n        weaknesses: [\n            \"Steep learning curve\",\n            \"Requires technical setup\",\n            \"Inconsistent quality without expertise\",\n            \"Hardware requirements for local use\"\n        ],\n        pricing: \"Free (self-hosted) or API costs (~$0.002/image)\",\n        freeOption: \"Yes (fully open-source)\",\n        bestFor: \"Bulk generation, custom training, technical workflows\",\n        ctaUrl: \"https://stability.ai\",\n        ctaText: \"Get Stable Diffusion\",\n        color: \"blue\"\n    },\n    ideogram: {\n        name: \"Ideogram\",\n        tagline: \"The Typography Specialist\",\n        description: \"Exceptional text rendering and typography with clean, modern aesthetics perfect for logos and graphics.\",\n        strengths: [\n            \"Exceptional text rendering and typography\",\n            \"Clean, modern aesthetic output\",\n            \"Affordable pricing structure\",\n            \"Good prompt adherence\",\n            \"User-friendly interface\"\n        ],\n        weaknesses: [\n            \"Less photorealistic than competitors\",\n            \"Smaller community and resources\",\n            \"Newer platform with fewer features\",\n            \"Free plan images are public\"\n        ],\n        pricing: \"$16/month (Plus), $48/month (Pro)\",\n        freeOption: \"Yes (with public images)\",\n        bestFor: \"Logos, social media graphics, text-heavy designs\",\n        ctaUrl: \"https://ideogram.ai\",\n        ctaText: \"Try Ideogram\",\n        color: \"green\"\n    },\n    dalle: {\n        name: \"DALL-E\",\n        tagline: \"The Conversational Creator\",\n        description: \"Superior prompt understanding with ChatGPT integration, perfect for rapid ideation and general-purpose generation.\",\n        strengths: [\n            \"Superior prompt comprehension\",\n            \"Seamless ChatGPT integration\",\n            \"Consistent quality output\",\n            \"Strong ethical safeguards\",\n            \"Easy conversational interface\"\n        ],\n        weaknesses: [\n            \"More expensive than alternatives\",\n            \"Less artistic flair than Midjourney\",\n            \"API-dependent for advanced use\",\n            \"Strict content policies\"\n        ],\n        pricing: \"$0.04 per image via API\",\n        freeOption: \"Limited (via ChatGPT)\",\n        bestFor: \"Rapid ideation, conversational workflows, general-purpose generation\",\n        ctaUrl: \"https://openai.com/dall-e-3\",\n        ctaText: \"Try DALL-E\",\n        color: \"emerald\"\n    },\n    // Video Generation Tools\n    capcut: {\n        name: \"CapCut\",\n        tagline: \"The Mobile-First Velocity Engine\",\n        description: \"The creator's flywheel for high-volume social media content with no watermark on free tier and seamless platform integration.\",\n        strengths: [\n            \"No watermark on free tier\",\n            \"Extensive template library\",\n            \"Seamless social media integration\",\n            \"Mobile-optimized workflow\",\n            \"Auto-captions and AI features\"\n        ],\n        weaknesses: [\n            \"Limited collaboration features\",\n            \"Primarily mobile-focused\",\n            \"Fewer advanced AI features\",\n            \"Less suitable for professional projects\"\n        ],\n        pricing: \"Free tier available, Pro $9.99/month\",\n        freeOption: \"Yes (no watermark)\",\n        bestFor: \"Solo creators, TikTok/Instagram content, high-volume social media\",\n        ctaUrl: \"https://www.capcut.com\",\n        ctaText: \"Try CapCut\",\n        color: \"red\"\n    },\n    veed: {\n        name: \"Veed.io\",\n        tagline: \"The Collaborative Marketing Platform\",\n        description: \"Built-in Brand Kit and shared workspaces make it the marketer's choice for team-based video creation.\",\n        strengths: [\n            \"Team collaboration features\",\n            \"Brand consistency tools\",\n            \"Web-based workflow\",\n            \"Extensive format support\",\n            \"Professional templates\"\n        ],\n        weaknesses: [\n            \"Watermark on free tier\",\n            \"Higher pricing for teams\",\n            \"Less advanced AI than specialized tools\",\n            \"Can be complex for beginners\"\n        ],\n        pricing: \"Free tier with watermark, Pro $24/month\",\n        freeOption: \"Yes (with watermark)\",\n        bestFor: \"Marketing teams, brand content, collaborative projects, corporate communications\",\n        ctaUrl: \"https://www.veed.io\",\n        ctaText: \"Try Veed.io\",\n        color: \"orange\"\n    },\n    runway: {\n        name: \"Runway Gen-3\",\n        tagline: \"The Cinematic Realism Leader\",\n        description: \"Industry-leading video quality with advanced camera controls and professional-grade output for filmmakers.\",\n        strengths: [\n            \"Industry-leading video quality\",\n            \"Advanced camera controls\",\n            \"Professional-grade output\",\n            \"Extensive customization options\",\n            \"Cinematic realism\"\n        ],\n        weaknesses: [\n            \"Higher cost per generation\",\n            \"Longer processing times\",\n            \"Resolution limitations (1280\\xd7768)\",\n            \"Learning curve for beginners\"\n        ],\n        pricing: \"$15/month for 625 credits\",\n        freeOption: \"Limited trial credits\",\n        bestFor: \"Film production, commercials, high-end marketing content, creative agencies\",\n        ctaUrl: \"https://runwayml.com\",\n        ctaText: \"Try Runway\",\n        color: \"indigo\"\n    },\n    pika: {\n        name: \"Pika\",\n        tagline: \"The Creative Experimenter's Playground\",\n        description: \"Fast generation times with unique creative features perfect for social media content and artistic experiments.\",\n        strengths: [\n            \"Fast generation times (30-60 seconds)\",\n            \"Unique creative features (Modify Region, Lip Sync)\",\n            \"Affordable credit-based pricing\",\n            \"Innovative editing tools\",\n            \"Great for experimentation\"\n        ],\n        weaknesses: [\n            \"Resolution limited to 1080p\",\n            \"Credit-based pricing can add up\",\n            \"Less photorealistic than Runway\",\n            \"Smaller community and resources\"\n        ],\n        pricing: \"$10/month for 700 credits\",\n        freeOption: \"Limited free credits\",\n        bestFor: \"Creative experiments, social media content, rapid prototyping, artistic projects\",\n        ctaUrl: \"https://pika.art\",\n        ctaText: \"Try Pika\",\n        color: \"pink\"\n    },\n    descript: {\n        name: \"Descript\",\n        tagline: \"The Text-Based Editing Revolution\",\n        description: \"Edit video by editing the transcript. Revolutionary approach perfect for podcasts, interviews, and educational content.\",\n        strengths: [\n            \"Revolutionary transcript-based editing\",\n            \"Excellent audio AI features\",\n            \"Voice cloning capabilities (Overdub)\",\n            \"Intuitive workflow for content creators\",\n            \"Filler word removal and Studio Sound\"\n        ],\n        weaknesses: [\n            \"Learning curve for traditional editors\",\n            \"Limited video effects and transitions\",\n            \"Primarily suited for talking-head content\",\n            \"Higher pricing for advanced features\"\n        ],\n        pricing: \"Free tier available, Creator $12/month\",\n        freeOption: \"Yes (with limitations)\",\n        bestFor: \"Podcasts, interviews, educational content, webinars, voice-over work\",\n        ctaUrl: \"https://www.descript.com\",\n        ctaText: \"Try Descript\",\n        color: \"teal\"\n    },\n    captions: {\n        name: \"Captions.ai\",\n        tagline: \"The Social Engagement Optimizer\",\n        description: \"Animated, mobile-style captions with AI dubbing in 29+ languages. Unlimited free exports with no watermark.\",\n        strengths: [\n            \"Unlimited free exports (no watermark)\",\n            \"Excellent subtitle animations\",\n            \"Multi-language AI dubbing (29+ languages)\",\n            \"Eye contact correction\",\n            \"AI script generation\"\n        ],\n        weaknesses: [\n            \"Limited editing features beyond captions\",\n            \"Focused primarily on subtitle creation\",\n            \"Fewer creative tools than competitors\",\n            \"Less suitable for complex video projects\"\n        ],\n        pricing: \"Free tier with unlimited exports, Pro $20/month\",\n        freeOption: \"Yes (unlimited, no watermark)\",\n        bestFor: \"Social media optimization, international content, accessibility compliance, engagement boosting\",\n        ctaUrl: \"https://www.captions.ai\",\n        ctaText: \"Try Captions.ai\",\n        color: \"yellow\"\n    },\n    copilot: {\n        name: \"GitHub Copilot\",\n        tagline: \"The Workflow Integrator\",\n        description: \"Seamlessly integrated AI coding assistant that understands your GitHub workflow, repositories, and development context.\",\n        strengths: [\n            \"Deep VS Code and JetBrains integration\",\n            \"Understands Git context and history\",\n            \"PR and issue integration\",\n            \"Enterprise-grade security and compliance\",\n            \"Supports 30+ programming languages\"\n        ],\n        weaknesses: [\n            \"Limited to GitHub ecosystem\",\n            \"Enterprise pricing can be steep\",\n            \"Requires GitHub account and repositories\"\n        ],\n        pricing: \"$19/month (Business) / $39/month (Enterprise)\",\n        freeOption: \"Yes (for students and open-source)\",\n        bestFor: \"GitHub-based teams, VS Code users, enterprise development\",\n        ctaUrl: \"https://github.com/features/copilot\",\n        ctaText: \"Try GitHub Copilot\",\n        color: \"indigo\"\n    },\n    replit: {\n        name: \"Replit Ghostwriter\",\n        tagline: \"The Rapid Prototyper\",\n        description: \"AI coding assistant built into the Replit cloud IDE for instant development, deployment, and collaboration.\",\n        strengths: [\n            \"Instant deployment and hosting\",\n            \"Browser-based development environment\",\n            \"Great for learning and experimentation\",\n            \"Real-time collaboration features\",\n            \"No local setup required\"\n        ],\n        weaknesses: [\n            \"Limited to Replit environment\",\n            \"Less suitable for complex enterprise projects\",\n            \"Smaller context window than dedicated models\"\n        ],\n        pricing: \"$20/month (Pro) / $50/month (Teams)\",\n        freeOption: \"Yes (with usage limits)\",\n        bestFor: \"Rapid prototyping, educational projects, web development\",\n        ctaUrl: \"https://replit.com/ai\",\n        ctaText: \"Try Replit Ghostwriter\",\n        color: \"teal\"\n    }\n};\nasync function generateStaticParams() {\n    return Object.keys(modelConfigs).map((model)=>({\n            model\n        }));\n}\nfunction RecommendPage({ params }) {\n    const config = modelConfigs[params.model];\n    if (!config) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    const colorClasses = {\n        orange: \"from-orange-500 to-red-500\",\n        green: \"from-green-500 to-emerald-500\",\n        blue: \"from-blue-500 to-indigo-500\",\n        purple: \"from-purple-500 to-pink-500\",\n        gray: \"from-gray-500 to-slate-500\",\n        indigo: \"from-indigo-500 to-purple-500\",\n        teal: \"from-teal-500 to-cyan-500\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${colorClasses[config.color]} text-white text-2xl font-bold mb-4`,\n                                children: config.name.charAt(0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: [\n                                \"Perfect Match: \",\n                                config.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 mb-2\",\n                            children: config.tagline\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-700 max-w-2xl mx-auto\",\n                            children: config.description\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-8 mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-gray-900 mb-4 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-500 mr-2\",\n                                            children: \"✓\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Strengths\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: config.strengths.map((strength, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500 mr-2 mt-1\",\n                                                    children: \"•\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-700\",\n                                                    children: strength\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-gray-900 mb-4 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-500 mr-2\",\n                                            children: \"⚠\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Considerations\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: config.weaknesses.map((weakness, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-500 mr-2 mt-1\",\n                                                    children: \"•\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-700\",\n                                                    children: weakness\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                    lineNumber: 452,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-8 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-3 gap-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: config.pricing\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"Free Option\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-700\",\n                                        children: config.freeOption\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"Best For\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-700\",\n                                        children: config.bestFor\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: config.ctaUrl,\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: `inline-block bg-gradient-to-r ${colorClasses[config.color]} text-white font-bold py-4 px-8 rounded-lg text-lg hover:shadow-lg transition-all duration-200 transform hover:scale-105`,\n                            children: [\n                                config.ctaText,\n                                \" →\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mt-2\",\n                            children: \"Start with the free version, upgrade when ready\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                    lineNumber: 505,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/best-ai-for-writing-2025-claude-3-5-vs-gpt-4o-gemini-more\",\n                                className: \"text-blue-600 hover:text-blue-800 font-medium mr-6\",\n                                children: \"← AI Writing Guide\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/best-ai-for-coding-2025-gpt-4o-vs-claude-github-copilot-more\",\n                                className: \"text-green-600 hover:text-green-800 font-medium\",\n                                children: \"← AI Coding Guide\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n                    lineNumber: 520,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n            lineNumber: 434,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Webiste Tool/app/recommend/[model]/page.tsx\",\n        lineNumber: 433,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/recommend/[model]/page.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Frecommend%2F%5Bmodel%5D%2Fpage&page=%2Frecommend%2F%5Bmodel%5D%2Fpage&appPaths=%2Frecommend%2F%5Bmodel%5D%2Fpage&pagePath=private-next-app-dir%2Frecommend%2F%5Bmodel%5D%2Fpage.tsx&appDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fphilmckenzie%2FDesktop%2FWebiste%20Tool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();