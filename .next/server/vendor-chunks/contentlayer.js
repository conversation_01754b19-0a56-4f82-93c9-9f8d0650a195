"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/contentlayer";
exports.ids = ["vendor-chunks/contentlayer"];
exports.modules = {

/***/ "(rsc)/./node_modules/contentlayer/dist/client/index.js":
/*!********************************************************!*\
  !*** ./node_modules/contentlayer/dist/client/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   guards: () => (/* reexport safe */ _contentlayer_client__WEBPACK_IMPORTED_MODULE_0__.guards),\n/* harmony export */   isType: () => (/* reexport safe */ _contentlayer_client__WEBPACK_IMPORTED_MODULE_0__.isType),\n/* harmony export */   pick: () => (/* reexport safe */ _contentlayer_client__WEBPACK_IMPORTED_MODULE_0__.pick)\n/* harmony export */ });\n/* harmony import */ var _contentlayer_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @contentlayer/client */ \"(rsc)/./node_modules/@contentlayer/client/dist/index.js\");\n //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvY29udGVudGxheWVyL2Rpc3QvY2xpZW50L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBcUMsQ0FDckMsaUNBQWlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2ViaXN0ZS10b29sLy4vbm9kZV9tb2R1bGVzL2NvbnRlbnRsYXllci9kaXN0L2NsaWVudC9pbmRleC5qcz84NDUwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJ0Bjb250ZW50bGF5ZXIvY2xpZW50Jztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/contentlayer/dist/client/index.js\n");

/***/ })

};
;