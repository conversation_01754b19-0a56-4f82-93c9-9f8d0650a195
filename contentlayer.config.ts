import { defineDocumentType, makeSource } from 'contentlayer/source-files'
import rehypeHighlight from 'rehype-highlight'

export const Pillar = defineDocumentType(() => ({
  name: 'Pillar',
  filePathPattern: `**/*.mdx`,
  contentType: 'mdx',
  fields: {
    title: {
      type: 'string',
      description: 'The title of the page',
      required: true,
    },
    description: {
      type: 'string',
      description: 'The description of the page',
      required: true,
    },
    slug: {
      type: 'string',
      description: 'The slug for the page URL',
      required: true,
    },
    cluster: {
      type: 'string',
      description: 'The parent cluster ID',
      required: true,
    },
    template: {
      type: 'string',
      description: 'The template to use for rendering',
      required: true,
    },
    priority: {
      type: 'enum',
      options: ['High', 'Medium', 'Low'],
      description: 'The priority level',
      required: false,
    },
    lastUpdated: {
      type: 'string',
      description: 'Last updated date',
      required: false,
    },
  },
  computedFields: {
    url: {
      type: 'string',
      resolve: (doc) => `/${doc.slug}`,
    },
    slugFromPath: {
      type: 'string',
      resolve: (doc) => doc._raw.flattenedPath,
    },
  },
}))

export default makeSource({
  contentDirPath: './content',
  documentTypes: [Pillar],
  mdx: {
    remarkPlugins: [],
    rehypePlugins: [rehypeHighlight],
  },
})
