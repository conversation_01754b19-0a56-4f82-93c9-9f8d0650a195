# Website Tool - AI Recommender Static Site!

A Next.js 13 static site generator for AI tool recommendations, built with MDX and Contentlayer.

## 🚀 Features

- **Static Site Generation (SSG)** - Pre-rendered pages for optimal performance
- **MDX Content Management** - Write content in Markdown with React components
- **Dynamic Routing** - Automatic page generation from content files
- **Taxonomy Integration** - Front-matter matches YAML taxonomy structure
- **Tailwind CSS** - Utility-first styling
- **TypeScript** - Type-safe development

## 📁 Project Structure

```
├── app/                    # Next.js App Router
│   ├── [slug]/            # Dynamic content pages
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── content/               # MDX content files
│   └── pillars/          # Pillar content pages
├── data/                 # Data files
│   └── taxonomy.yaml     # Content taxonomy
├── components/           # React components
├── templates/            # Content templates
└── scripts/              # Utility scripts
```

## 🛠️ Development

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

```bash
# Clone the repository
git clone https://github.com/PudgyPengu/ai-tool.git
cd ai-tool

# Install dependencies
npm install

# Start development server
npm run dev
```

### Building for Production

```bash
# Build static site
npm run build

# Start production server
npm start
```

## 📝 Content Management

### Adding New Content

1. Create a new `.mdx` file in the appropriate content directory
2. Add front-matter matching the taxonomy structure:

```yaml
---
title: Your Page Title
description: Page description
slug: your-page-slug
cluster: relevant-cluster-id
template: pillar
priority: Low|Medium|High
---
```

3. Write your content in MDX format
4. Build the site to generate static pages

### Content Structure

- **Front-matter**: YAML metadata matching `taxonomy.yaml`
- **Body**: MDX content with React components support
- **Templates**: Reusable layout components

## 🔧 Configuration

- `contentlayer.config.ts` - Content processing configuration
- `next.config.js` - Next.js configuration
- `tailwind.config.js` - Tailwind CSS configuration

## 📊 Build Output

The build generates static HTML + JSON for all content pages:

```
Route (app)                              Size     First Load JS
┌ ○ /                                    179 B          87.7 kB
└ ● /[slug]                              2.76 kB        83.3 kB
    └ /best-ai-for-writing
```

- `○` Static pages
- `●` SSG (Static Site Generation)

## 🚀 Deployment

The site generates static files that can be deployed to any static hosting service:

- Vercel
- Netlify  
- GitHub Pages
- AWS S3 + CloudFront

## 📄 License

This project is licensed under the ISC License.

---

## 🎯 **NEW: Intelligent Quiz System**

The site now features an interactive quiz that recommends the perfect AI writing tool based on user needs:
- **Smart Questionnaire**: 5 strategic questions about writing tasks, style, budget, and requirements
- **Personalized Recommendations**: Algorithm matches users to Claude, GPT-4o, Gemini, Perplexity, or Grok
- **Beautiful UI**: Progressive interface with loading states and smooth transitions
- **Result Pages**: Detailed comparisons with strengths, weaknesses, and direct CTAs

Try it at: `/quiz/writing`

---
*Last updated: 2025-07-21*
