/**
 * Add support for MDX (JSX: `<Video id={123} />`, export/imports: `export {x}
 * from 'y'`; and expressions: `{1 + 1}`).
 *
 * @this {Processor}
 *   Processor.
 * @param {Readonly<Options> | null | undefined} [options]
 *   Configuration (optional).
 * @returns {undefined}
 *   Nothing.
 */
export default function remarkMdx(this: Processor<undefined, undefined, undefined, undefined, undefined>, options?: Readonly<Options> | null | undefined): undefined;
/**
 * Configuration.
 */
export type Options = MicromarkOptions & ToMarkdownOptions;
import type { Processor } from 'unified';
import type { Options as MicromarkOptions } from 'micromark-extension-mdxjs';
import type { ToMarkdownOptions } from 'mdast-util-mdx';
//# sourceMappingURL=index.d.ts.map