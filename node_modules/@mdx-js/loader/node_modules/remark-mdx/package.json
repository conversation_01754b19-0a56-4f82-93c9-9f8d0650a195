{"name": "remark-mdx", "version": "3.1.0", "description": "remark plugin to support MDX syntax", "license": "MIT", "keywords": ["javascript", "jsx", "markdown", "mdast", "mdx", "plugin", "remark", "remark-plugin", "unified", "xml"], "homepage": "https://mdxjs.com", "repository": {"type": "git", "url": "https://github.com/mdx-js/mdx", "directory": "packages/remark-mdx/"}, "bugs": "https://github.com/mdx-js/mdx/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON> <<EMAIL>>"], "type": "module", "sideEffects": false, "exports": "./index.js", "files": ["lib/", "index.d.ts.map", "index.d.ts", "index.js"], "dependencies": {"mdast-util-mdx": "^3.0.0", "micromark-extension-mdxjs": "^3.0.0"}, "devDependencies": {}, "scripts": {"test": "npm run test-coverage", "test-api": "node --conditions development test/index.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api"}, "xo": {"prettier": true, "rules": {"logical-assignment-operators": "off", "n/file-extension-in-import": "off", "unicorn/no-this-assignment": "off"}}}