/**
 * @param {ImageReference} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
export function imageReference(node: ImageReference, _: Parents | undefined, state: State, info: Info): string;
export namespace imageReference {
    export { imageReferencePeek as peek };
}
import type { ImageReference } from 'mdast';
import type { Parents } from 'mdast';
import type { State } from 'mdast-util-to-markdown';
import type { Info } from 'mdast-util-to-markdown';
/**
 * @returns {string}
 */
declare function imageReferencePeek(): string;
export {};
//# sourceMappingURL=image-reference.d.ts.map