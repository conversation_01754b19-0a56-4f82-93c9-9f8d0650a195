/**
 * Turn an mdast syntax tree into markdown.
 *
 * @param {Nodes} tree
 *   Tree to serialize.
 * @param {Options | null | undefined} [options]
 *   Configuration (optional).
 * @returns {string}
 *   Serialized markdown representing `tree`.
 */
export function toMarkdown(tree: Nodes, options?: Options | null | undefined): string;
import type { Nodes } from 'mdast';
import type { Options } from 'mdast-util-to-markdown';
//# sourceMappingURL=index.d.ts.map