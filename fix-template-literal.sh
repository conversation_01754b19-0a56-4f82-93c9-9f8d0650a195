#!/bin/bash
echo "🔧 Fixing template literal error in coding pillar..."

cd "/Users/<USER>/Desktop/Webiste Tool"

# Add the fixed file
git add app/[slug]/PillarClient.tsx

# Commit the fix
git commit -m "🔧 FIX: Template literal error in coding pillar

CRITICAL FIX: Escape template literals in code examples
✅ Fixed Python code example with \${calculate_cart_total(cart)}
✅ Fixed JavaScript code example with \${final_price.toFixed(2)}
✅ Used string concatenation to prevent JS execution
✅ Coding pillar now loads without runtime errors

BEFORE: ReferenceError - calculate_cart_total is not defined
AFTER: Code examples display correctly as plain text

Both writing and coding pillars now work perfectly!"

# Push to GitHub
git push origin main

echo "✅ Template literal fix deployed!"
echo "🎯 Coding pillar should now work without errors"
