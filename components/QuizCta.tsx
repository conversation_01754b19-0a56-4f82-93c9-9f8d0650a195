interface QuizCtaProps {
  task: string;
}

export default function QuizCta({ task }: QuizCtaProps) {
  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 my-8">
      <div className="text-center">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Not sure which AI fits your workflow?
        </h3>
        <p className="text-gray-600 mb-4">
          Take our 30-second quiz to get a personalized recommendation
        </p>
        <button className="bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors">
          Take the {task.charAt(0).toUpperCase() + task.slice(1)} Quiz →
        </button>
      </div>
    </div>
  );
}
