const { withContentlayer } = require('next-contentlayer')

/** @type {import('next').NextConfig} */
const nextConfig = {
  pageExtensions: ['js', 'jsx', 'ts', 'tsx', 'md', 'mdx'],
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Provide process polyfill for client-side
      config.resolve.fallback = {
        ...config.resolve.fallback,
        process: require.resolve('process/browser'),
      }
    }
    return config
  },
  env: {
    // Make process.env available in client-side
    NODE_ENV: process.env.NODE_ENV,
  },
}

module.exports = withContentlayer(nextConfig)
