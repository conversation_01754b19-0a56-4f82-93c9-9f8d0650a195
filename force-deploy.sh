#!/bin/bash
echo "🚀 FORCE DEPLOYMENT: Image Pillar to Vercel"
echo "==========================================="

cd "/Users/<USER>/Desktop/Webiste Tool"

echo "📋 Current git status:"
git status --porcelain

echo ""
echo "📤 Force pushing to GitHub..."
git add .
git commit -m "🔧 FORCE DEPLOY: Ensure image pillar system is live

- Force commit to trigger Vercel deployment
- All image pillar files should be included
- Homepage, quiz, and recommendations ready"

git push origin main --force

echo ""
echo "⚡ Attempting Vercel CLI deployment..."
# Check if vercel CLI is available
if command -v vercel &> /dev/null; then
    echo "✅ Vercel CLI found, deploying..."
    vercel --prod --force
else
    echo "❌ Vercel CLI not found. Manual deployment required."
    echo ""
    echo "🎯 MANUAL STEPS:"
    echo "1. Go to https://vercel.com/dashboard"
    echo "2. Find your 'ai-tool' project"
    echo "3. Click 'Deployments' tab"
    echo "4. Click 'Redeploy' on latest deployment"
    echo "5. Wait 2-3 minutes for completion"
fi

echo ""
echo "🔍 VERIFICATION URLS:"
echo "- Check GitHub: https://github.com/PudgyPengu/ai-tool"
echo "- Check Vercel: https://vercel.com/dashboard"
echo "- Test Homepage: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/"
echo "- Test Image Pillar: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/best-ai-image-generators-2025-midjourney-vs-stable-diffusion-ideogram-dalle"

echo ""
echo "🎉 Force deployment initiated!"
