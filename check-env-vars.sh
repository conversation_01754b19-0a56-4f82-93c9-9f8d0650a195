#!/bin/bash
echo "🔍 CHECKING: Environment Variables & Vercel Sync"
echo "==============================================="

cd "/Users/<USER>/Desktop/Webiste Tool"

echo "📋 Checking for .env files:"
echo "✅ .env.local:" $([ -f ".env.local" ] && echo "EXISTS" || echo "MISSING")
echo "✅ .env:" $([ -f ".env" ] && echo "EXISTS" || echo "MISSING")
echo "✅ .env.example:" $([ -f ".env.example" ] && echo "EXISTS" || echo "MISSING")

echo ""
echo "🔧 Vercel Environment Variable Sync Commands:"
echo "1. Pull Vercel env vars to local:"
echo "   vercel env pull .env.local"
echo ""
echo "2. Push local env vars to Vercel:"
echo "   vercel env add"
echo ""
echo "3. List Vercel env vars:"
echo "   vercel env ls"

echo ""
echo "🎯 Common Issues & Solutions:"
echo "- If .env.local exists locally but not on Vercel → Upload via Vercel dashboard"
echo "- If Vercel has env vars but local doesn't → Run 'vercel env pull'"
echo "- If build fails due to missing vars → Check Vercel project settings"

echo ""
echo "📱 Manual Vercel Dashboard Steps:"
echo "1. Go to https://vercel.com/dashboard"
echo "2. Click your 'ai-tool' project"
echo "3. Go to Settings → Environment Variables"
echo "4. Add any missing variables"
echo "5. Redeploy from Deployments tab"

echo ""
echo "🚀 Force Redeploy Command:"
echo "vercel --prod --force"
