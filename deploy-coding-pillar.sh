#!/bin/bash
echo "🚀 Deploying AI Coding Pillar to GitHub and Vercel..."

# Navigate to project directory
cd "/Users/<USER>/Desktop/Webiste Tool"

# Add all new files
echo "📁 Adding files..."
git add content/pillars/best-ai-for-coding.mdx
git add app/quiz/coding/page.tsx
git add app/quiz/coding/quizConfig.ts
git add app/recommend/[model]/page.tsx

# Show what we're committing
echo "📋 Files to commit:"
git status --porcelain

# Commit with detailed message
git commit -m "🎯 DEPLOY: Complete AI Coding Pillar & Quiz System

NEW PILLAR: Best AI for Coding & Debugging (2025)
✅ Comprehensive coding AI comparison guide
✅ Developer-focused content with code examples
✅ Enterprise checklist and security considerations
✅ Deep-dive profiles for each coding AI tool

NEW QUIZ SYSTEM: Coding AI Recommender
✅ 5 strategic questions for developers
✅ Smart scoring for GPT-4o, <PERSON>, Copilot, Replit
✅ Override rules for enterprise/complexity needs
✅ Beautiful progressive UI with coding theme

ENHANCED RECOMMENDATIONS:
✅ Added GitHub Copilot configuration
✅ Added Replit Ghostwriter configuration  
✅ Updated color schemes and CTAs
✅ Dual navigation to both pillar guides

FILES ADDED:
- content/pillars/best-ai-for-coding.mdx
- app/quiz/coding/page.tsx
- app/quiz/coding/quizConfig.ts

FILES UPDATED:
- app/recommend/[model]/page.tsx

URLS CREATED:
- /best-ai-for-coding-2025-gpt-4o-vs-claude-github-copilot-more
- /quiz/coding
- /recommend/copilot
- /recommend/replit

Timestamp: $(date)"

# Push to GitHub
echo "📤 Pushing to GitHub..."
git push origin main

echo "✅ Deployment complete!"
echo "🔗 Check Vercel dashboard for automatic rebuild"
echo "🎯 Test URLs:"
echo "   - Coding Pillar: /best-ai-for-coding-2025-gpt-4o-vs-claude-github-copilot-more"
echo "   - Coding Quiz: /quiz/coding"
echo "   - Recommendations: /recommend/copilot, /recommend/replit"
