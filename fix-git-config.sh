#!/bin/bash
echo "🔧 FIXING: Git User Configuration for Vercel Deployment"
echo "======================================================"

cd "/Users/<USER>/Desktop/Webiste Tool"

echo "📋 Current Git Configuration:"
echo "Global user.name: $(git config --global user.name)"
echo "Global user.email: $(git config --global user.email)"
echo ""
echo "Local user.name: $(git config user.name)"
echo "Local user.email: $(git config user.email)"

echo ""
echo "🔍 GitHub Repository Information:"
git remote -v

echo ""
echo "📊 Recent Commits (to check author):"
git log --oneline --format="%h %an <%ae> %s" -3

echo ""
echo "🎯 RECOMMENDED ACTIONS:"
echo ""
echo "1. Set Git config to match your GitHub/Vercel account:"
echo "   git config --global user.name \"Your GitHub Username\""
echo "   git config --global user.email \"<EMAIL>\""
echo ""
echo "2. Or set locally for this project only:"
echo "   git config user.name \"Your GitHub Username\""
echo "   git config user.email \"<EMAIL>\""
echo ""
echo "3. After fixing config, recommit and push:"
echo "   git commit --amend --reset-author"
echo "   git push origin main --force"

echo ""
echo "🔗 VERCEL ACCOUNT CHECK:"
echo "1. Go to https://vercel.com/account"
echo "2. Check 'Connected Git Accounts' section"
echo "3. Ensure the email matches your Git config"
echo "4. If using GitHub, verify it's the same account as github.com/PudgyPengu"

echo ""
echo "⚡ QUICK FIX COMMANDS:"
echo "# Replace with your actual GitHub details:"
echo "git config --global user.name \"PudgyPengu\""
echo "git config --global user.email \"<EMAIL>\""
echo "git commit --amend --reset-author"
echo "git push origin main --force"

echo ""
echo "🎯 This should trigger Vercel deployment properly!"
