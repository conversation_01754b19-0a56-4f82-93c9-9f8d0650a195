#!/bin/bash
echo "🔧 Fixing pillar content rendering..."

cd "/Users/<USER>/Desktop/Webiste Tool"

# Add the fixed file
git add app/[slug]/PillarClient.tsx

# Commit the fix
git commit -m "🔧 FIX: Pillar content rendering

CRITICAL FIX: Replace hardcoded content with dynamic MDX rendering
✅ Remove hardcoded writing content from PillarClient.tsx
✅ Use pillar.body.html to render actual MDX content
✅ Add dynamic cluster badges (Writing/Coding)
✅ Ensure both writing and coding pillars show correct content

BEFORE: All pillars showed hardcoded writing content
AFTER: Each pillar shows its own MDX content dynamically

This fixes the issue where the coding pillar was showing writing content."

# Push to GitHub
git push origin main

echo "✅ Fix deployed!"
echo "🎯 Both pillar pages should now show their correct content"
