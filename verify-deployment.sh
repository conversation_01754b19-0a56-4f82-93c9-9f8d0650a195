#!/bin/bash
echo "🔍 VERIFYING: Image Pillar Deployment Status"
echo "============================================"

cd "/Users/<USER>/Desktop/Webiste Tool"

echo "📋 Latest git commits:"
git log --oneline -3

echo ""
echo "📁 Checking if image pillar files exist:"
echo "✅ Image pillar MDX:" $([ -f "content/pillars/best-ai-image-generators.mdx" ] && echo "EXISTS" || echo "MISSING")
echo "✅ Image quiz page:" $([ -f "app/quiz/image/page.tsx" ] && echo "EXISTS" || echo "MISSING")
echo "✅ Image quiz config:" $([ -f "app/quiz/image/quizConfig.ts" ] && echo "EXISTS" || echo "MISSING")

echo ""
echo "🎯 Expected live URLs after Vercel deployment:"
echo "- Homepage: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/"
echo "- Image Pillar: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/best-ai-image-generators-2025-midjourney-vs-stable-diffusion-ideogram-dalle"
echo "- Image Quiz: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/quiz/image"
echo ""
echo "🎉 Deployment verification complete!"
