#!/bin/bash
echo "🚀 DEPLOYING: Enhanced Homepage to GitHub & Vercel"
echo "================================================"

# Navigate to project directory
cd "/Users/<USER>/Desktop/Webiste Tool"

# Add the enhanced homepage
echo "📁 Adding enhanced homepage..."
git add app/page.tsx

# Show what we're committing
echo "📋 Files staged for commit:"
git status --porcelain

# Create commit for homepage enhancement
echo "💾 Creating commit..."
git commit -m "🎨 ENHANCE: Professional Homepage with Pillar Cards & Quiz CTAs

✨ HOMEPAGE REDESIGN: Complete landing page transformation

NEW FEATURES:
🎯 Hero Section:
- Compelling headline: 'Find Your Perfect AI Tool'
- Clear value proposition with comprehensive comparisons
- Prominent quiz CTAs for both writing and coding

🎨 Enhanced Pillar Cards:
- Visual differentiation: Blue gradient for writing, green for coding
- Rich descriptions with full pillar titles
- Dual action buttons: 'Read Full Guide' + 'Take Quiz'
- Priority badges and cluster indicators
- Professional card design with hover effects

🔥 Bottom CTA Section:
- Final conversion opportunity
- Large quiz buttons for maximum visibility
- Clear 30-second value proposition

DESIGN IMPROVEMENTS:
✅ Professional gradient backgrounds
✅ Improved typography and spacing
✅ Mobile-responsive grid layout
✅ Enhanced hover states and transitions
✅ Clear visual hierarchy
✅ Conversion-optimized CTAs

USER EXPERIENCE:
- Multiple paths to quiz engagement
- Clear navigation to all content
- Professional, trustworthy design
- Fast loading with optimized components

HOMEPAGE FEATURES:
- Dynamic pillar display from Contentlayer
- Smart pillar type detection (writing/coding)
- Direct quiz access from multiple locations
- Professional landing page design
- Mobile-first responsive layout

This creates a complete, professional homepage that showcases both AI tool pillars and drives quiz engagement!"

# Push to GitHub (triggers Vercel deployment)
echo "📤 Pushing to GitHub..."
git push origin main

# Check if push was successful
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ SUCCESS! Enhanced homepage deployed to GitHub"
    echo "🔄 Vercel will automatically redeploy with new homepage"
    echo ""
    echo "🎯 LIVE URLS (once deployed):"
    echo "- Homepage: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/"
    echo "- Writing Pillar: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/best-ai-for-writing-2025-claude-3-5-vs-gpt-4o-gemini-more"
    echo "- Coding Pillar: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/best-ai-for-coding-2025-gpt-4o-vs-claude-github-copilot-more"
    echo "- Writing Quiz: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/quiz/writing"
    echo "- Coding Quiz: https://ai-tool-t0n8cu2ew-phils-projects-50f74296.vercel.app/quiz/coding"
    echo ""
    echo "🎉 COMPLETE AI TOOLS PLATFORM NOW LIVE!"
    echo "📊 Features: Professional homepage, 2 pillar guides, 2 quiz systems, 7 recommendation pages"
else
    echo "❌ Push failed. Check git status and try again."
    git status
fi
